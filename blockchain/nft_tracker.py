"""
NFT Transfer and Gaming Asset Tracking System
Comprehensive tracking of gaming NFTs, metadata, and marketplace activities
"""
import asyncio
import logging
import aiohttp
import json
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import base64

from web3 import Web3
from .multi_chain_client import multi_chain_manager, EVMChainClient
from .gaming_contracts import gaming_contract_manager, GamingContract, ContractType
from models.gaming import BlockchainData
from models.crud import create_blockchain_data, get_blockchain_data_by_filters
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class NFTActivityType(Enum):
    """Types of NFT activities"""
    MINT = "mint"
    TRANSFER = "transfer"
    BURN = "burn"
    MARKETPLACE_SALE = "marketplace_sale"
    MARKETPLACE_LIST = "marketplace_list"
    MARKETPLACE_DELIST = "marketplace_delist"
    STAKING = "staking"
    UNSTAKING = "unstaking"


@dataclass
class NFTMetadata:
    """NFT metadata structure"""
    name: str
    description: str
    image: str
    attributes: List[Dict[str, Any]] = field(default_factory=list)
    animation_url: Optional[str] = None
    external_url: Optional[str] = None
    background_color: Optional[str] = None
    youtube_url: Optional[str] = None
    raw_metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class GameAsset:
    """Gaming asset information"""
    token_id: str
    contract_address: str
    chain: str
    collection_name: str
    owner_address: str
    metadata: Optional[NFTMetadata] = None
    last_transfer_hash: Optional[str] = None
    last_transfer_timestamp: Optional[datetime] = None
    mint_hash: Optional[str] = None
    mint_timestamp: Optional[datetime] = None
    is_burned: bool = False
    current_price: Optional[float] = None
    last_sale_price: Optional[float] = None
    rarity_rank: Optional[int] = None
    game_specific_data: Dict[str, Any] = field(default_factory=dict)


class NFTTracker:
    """Tracks NFT transfers and gaming asset activities"""
    
    def __init__(self):
        self.tracked_collections: Set[str] = set()
        self.asset_cache: Dict[str, GameAsset] = {}
        self.metadata_cache: Dict[str, NFTMetadata] = {}
        self.marketplace_contracts: Dict[str, List[str]] = {}
        self.ipfs_gateways = [
            "https://ipfs.io/ipfs/",
            "https://gateway.pinata.cloud/ipfs/",
            "https://cloudflare-ipfs.com/ipfs/"
        ]
        self._setup_tracked_collections()
        self._setup_marketplace_contracts()
    
    def _setup_tracked_collections(self):
        """Setup collections to track"""
        # Get all gaming NFT contracts
        gaming_nfts = gaming_contract_manager.get_gaming_nfts()
        
        for contract in gaming_nfts:
            collection_id = f"{contract.chain}:{contract.address.lower()}"
            self.tracked_collections.add(collection_id)
            logger.info(f"Tracking NFT collection: {contract.name} on {contract.chain}")
    
    def _setup_marketplace_contracts(self):
        """Setup known marketplace contracts"""
        self.marketplace_contracts = {
            'ethereum': [
                '******************************************',  # OpenSea
                '******************************************',  # LooksRare
                '******************************************',  # X2Y2
            ],
            'polygon': [
                '******************************************',  # OpenSea Polygon
            ],
            'bsc': [
                '******************************************',  # NFTrade
            ]
        }
    
    async def track_nft_transfer(
        self,
        chain: str,
        contract_address: str,
        token_id: str,
        from_address: str,
        to_address: str,
        transaction_hash: str,
        block_number: int,
        block_timestamp: datetime
    ):
        """Track NFT transfer event"""
        try:
            asset_id = f"{chain}:{contract_address.lower()}:{token_id}"
            
            # Get or create asset
            asset = await self._get_or_create_asset(
                chain, contract_address, token_id, to_address
            )
            
            # Update asset ownership
            asset.owner_address = to_address
            asset.last_transfer_hash = transaction_hash
            asset.last_transfer_timestamp = block_timestamp
            
            # Check if this is a mint (from zero address)
            if from_address == "0x0000000000000000000000000000000000000000":
                asset.mint_hash = transaction_hash
                asset.mint_timestamp = block_timestamp
                activity_type = NFTActivityType.MINT
            # Check if this is a burn (to zero address)
            elif to_address == "0x0000000000000000000000000000000000000000":
                asset.is_burned = True
                activity_type = NFTActivityType.BURN
            else:
                activity_type = NFTActivityType.TRANSFER
            
            # Update cache
            self.asset_cache[asset_id] = asset
            
            # Store activity in database
            await self._store_nft_activity(
                asset, activity_type, transaction_hash, block_number, block_timestamp,
                from_address, to_address
            )
            
            # Fetch metadata if not cached
            if not asset.metadata:
                await self._fetch_and_cache_metadata(chain, contract_address, token_id)
            
            logger.info(f"Tracked NFT {activity_type.value}: {asset.collection_name} #{token_id} "
                       f"from {from_address} to {to_address}")
            
        except Exception as e:
            logger.error(f"Error tracking NFT transfer: {e}")
    
    async def _get_or_create_asset(
        self,
        chain: str,
        contract_address: str,
        token_id: str,
        owner_address: str
    ) -> GameAsset:
        """Get existing asset or create new one"""
        asset_id = f"{chain}:{contract_address.lower()}:{token_id}"
        
        # Check cache first
        if asset_id in self.asset_cache:
            return self.asset_cache[asset_id]
        
        # Check database
        existing_data = await get_blockchain_data_by_filters({
            'blockchain': chain,
            'contract_address': contract_address.lower(),
            'nft_token_id': token_id
        })
        
        if existing_data:
            # Reconstruct asset from database
            latest_data = existing_data[0]  # Assuming sorted by timestamp desc
            asset = GameAsset(
                token_id=token_id,
                contract_address=contract_address.lower(),
                chain=chain,
                collection_name=latest_data.nft_collection or "Unknown",
                owner_address=owner_address,
                last_transfer_hash=latest_data.transaction_hash,
                last_transfer_timestamp=latest_data.block_timestamp
            )
        else:
            # Create new asset
            contract = gaming_contract_manager.get_contract(chain, contract_address)
            collection_name = contract.name if contract else "Unknown Collection"
            
            asset = GameAsset(
                token_id=token_id,
                contract_address=contract_address.lower(),
                chain=chain,
                collection_name=collection_name,
                owner_address=owner_address
            )
        
        return asset
    
    async def _fetch_and_cache_metadata(
        self,
        chain: str,
        contract_address: str,
        token_id: str
    ):
        """Fetch and cache NFT metadata"""
        try:
            metadata_id = f"{chain}:{contract_address.lower()}:{token_id}"
            
            # Check cache first
            if metadata_id in self.metadata_cache:
                return self.metadata_cache[metadata_id]
            
            # Get client for chain
            client = multi_chain_manager.get_client(chain)
            if not isinstance(client, EVMChainClient):
                return None
            
            # Get contract
            contract = gaming_contract_manager.get_contract(chain, contract_address)
            if not contract:
                return None
            
            # Call tokenURI function
            token_uri = await client.call_contract_function(
                contract_address, contract.abi, "tokenURI", int(token_id)
            )
            
            if not token_uri:
                return None
            
            # Fetch metadata from URI
            metadata = await self._fetch_metadata_from_uri(token_uri)
            if metadata:
                self.metadata_cache[metadata_id] = metadata
                
                # Update asset in cache
                asset_id = f"{chain}:{contract_address.lower()}:{token_id}"
                if asset_id in self.asset_cache:
                    self.asset_cache[asset_id].metadata = metadata
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error fetching metadata for {chain}:{contract_address}:{token_id}: {e}")
            return None
    
    async def _fetch_metadata_from_uri(self, token_uri: str) -> Optional[NFTMetadata]:
        """Fetch metadata from token URI"""
        try:
            # Handle different URI schemes
            if token_uri.startswith('data:'):
                # Data URI
                if 'base64,' in token_uri:
                    encoded_data = token_uri.split('base64,')[1]
                    decoded_data = base64.b64decode(encoded_data)
                    metadata_json = json.loads(decoded_data)
                else:
                    # JSON data URI
                    json_data = token_uri.split('data:application/json,')[1]
                    metadata_json = json.loads(json_data)
            
            elif token_uri.startswith('ipfs://'):
                # IPFS URI
                ipfs_hash = token_uri.replace('ipfs://', '')
                metadata_json = await self._fetch_from_ipfs(ipfs_hash)
            
            elif token_uri.startswith('http'):
                # HTTP URI
                async with aiohttp.ClientSession() as session:
                    async with session.get(token_uri, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        if response.status == 200:
                            metadata_json = await response.json()
                        else:
                            return None
            else:
                logger.warning(f"Unsupported token URI scheme: {token_uri}")
                return None
            
            # Parse metadata
            if metadata_json:
                return NFTMetadata(
                    name=metadata_json.get('name', ''),
                    description=metadata_json.get('description', ''),
                    image=metadata_json.get('image', ''),
                    attributes=metadata_json.get('attributes', []),
                    animation_url=metadata_json.get('animation_url'),
                    external_url=metadata_json.get('external_url'),
                    background_color=metadata_json.get('background_color'),
                    youtube_url=metadata_json.get('youtube_url'),
                    raw_metadata=metadata_json
                )
            
        except Exception as e:
            logger.error(f"Error fetching metadata from URI {token_uri}: {e}")
        
        return None
    
    async def _fetch_from_ipfs(self, ipfs_hash: str) -> Optional[Dict]:
        """Fetch data from IPFS using multiple gateways"""
        for gateway in self.ipfs_gateways:
            try:
                url = f"{gateway}{ipfs_hash}"
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        if response.status == 200:
                            return await response.json()
            except Exception as e:
                logger.debug(f"Failed to fetch from IPFS gateway {gateway}: {e}")
                continue
        
        logger.warning(f"Failed to fetch IPFS data for hash: {ipfs_hash}")
        return None
    
    async def _store_nft_activity(
        self,
        asset: GameAsset,
        activity_type: NFTActivityType,
        transaction_hash: str,
        block_number: int,
        block_timestamp: datetime,
        from_address: str,
        to_address: str
    ):
        """Store NFT activity in database"""
        try:
            blockchain_data = {
                'blockchain': asset.chain,
                'block_number': block_number,
                'transaction_hash': transaction_hash,
                'contract_address': asset.contract_address,
                'event_type': activity_type.value,
                'from_address': from_address,
                'to_address': to_address,
                'block_timestamp': block_timestamp,
                'nft_token_id': asset.token_id,
                'nft_collection': asset.collection_name,
                'event_data': {
                    'activity_type': activity_type.value,
                    'collection_name': asset.collection_name,
                    'metadata': asset.metadata.__dict__ if asset.metadata else None
                }
            }
            
            await create_blockchain_data(blockchain_data)
            
        except Exception as e:
            logger.error(f"Error storing NFT activity: {e}")
    
    async def get_asset_history(
        self,
        chain: str,
        contract_address: str,
        token_id: str
    ) -> List[Dict[str, Any]]:
        """Get complete history of an NFT asset"""
        try:
            history_data = await get_blockchain_data_by_filters({
                'blockchain': chain,
                'contract_address': contract_address.lower(),
                'nft_token_id': token_id
            })
            
            history = []
            for data in history_data:
                history.append({
                    'activity_type': data.event_type,
                    'transaction_hash': data.transaction_hash,
                    'block_number': data.block_number,
                    'block_timestamp': data.block_timestamp,
                    'from_address': data.from_address,
                    'to_address': data.to_address,
                    'event_data': data.event_data
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Error getting asset history: {e}")
            return []
    
    async def get_collection_stats(self, chain: str, contract_address: str) -> Dict[str, Any]:
        """Get statistics for an NFT collection"""
        try:
            collection_data = await get_blockchain_data_by_filters({
                'blockchain': chain,
                'contract_address': contract_address.lower()
            })
            
            if not collection_data:
                return {}
            
            # Calculate stats
            total_transfers = len(collection_data)
            unique_tokens = len(set(data.nft_token_id for data in collection_data if data.nft_token_id))
            unique_owners = len(set(data.to_address for data in collection_data if data.to_address))
            
            mints = [data for data in collection_data if data.event_type == 'mint']
            burns = [data for data in collection_data if data.event_type == 'burn']
            
            stats = {
                'total_transfers': total_transfers,
                'unique_tokens': unique_tokens,
                'unique_owners': unique_owners,
                'total_mints': len(mints),
                'total_burns': len(burns),
                'first_activity': min(data.block_timestamp for data in collection_data) if collection_data else None,
                'last_activity': max(data.block_timestamp for data in collection_data) if collection_data else None
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {}
    
    def get_tracked_collections(self) -> List[str]:
        """Get list of tracked collections"""
        return list(self.tracked_collections)
    
    def add_collection_to_track(self, chain: str, contract_address: str):
        """Add collection to tracking list"""
        collection_id = f"{chain}:{contract_address.lower()}"
        self.tracked_collections.add(collection_id)
        logger.info(f"Added collection to tracking: {collection_id}")
    
    def remove_collection_from_track(self, chain: str, contract_address: str):
        """Remove collection from tracking list"""
        collection_id = f"{chain}:{contract_address.lower()}"
        self.tracked_collections.discard(collection_id)
        logger.info(f"Removed collection from tracking: {collection_id}")


# Global NFT tracker instance
nft_tracker = NFTTracker()
