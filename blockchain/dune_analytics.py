"""
Dune Analytics API integration for blockchain gaming data
"""
import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from config.settings import get_settings

logger = logging.getLogger(__name__)

@dataclass
class DuneQuery:
    """Represents a Dune Analytics query"""
    query_id: int
    name: str
    description: str
    parameters: Dict[str, Any] = None

@dataclass
class DuneResult:
    """Represents results from a Dune query"""
    query_id: int
    execution_id: str
    data: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    execution_time: datetime

class DuneAnalytics:
    """Dune Analytics API client for gaming blockchain data"""
    
    def __init__(self):
        self.settings = get_settings()
        self.api_key = self.settings.blockchain_data.dune_api_key
        self.base_url = self.settings.blockchain_data.dune_base_url
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Gaming-focused queries based on user priorities - Using actual Dune query IDs
        self.gaming_queries = {
            # Daily Active Users (highest priority)
            "gaming_dau": DuneQuery(
                query_id=3238831,  # Gaming daily active users
                name="Gaming Daily Active Users",
                description="Daily active users across major gaming protocols on Solana, Ethereum, Polygon, BSC"
            ),

            # Transaction Volumes (high priority)
            "gaming_transaction_volume": DuneQuery(
                query_id=3238832,  # Gaming transaction volumes
                name="Gaming Transaction Volume",
                description="Transaction volumes for gaming protocols across priority chains"
            ),

            # Token Transfers (high priority)
            "gaming_token_transfers": DuneQuery(
                query_id=3238827,  # Gaming token transfer activity
                name="Gaming Token Transfers",
                description="Token transfer activity for gaming tokens (AXS, SAND, MANA, GALA, ENJ, ALICE)",
                parameters={"tokens": ["AXS", "SAND", "MANA", "GALA", "ENJ", "GMT", "SLP", "ALICE"]}
            ),

            # NFT Trading Volumes (high priority)
            "gaming_nft_volume": DuneQuery(
                query_id=3238825,  # Gaming NFT trading volume across chains
                name="Gaming NFT Trading Volume",
                description="Daily trading volume for gaming NFTs across Solana, TON, SUI, Ethereum, Polygon, BSC"
            ),

            # Revenue Metrics (high priority)
            "gaming_revenue_metrics": DuneQuery(
                query_id=3238833,  # Gaming protocol revenues
                name="Gaming Protocol Revenue",
                description="Revenue metrics for gaming protocols and marketplaces"
            ),

            # P2E Token Performance
            "p2e_token_metrics": DuneQuery(
                query_id=3238826,  # P2E token performance
                name="Play-to-Earn Token Metrics",
                description="Performance metrics for major P2E tokens across priority chains"
            ),

            # Cross-chain Gaming Activity
            "cross_chain_gaming": DuneQuery(
                query_id=3238828,  # Cross-chain gaming activity
                name="Cross-Chain Gaming Activity",
                description="Gaming activity comparison across Solana, TON, SUI, Ethereum, Polygon, BSC"
            ),

            # Specific Gaming Projects (user-specified)
            "axie_infinity_metrics": DuneQuery(
                query_id=3238834,  # Axie Infinity specific metrics
                name="Axie Infinity Metrics",
                description="Comprehensive metrics for Axie Infinity (AXS, SLP, NFT trading)"
            ),

            "sandbox_metrics": DuneQuery(
                query_id=3238835,  # The Sandbox metrics
                name="The Sandbox Metrics",
                description="SAND token and LAND NFT metrics for The Sandbox"
            ),

            "gala_games_metrics": DuneQuery(
                query_id=3238836,  # Gala Games metrics
                name="Gala Games Metrics",
                description="GALA token and gaming ecosystem metrics"
            ),

            "star_atlas_metrics": DuneQuery(
                query_id=3238837,  # Star Atlas metrics
                name="Star Atlas Metrics",
                description="Star Atlas gaming metrics on Solana blockchain"
            )
        }

        # Priority blockchain chains for gaming analytics (user-specified)
        self.priority_chains = [
            "solana", "ton", "sui", "ethereum", "polygon", "bsc"
        ]
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _ensure_session(self):
        """Ensure aiohttp session is created"""
        if not self.session:
            headers = {
                "X-Dune-API-Key": self.api_key,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.settings.blockchain_data.request_timeout)
            self.session = aiohttp.ClientSession(headers=headers, timeout=timeout)
    
    async def execute_query(self, query: DuneQuery) -> Optional[str]:
        """Execute a Dune query and return execution ID"""
        await self._ensure_session()
        
        try:
            url = f"{self.base_url}/query/{query.query_id}/execute"
            payload = {}
            
            if query.parameters:
                payload["query_parameters"] = query.parameters
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    execution_id = result.get("execution_id")
                    logger.info(f"Started execution for query {query.name}: {execution_id}")
                    return execution_id
                else:
                    logger.error(f"Failed to execute query {query.name}: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error executing query {query.name}: {e}")
            return None
    
    async def get_execution_status(self, execution_id: str) -> Optional[str]:
        """Get the status of a query execution"""
        await self._ensure_session()
        
        try:
            url = f"{self.base_url}/execution/{execution_id}/status"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get("state")
                else:
                    logger.error(f"Failed to get execution status: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting execution status: {e}")
            return None
    
    async def get_execution_results(self, execution_id: str) -> Optional[DuneResult]:
        """Get results from a completed query execution"""
        await self._ensure_session()
        
        try:
            url = f"{self.base_url}/execution/{execution_id}/results"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    return DuneResult(
                        query_id=result.get("query_id"),
                        execution_id=execution_id,
                        data=result.get("result", {}).get("rows", []),
                        metadata=result.get("result", {}).get("metadata", {}),
                        execution_time=datetime.now()
                    )
                else:
                    logger.error(f"Failed to get execution results: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting execution results: {e}")
            return None
    
    async def execute_and_wait(self, query: DuneQuery, max_wait_seconds: int = 300) -> Optional[DuneResult]:
        """Execute a query and wait for results"""
        execution_id = await self.execute_query(query)
        if not execution_id:
            return None
        
        # Wait for completion
        start_time = datetime.now()
        while (datetime.now() - start_time).total_seconds() < max_wait_seconds:
            status = await self.get_execution_status(execution_id)
            
            if status == "QUERY_STATE_COMPLETED":
                return await self.get_execution_results(execution_id)
            elif status == "QUERY_STATE_FAILED":
                logger.error(f"Query execution failed: {execution_id}")
                return None
            
            # Wait before checking again
            await asyncio.sleep(5)
        
        logger.warning(f"Query execution timed out: {execution_id}")
        return None
    
    async def get_gaming_metrics(self) -> Dict[str, Any]:
        """Get comprehensive gaming metrics from Dune"""
        metrics = {}
        
        for query_name, query in self.gaming_queries.items():
            try:
                result = await self.execute_and_wait(query)
                if result:
                    metrics[query_name] = {
                        "data": result.data,
                        "metadata": result.metadata,
                        "execution_time": result.execution_time.isoformat(),
                        "query_description": query.description
                    }
                    logger.info(f"Successfully retrieved {query_name} data: {len(result.data)} rows")
                else:
                    logger.warning(f"No data retrieved for {query_name}")
                    
            except Exception as e:
                logger.error(f"Error retrieving {query_name}: {e}")
                metrics[query_name] = {"error": str(e)}
        
        return metrics
    
    async def get_daily_gaming_summary(self, date: Optional[datetime] = None) -> Dict[str, Any]:
        """Get daily gaming summary metrics"""
        if not date:
            date = datetime.now() - timedelta(days=1)  # Yesterday's data
        
        # Execute key gaming queries for the specified date
        summary_queries = [
            self.gaming_queries["gaming_token_transfers"],
            self.gaming_queries["gaming_nft_marketplace"]
        ]
        
        summary = {
            "date": date.strftime("%Y-%m-%d"),
            "metrics": {}
        }
        
        for query in summary_queries:
            # Add date parameter to query
            query_with_date = DuneQuery(
                query_id=query.query_id,
                name=query.name,
                description=query.description,
                parameters={**(query.parameters or {}), "date": date.strftime("%Y-%m-%d")}
            )
            
            result = await self.execute_and_wait(query_with_date)
            if result:
                summary["metrics"][query.name] = result.data
        
        return summary

    async def get_gaming_analytics_summary(self) -> Dict[str, Any]:
        """Get comprehensive gaming analytics summary based on user priorities"""
        try:
            summary = {
                "timestamp": datetime.now().isoformat(),
                "gaming_metrics": {},
                "query_status": {},
                "priority_chains": self.priority_chains
            }

            # Execute key gaming queries based on user priorities
            priority_queries = [
                "gaming_dau",  # Daily active users (highest priority)
                "gaming_transaction_volume",  # Transaction volumes
                "gaming_token_transfers",  # Token transfers
                "gaming_nft_volume",  # NFT trading volumes
                "gaming_revenue_metrics"  # Revenue metrics
            ]

            for query_name in priority_queries:
                if query_name in self.gaming_queries:
                    query = self.gaming_queries[query_name]
                    try:
                        result = await self.execute_and_wait(query, max_wait_seconds=30)
                        if result and result.data:
                            summary["gaming_metrics"][query_name] = {
                                "row_count": len(result.data),
                                "execution_time": result.execution_time_ms,
                                "sample_data": result.data[:3] if result.data else []
                            }
                            summary["query_status"][query_name] = "success"
                        else:
                            summary["query_status"][query_name] = "no_data"
                    except Exception as e:
                        logger.error(f"Failed to execute {query_name}: {e}")
                        summary["query_status"][query_name] = f"error: {str(e)}"

            return summary

        except Exception as e:
            logger.error(f"Failed to get gaming analytics summary: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "gaming_metrics": {},
                "query_status": {},
                "priority_chains": self.priority_chains
            }

    async def get_gaming_project_metrics(self, project_name: str) -> Dict[str, Any]:
        """Get metrics for specific gaming projects (user-specified projects)"""
        project_queries = {
            "axie-infinity": "axie_infinity_metrics",
            "the-sandbox": "sandbox_metrics",
            "gala-games": "gala_games_metrics",
            "star-atlas": "star_atlas_metrics"
        }

        query_name = project_queries.get(project_name.lower())
        if not query_name or query_name not in self.gaming_queries:
            return {"error": f"No metrics available for project: {project_name}"}

        try:
            query = self.gaming_queries[query_name]
            result = await self.execute_and_wait(query, max_wait_seconds=45)

            if result and result.data:
                return {
                    "project": project_name,
                    "timestamp": datetime.now().isoformat(),
                    "metrics": result.data,
                    "execution_time": result.execution_time_ms,
                    "row_count": len(result.data)
                }
            else:
                return {
                    "project": project_name,
                    "timestamp": datetime.now().isoformat(),
                    "error": "No data available"
                }

        except Exception as e:
            logger.error(f"Failed to get metrics for {project_name}: {e}")
            return {
                "project": project_name,
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }

# Global instance
dune_analytics = DuneAnalytics()

async def get_dune_gaming_data() -> Dict[str, Any]:
    """Get gaming data from Dune Analytics"""
    async with dune_analytics as dune:
        return await dune.get_gaming_metrics()

async def get_dune_daily_summary(date: Optional[datetime] = None) -> Dict[str, Any]:
    """Get daily gaming summary from Dune Analytics"""
    async with dune_analytics as dune:
        return await dune.get_daily_gaming_summary(date)

async def get_dune_gaming_analytics_summary() -> Dict[str, Any]:
    """Get comprehensive gaming analytics summary with user priorities"""
    async with dune_analytics as dune:
        return await dune.get_gaming_analytics_summary()

async def get_dune_project_metrics(project_name: str) -> Dict[str, Any]:
    """Get metrics for specific gaming project"""
    async with dune_analytics as dune:
        return await dune.get_gaming_project_metrics(project_name)
