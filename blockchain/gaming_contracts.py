"""
Gaming Contract ABI Management System
Manages ABIs and contract interactions for popular gaming protocols
"""
import json
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import sys
import os

# Add the config directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.gaming_config import gaming_project_manager, GamingProjectConfig, TokenInfo, NFTInfo

logger = logging.getLogger(__name__)


class ContractType(Enum):
    """Types of gaming contracts"""
    ERC721_NFT = "erc721_nft"
    ERC1155_NFT = "erc1155_nft"
    GAMING_TOKEN = "gaming_token"
    MARKETPLACE = "marketplace"
    STAKING = "staking"
    P2E_GAME = "p2e_game"
    METAVERSE = "metaverse"
    GUILD = "guild"
    BRIDGE = "bridge"


@dataclass
class GamingContract:
    """Gaming contract information"""
    name: str
    address: str
    chain: str
    contract_type: ContractType
    abi: List[Dict]
    description: str
    project_website: str = ""
    token_symbol: str = ""
    is_verified: bool = True
    deployment_block: Optional[int] = None


class GamingContractManager:
    """Manages gaming contract ABIs and interactions"""

    def __init__(self):
        self.contracts: Dict[str, GamingContract] = {}
        self.contracts_by_chain: Dict[str, List[str]] = {}
        self.contracts_by_type: Dict[ContractType, List[str]] = {}
        self._load_gaming_contracts()

    def _load_gaming_contracts(self):
        """Load gaming contract definitions from CSV data"""
        logger.info("🔄 Loading gaming contracts from CSV data...")

        # Get all enabled projects from CSV
        enabled_projects = gaming_project_manager.get_enabled_projects()

        gaming_contracts = []

        for project_key, project in enabled_projects.items():
            # Add token contracts
            for token in project.tokens:
                if token.contract_address:
                    contract = self._create_token_contract(project, token)
                    if contract:
                        gaming_contracts.append(contract)

            # Add NFT contracts
            for nft in project.nfts:
                if nft.contract_address:
                    contract = self._create_nft_contract(project, nft)
                    if contract:
                        gaming_contracts.append(contract)

        # Add fallback contracts if CSV is empty or missing
        if not gaming_contracts:
            logger.warning("⚠️ No contracts found in CSV, loading fallback contracts")
            gaming_contracts = self._get_fallback_contracts()

        # Register all contracts
        for contract in gaming_contracts:
            self._register_contract(contract)

        logger.info(f"✅ Loaded {len(gaming_contracts)} gaming contracts from CSV data")

    def _create_token_contract(self, project: GamingProjectConfig, token: TokenInfo) -> Optional[GamingContract]:
        """Create a GamingContract from project and token info"""
        try:
            # Normalize blockchain name
            chain = self._normalize_blockchain_name(project.blockchain)
            if not chain:
                logger.warning(f"⚠️ Unknown blockchain for {project.project_name}: {project.blockchain}")
                return None

            # Determine contract type based on token type
            contract_type = ContractType.GAMING_TOKEN
            if 'governance' in token.token_type.lower():
                contract_type = ContractType.GAMING_TOKEN
            elif 'utility' in token.token_type.lower():
                contract_type = ContractType.GAMING_TOKEN

            return GamingContract(
                name=f"{project.project_name} ({token.symbol})",
                address=token.contract_address,
                chain=chain,
                contract_type=contract_type,
                abi=self._get_erc20_abi(),
                description=f"{token.token_type} token for {project.project_name}",
                project_website=project.website,
                token_symbol=token.symbol,
                is_verified=True
            )
        except Exception as e:
            logger.error(f"❌ Failed to create token contract for {project.project_name}: {e}")
            return None

    def _create_nft_contract(self, project: GamingProjectConfig, nft: NFTInfo) -> Optional[GamingContract]:
        """Create a GamingContract from project and NFT info"""
        try:
            # Normalize blockchain name
            chain = self._normalize_blockchain_name(project.blockchain)
            if not chain:
                logger.warning(f"⚠️ Unknown blockchain for {project.project_name}: {project.blockchain}")
                return None

            # Use ERC721 as default NFT type
            contract_type = ContractType.ERC721_NFT

            nft_name = nft.name if nft.name else f"{project.project_name} NFT"
            description = f"NFT collection for {project.project_name}"
            if nft.function:
                description += f" - {nft.function}"

            return GamingContract(
                name=nft_name,
                address=nft.contract_address,
                chain=chain,
                contract_type=contract_type,
                abi=self._get_erc721_abi(),
                description=description,
                project_website=project.website,
                is_verified=True
            )
        except Exception as e:
            logger.error(f"❌ Failed to create NFT contract for {project.project_name}: {e}")
            return None

    def _normalize_blockchain_name(self, blockchain: str) -> Optional[str]:
        """Normalize blockchain names to standard format"""
        if not blockchain:
            return None

        blockchain = blockchain.lower().strip()

        # Mapping of various blockchain name formats to standard names
        blockchain_mapping = {
            'ethereum': 'ethereum',
            'eth': 'ethereum',
            'mainnet': 'ethereum',
            'solana': 'solana',
            'sol': 'solana',
            'polygon': 'polygon',
            'matic': 'polygon',
            'binance smart chain': 'bsc',
            'bsc': 'bsc',
            'bnb': 'bsc',
            'avalanche': 'avalanche',
            'avax': 'avalanche',
            'ronin': 'ronin',
            'base': 'base',
            'arbitrum': 'arbitrum',
            'optimism': 'optimism',
            'ton': 'ton',
            'sui': 'sui'
        }

        # Handle comma-separated blockchains (take the first one)
        if ',' in blockchain:
            blockchain = blockchain.split(',')[0].strip()

        return blockchain_mapping.get(blockchain, blockchain)

    def _get_fallback_contracts(self) -> List[GamingContract]:
        """Get minimal fallback contracts when CSV data is not available"""
        logger.warning("⚠️ Using minimal fallback contracts - CSV data should be loaded instead")
        return [
            # Minimal Axie Infinity fallback
            GamingContract(
                name="Axie Infinity Shards (AXS)",
                address="******************************************",
                chain="ethereum",
                contract_type=ContractType.GAMING_TOKEN,
                abi=self._get_erc20_abi(),
                description="Governance token for Axie Infinity",
                project_website="https://axieinfinity.com",
                token_symbol="AXS",
                deployment_block=11314878
            )
        ]

    def _register_contract(self, contract: GamingContract):
        """Register a gaming contract"""
        contract_id = f"{contract.chain}:{contract.address.lower()}"
        self.contracts[contract_id] = contract
        
        # Index by chain
        if contract.chain not in self.contracts_by_chain:
            self.contracts_by_chain[contract.chain] = []
        self.contracts_by_chain[contract.chain].append(contract_id)
        
        # Index by type
        if contract.contract_type not in self.contracts_by_type:
            self.contracts_by_type[contract.contract_type] = []
        self.contracts_by_type[contract.contract_type].append(contract_id)
        
        logger.info(f"Registered gaming contract: {contract.name} on {contract.chain}")

    def register_contract(self, contract: GamingContract):
        """Public method to register a gaming contract"""
        self._register_contract(contract)

    def reload_contracts_from_csv(self):
        """Reload contracts from CSV data"""
        logger.info("🔄 Reloading gaming contracts from CSV...")
        self.contracts.clear()
        self.contracts_by_chain.clear()
        self.contracts_by_type.clear()
        self._load_gaming_contracts()

    def get_contract(self, chain: str, address: str) -> Optional[GamingContract]:
        """Get contract by chain and address"""
        contract_id = f"{chain}:{address.lower()}"
        return self.contracts.get(contract_id)
    
    def get_contracts_by_chain(self, chain: str) -> List[GamingContract]:
        """Get all contracts for a specific chain"""
        contract_ids = self.contracts_by_chain.get(chain, [])
        return [self.contracts[contract_id] for contract_id in contract_ids]
    
    def get_contracts_by_type(self, contract_type: ContractType) -> List[GamingContract]:
        """Get all contracts of a specific type"""
        contract_ids = self.contracts_by_type.get(contract_type, [])
        return [self.contracts[contract_id] for contract_id in contract_ids]
    
    def get_gaming_tokens(self) -> List[GamingContract]:
        """Get all gaming token contracts"""
        return self.get_contracts_by_type(ContractType.GAMING_TOKEN)
    
    def get_gaming_nfts(self) -> List[GamingContract]:
        """Get all gaming NFT contracts"""
        erc721_nfts = self.get_contracts_by_type(ContractType.ERC721_NFT)
        erc1155_nfts = self.get_contracts_by_type(ContractType.ERC1155_NFT)
        return erc721_nfts + erc1155_nfts
    
    def get_p2e_contracts(self) -> List[GamingContract]:
        """Get all Play-to-Earn game contracts"""
        return self.get_contracts_by_type(ContractType.P2E_GAME)
    
    def get_metaverse_contracts(self) -> List[GamingContract]:
        """Get all metaverse-related contracts"""
        return self.get_contracts_by_type(ContractType.METAVERSE)

    def get_all_contracts(self) -> List[GamingContract]:
        """Get all registered contracts"""
        return list(self.contracts.values())

    def get_contract_addresses_by_chain(self, chain: str) -> List[str]:
        """Get all contract addresses for a specific chain"""
        contracts = self.get_contracts_by_chain(chain)
        return [contract.address for contract in contracts]

    def get_token_symbols(self) -> List[str]:
        """Get all unique token symbols"""
        symbols = set()
        for contract in self.get_gaming_tokens():
            if contract.token_symbol:
                symbols.add(contract.token_symbol)
        return list(symbols)

    def get_contracts_by_project_website(self, website: str) -> List[GamingContract]:
        """Get all contracts for a specific project website"""
        return [contract for contract in self.contracts.values()
                if contract.project_website == website]

    def get_supported_chains(self) -> List[str]:
        """Get all supported blockchain chains"""
        return list(self.contracts_by_chain.keys())

    def get_contract_summary(self) -> Dict[str, Any]:
        """Get a summary of all contracts"""
        total_contracts = len(self.contracts)
        contracts_by_chain = {chain: len(contracts) for chain, contracts in self.contracts_by_chain.items()}
        contracts_by_type = {contract_type.value: len(contracts)
                           for contract_type, contracts in self.contracts_by_type.items()}

        return {
            'total_contracts': total_contracts,
            'contracts_by_chain': contracts_by_chain,
            'contracts_by_type': contracts_by_type,
            'supported_chains': self.get_supported_chains(),
            'token_symbols': self.get_token_symbols(),
            'gaming_tokens': len(self.get_gaming_tokens()),
            'gaming_nfts': len(self.get_gaming_nfts())
        }
    
    def _get_erc20_abi(self) -> List[Dict]:
        """Get standard ERC20 ABI"""
        return [
            {
                "constant": True,
                "inputs": [],
                "name": "name",
                "outputs": [{"name": "", "type": "string"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "symbol",
                "outputs": [{"name": "", "type": "string"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "totalSupply",
                "outputs": [{"name": "", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [{"name": "_owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": False,
                "inputs": [
                    {"name": "_to", "type": "address"},
                    {"name": "_value", "type": "uint256"}
                ],
                "name": "transfer",
                "outputs": [{"name": "", "type": "bool"}],
                "type": "function"
            },
            {
                "anonymous": False,
                "inputs": [
                    {"indexed": True, "name": "from", "type": "address"},
                    {"indexed": True, "name": "to", "type": "address"},
                    {"indexed": False, "name": "value", "type": "uint256"}
                ],
                "name": "Transfer",
                "type": "event"
            }
        ]
    
    def _get_erc721_abi(self) -> List[Dict]:
        """Get standard ERC721 ABI"""
        return [
            {
                "constant": True,
                "inputs": [],
                "name": "name",
                "outputs": [{"name": "", "type": "string"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "symbol",
                "outputs": [{"name": "", "type": "string"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [{"name": "_tokenId", "type": "uint256"}],
                "name": "ownerOf",
                "outputs": [{"name": "", "type": "address"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [{"name": "_owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [{"name": "_tokenId", "type": "uint256"}],
                "name": "tokenURI",
                "outputs": [{"name": "", "type": "string"}],
                "type": "function"
            },
            {
                "anonymous": False,
                "inputs": [
                    {"indexed": True, "name": "from", "type": "address"},
                    {"indexed": True, "name": "to", "type": "address"},
                    {"indexed": True, "name": "tokenId", "type": "uint256"}
                ],
                "name": "Transfer",
                "type": "event"
            }
        ]
    
    def save_contracts_to_file(self, filepath: str):
        """Save contract definitions to JSON file"""
        contracts_data = {}
        
        for contract_id, contract in self.contracts.items():
            contracts_data[contract_id] = {
                "name": contract.name,
                "address": contract.address,
                "chain": contract.chain,
                "contract_type": contract.contract_type.value,
                "abi": contract.abi,
                "description": contract.description,
                "project_website": contract.project_website,
                "token_symbol": contract.token_symbol,
                "is_verified": contract.is_verified,
                "deployment_block": contract.deployment_block
            }
        
        with open(filepath, 'w') as f:
            json.dump(contracts_data, f, indent=2)
        
        logger.info(f"Saved {len(contracts_data)} gaming contracts to {filepath}")
    
    def load_contracts_from_file(self, filepath: str):
        """Load contract definitions from JSON file"""
        try:
            with open(filepath, 'r') as f:
                contracts_data = json.load(f)
            
            for contract_id, data in contracts_data.items():
                contract = GamingContract(
                    name=data["name"],
                    address=data["address"],
                    chain=data["chain"],
                    contract_type=ContractType(data["contract_type"]),
                    abi=data["abi"],
                    description=data["description"],
                    project_website=data.get("project_website", ""),
                    token_symbol=data.get("token_symbol", ""),
                    is_verified=data.get("is_verified", True),
                    deployment_block=data.get("deployment_block")
                )
                self.register_contract(contract)
            
            logger.info(f"Loaded {len(contracts_data)} gaming contracts from {filepath}")
            
        except FileNotFoundError:
            logger.warning(f"Contract file {filepath} not found")
        except Exception as e:
            logger.error(f"Error loading contracts from {filepath}: {e}")


# Global gaming contract manager instance
gaming_contract_manager = GamingContractManager()
