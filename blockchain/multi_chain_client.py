"""
Multi-chain blockchain client supporting EVM and non-EVM chains
"""
import asyncio
import logging
import aiohttp
import json
from typing import Dict, Optional, Any, List, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime

from web3 import Web3
from web3.middleware import geth_poa_middleware
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class BlockchainInfo:
    """Blockchain network information"""
    name: str
    chain_id: Optional[int]
    native_token: str
    block_time: float  # Average block time in seconds
    is_evm: bool
    gaming_focus: bool = False


class BaseChainClient(ABC):
    """Base class for blockchain clients"""
    
    def __init__(self, rpc_url: str, chain_info: BlockchainInfo):
        self.rpc_url = rpc_url
        self.chain_info = chain_info
        self.is_connected = False
    
    @abstractmethod
    async def get_latest_block_number(self) -> Optional[int]:
        """Get the latest block number"""
        pass
    
    @abstractmethod
    async def get_block(self, block_number: int) -> Optional[Dict]:
        """Get block data"""
        pass
    
    @abstractmethod
    async def get_transaction(self, tx_hash: str) -> Optional[Dict]:
        """Get transaction data"""
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """Test if connection is working"""
        pass


class EVMChainClient(BaseChainClient):
    """Client for EVM-compatible chains"""
    
    def __init__(self, rpc_url: str, chain_info: BlockchainInfo):
        super().__init__(rpc_url, chain_info)
        self.web3 = None
        self._initialize_web3()
    
    def _initialize_web3(self):
        """Initialize Web3 connection"""
        try:
            self.web3 = Web3(Web3.HTTPProvider(
                self.rpc_url,
                request_kwargs={'timeout': settings.blockchain.request_timeout}
            ))
            
            # Add PoA middleware for chains that need it
            if self.chain_info.name in ['bsc', 'polygon', 'ronin']:
                self.web3.middleware_onion.inject(geth_poa_middleware, layer=0)
            
            self.is_connected = True
            logger.info(f"✅ Initialized {self.chain_info.name} Web3 client")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize {self.chain_info.name} Web3: {e}")
            self.is_connected = False
    
    async def get_latest_block_number(self) -> Optional[int]:
        """Get the latest block number"""
        if not self.web3:
            return None
        
        try:
            return self.web3.eth.block_number
        except Exception as e:
            logger.error(f"Error getting latest block for {self.chain_info.name}: {e}")
            return None
    
    async def get_block(self, block_number: int) -> Optional[Dict]:
        """Get block data"""
        if not self.web3:
            return None
        
        try:
            block = self.web3.eth.get_block(block_number, full_transactions=True)
            return dict(block)
        except Exception as e:
            logger.error(f"Error getting block {block_number} for {self.chain_info.name}: {e}")
            return None
    
    async def get_transaction(self, tx_hash: str) -> Optional[Dict]:
        """Get transaction data"""
        if not self.web3:
            return None
        
        try:
            tx = self.web3.eth.get_transaction(tx_hash)
            return dict(tx)
        except Exception as e:
            logger.error(f"Error getting transaction {tx_hash} for {self.chain_info.name}: {e}")
            return None
    
    async def test_connection(self) -> bool:
        """Test if connection is working"""
        try:
            block_number = await self.get_latest_block_number()
            return block_number is not None
        except Exception:
            return False
    
    async def call_contract_function(
        self,
        contract_address: str,
        abi: list,
        function_name: str,
        *args
    ) -> Any:
        """Call a contract function"""
        if not self.web3:
            return None
        
        try:
            contract = self.web3.eth.contract(
                address=Web3.toChecksumAddress(contract_address),
                abi=abi
            )
            
            function = getattr(contract.functions, function_name)
            result = function(*args).call()
            return result
        except Exception as e:
            logger.error(f"Error calling {function_name} on {contract_address} ({self.chain_info.name}): {e}")
            return None


class SolanaChainClient(BaseChainClient):
    """Client for Solana blockchain"""
    
    def __init__(self, rpc_url: str, chain_info: BlockchainInfo):
        super().__init__(rpc_url, chain_info)
        self.session = None
    
    async def _make_rpc_request(self, method: str, params: List = None) -> Optional[Dict]:
        """Make RPC request to Solana"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or []
        }
        
        try:
            async with self.session.post(
                self.rpc_url,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=settings.blockchain.request_timeout)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' in data:
                        logger.error(f"Solana RPC error: {data['error']}")
                        return None
                    return data.get('result')
                else:
                    logger.error(f"Solana RPC HTTP error: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Solana RPC request failed: {e}")
            return None
    
    async def get_latest_block_number(self) -> Optional[int]:
        """Get the latest slot (block) number"""
        result = await self._make_rpc_request("getSlot")
        return result if result is not None else None
    
    async def get_block(self, block_number: int) -> Optional[Dict]:
        """Get block data"""
        result = await self._make_rpc_request("getBlock", [block_number])
        return result
    
    async def get_transaction(self, tx_hash: str) -> Optional[Dict]:
        """Get transaction data"""
        result = await self._make_rpc_request("getTransaction", [tx_hash])
        return result
    
    async def test_connection(self) -> bool:
        """Test if connection is working"""
        try:
            slot = await self.get_latest_block_number()
            return slot is not None
        except Exception:
            return False
    
    async def get_account_info(self, account_address: str) -> Optional[Dict]:
        """Get Solana account information"""
        result = await self._make_rpc_request("getAccountInfo", [account_address])
        return result
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()


class TONChainClient(BaseChainClient):
    """Client for TON blockchain"""
    
    def __init__(self, rpc_url: str, chain_info: BlockchainInfo):
        super().__init__(rpc_url, chain_info)
        self.session = None
    
    async def _make_rpc_request(self, method: str, params: Dict = None) -> Optional[Dict]:
        """Make RPC request to TON"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or {}
        }
        
        try:
            async with self.session.post(
                self.rpc_url,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=settings.blockchain.request_timeout)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' in data:
                        logger.error(f"TON RPC error: {data['error']}")
                        return None
                    return data.get('result')
                else:
                    logger.error(f"TON RPC HTTP error: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"TON RPC request failed: {e}")
            return None
    
    async def get_latest_block_number(self) -> Optional[int]:
        """Get the latest block number"""
        result = await self._make_rpc_request("getMasterchainInfo")
        if result and 'last' in result:
            return result['last']['seqno']
        return None
    
    async def get_block(self, block_number: int) -> Optional[Dict]:
        """Get block data"""
        # TON uses different block identification
        result = await self._make_rpc_request("getBlockHeader", {"seqno": block_number})
        return result
    
    async def get_transaction(self, tx_hash: str) -> Optional[Dict]:
        """Get transaction data"""
        result = await self._make_rpc_request("getTransactions", {"hash": tx_hash})
        return result
    
    async def test_connection(self) -> bool:
        """Test if connection is working"""
        try:
            block_number = await self.get_latest_block_number()
            return block_number is not None
        except Exception:
            return False
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()


class MultiChainManager:
    """Manager for multiple blockchain clients"""
    
    def __init__(self):
        self.clients: Dict[str, BaseChainClient] = {}
        self.blockchain_info = self._get_blockchain_info()
        self._initialize_clients()
    
    def _get_blockchain_info(self) -> Dict[str, BlockchainInfo]:
        """Get blockchain information for supported chains"""
        return {
            'ethereum': BlockchainInfo('ethereum', 1, 'ETH', 12.0, True, True),
            'polygon': BlockchainInfo('polygon', 137, 'MATIC', 2.0, True, True),
            'bsc': BlockchainInfo('bsc', 56, 'BNB', 3.0, True, True),
            'arbitrum': BlockchainInfo('arbitrum', 42161, 'ETH', 0.25, True, True),
            'optimism': BlockchainInfo('optimism', 10, 'ETH', 2.0, True, True),
            'base': BlockchainInfo('base', 8453, 'ETH', 2.0, True, True),
            'avalanche': BlockchainInfo('avalanche', 43114, 'AVAX', 2.0, True, True),
            'immutable': BlockchainInfo('immutable', 13371, 'IMX', 1.0, True, True),
            'ronin': BlockchainInfo('ronin', 2020, 'RON', 3.0, True, True),
            'solana': BlockchainInfo('solana', None, 'SOL', 0.4, False, True),
            'ton': BlockchainInfo('ton', None, 'TON', 5.0, False, True),
        }
    
    def _initialize_clients(self):
        """Initialize blockchain clients"""
        # EVM chains
        evm_chains = {
            'ethereum': settings.blockchain.ethereum_rpc_url,
            'polygon': settings.blockchain.polygon_rpc_url,
            'bsc': settings.blockchain.bsc_rpc_url,
            'arbitrum': settings.blockchain.arbitrum_rpc_url,
            'optimism': settings.blockchain.optimism_rpc_url,
            'base': settings.blockchain.base_rpc_url,
            'avalanche': settings.blockchain.avalanche_rpc_url,
            'immutable': settings.blockchain.immutable_rpc_url,
            'ronin': settings.blockchain.ronin_rpc_url,
        }
        
        for chain, rpc_url in evm_chains.items():
            if rpc_url:
                chain_info = self.blockchain_info[chain]
                self.clients[chain] = EVMChainClient(rpc_url, chain_info)
        
        # Non-EVM chains
        if settings.blockchain.solana_rpc_url:
            solana_info = self.blockchain_info['solana']
            self.clients['solana'] = SolanaChainClient(settings.blockchain.solana_rpc_url, solana_info)
        
        if settings.blockchain.ton_rpc_url:
            ton_info = self.blockchain_info['ton']
            self.clients['ton'] = TONChainClient(settings.blockchain.ton_rpc_url, ton_info)
    
    def get_client(self, chain: str) -> Optional[BaseChainClient]:
        """Get client for a specific chain"""
        return self.clients.get(chain)
    
    def get_gaming_chains(self) -> List[str]:
        """Get list of gaming-focused chains"""
        return [
            chain for chain, info in self.blockchain_info.items()
            if info.gaming_focus and chain in self.clients
        ]
    
    async def test_all_connections(self) -> Dict[str, bool]:
        """Test all blockchain connections"""
        results = {}
        
        for chain, client in self.clients.items():
            try:
                results[chain] = await client.test_connection()
                if results[chain]:
                    logger.info(f"✅ {chain}: Connection successful")
                else:
                    logger.error(f"❌ {chain}: Connection failed")
            except Exception as e:
                results[chain] = False
                logger.error(f"❌ {chain}: Connection test error - {e}")
        
        return results
    
    async def get_all_latest_blocks(self) -> Dict[str, Optional[int]]:
        """Get latest block numbers from all chains"""
        results = {}
        
        for chain, client in self.clients.items():
            try:
                block_number = await client.get_latest_block_number()
                results[chain] = block_number
                if block_number:
                    logger.info(f"{chain}: Latest block {block_number}")
            except Exception as e:
                results[chain] = None
                logger.error(f"Error getting latest block for {chain}: {e}")
        
        return results
    
    async def close_all(self):
        """Close all client connections"""
        for client in self.clients.values():
            if hasattr(client, 'close'):
                await client.close()


# Global multi-chain manager instance
multi_chain_manager = MultiChainManager()
