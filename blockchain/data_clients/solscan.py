"""
Solscan API Client
Provides access to Solana blockchain data via Solscan API
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class SolscanClient(BaseBlockchainDataClient):
    """Client for Solscan API"""
    
    def __init__(self):
        super().__init__(
            api_key=settings.blockchain_data.solscan_api_key,
            base_url=settings.blockchain_data.solscan_base_url,
            rate_limit=60
        )
        self.public_url = settings.blockchain_data.solscan_public_url

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Solscan API"""
        if self.api_key and self.api_key != 'your_solscan_api_key':
            return {'token': self.api_key}
        return {}

    async def _test_endpoint(self):
        """Test Solscan API connection"""
        # Use a simple public endpoint that doesn't require authentication
        await self._make_public_request('chaininfo')

    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """Get data for gaming tokens"""
        results = []
        for token in tokens:
            token_info = await self.get_token_info(token)
            if token_info:
                results.append(token_info)
        return results

    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data"""
        return await self.get_nft_collection_info(collection_address) or {}

    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics"""
        # This would require mapping protocol names to account addresses
        # For now, return empty dict
        return {}

    async def _make_public_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make request to public Solscan API"""
        await self.rate_limiter.wait_if_needed()

        url = f"{self.public_url.rstrip('/')}/{endpoint.lstrip('/')}"

        # Default headers for public API
        headers = {
            'User-Agent': 'Web3GamingNewsBot/1.0',
            'Accept': 'application/json',
        }

        for attempt in range(3):  # Use settings.blockchain_data.max_retries if available
            try:
                if not self.session:
                    raise RuntimeError("Client session not initialized. Use async context manager.")

                async with self.session.request(
                    method='GET',
                    url=url,
                    params=params,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 429:  # Rate limited
                        wait_time = 2 ** attempt
                        logger.warning(f"Rate limited, waiting {wait_time} seconds")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        response_text = await response.text()
                        logger.error(f"Public API request failed: {response.status} - {response_text}")
                        response.raise_for_status()

            except Exception as e:
                logger.error(f"Public request failed (attempt {attempt + 1}): {e}")
                if attempt == 2:  # Last attempt
                    raise
                await asyncio.sleep(2 ** attempt)

        raise Exception(f"Failed to complete public request after 3 attempts")

    async def get_account_info(self, account_address: str) -> Optional[Dict[str, Any]]:
        """Get account information from Solscan"""
        try:
            # Use public API for basic account info
            response = await self._make_public_request(f'account/{account_address}')

            if response and response.get('success'):
                return response.get('data', {})

            return None

        except Exception as e:
            logger.error(f"Error getting account info for {account_address}: {e}")
            return None
    
    async def get_account_tokens(self, account_address: str) -> Optional[List[Dict]]:
        """Get tokens held by an account"""
        try:
            # Use pro API if available, fallback to public
            if self.api_key and self.api_key != 'your_solscan_api_key':
                url = f"/account/token-accounts"
                params = {'address': account_address}
                response = await self._make_request('GET', url, params=params)
            else:
                url = f"{self.public_url}/account/tokens"
                params = {'account': account_address}
                response = await self._make_request('GET', url, params=params)
            
            if response and response.get('success'):
                return response.get('data', [])
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting account tokens for {account_address}: {e}")
            return []
    
    async def get_account_transactions(
        self,
        account_address: str,
        limit: int = 50,
        before: Optional[str] = None
    ) -> Optional[List[Dict]]:
        """Get transactions for an account"""
        try:
            params = {
                'account': account_address,
                'limit': limit
            }

            if before:
                params['before'] = before

            response = await self._make_public_request('account/transaction', params=params)

            if response and response.get('success'):
                return response.get('data', [])

            return []

        except Exception as e:
            logger.error(f"Error getting account transactions for {account_address}: {e}")
            return []
    
    async def get_token_info(self, token_address: str) -> Optional[Dict[str, Any]]:
        """Get token information"""
        try:
            params = {'token': token_address}

            response = await self._make_public_request('token/meta', params=params)
            
            if response and response.get('success'):
                return response.get('data', {})
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting token info for {token_address}: {e}")
            return None
    
    async def get_token_holders(self, token_address: str, limit: int = 100) -> Optional[List[Dict]]:
        """Get token holders"""
        try:
            url = f"{self.public_url}/token/holders"
            params = {
                'token': token_address,
                'limit': limit
            }
            
            response = await self._make_request('GET', url, params=params)
            
            if response and response.get('success'):
                return response.get('data', [])
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting token holders for {token_address}: {e}")
            return []
    
    async def get_nft_collection_info(self, collection_address: str) -> Optional[Dict[str, Any]]:
        """Get NFT collection information"""
        try:
            # Use pro API if available
            if self.api_key and self.api_key != 'your_solscan_api_key':
                url = f"/nft/collection/meta"
                params = {'collection': collection_address}
                response = await self._make_request('GET', url, params=params)
            else:
                # Fallback to account info for public API
                return await self.get_account_info(collection_address)
            
            if response and response.get('success'):
                return response.get('data', {})
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting NFT collection info for {collection_address}: {e}")
            return None
    
    async def analyze_account_for_gaming(self, account_address: str) -> Dict[str, Any]:
        """Analyze Solana account for gaming-related patterns"""
        try:
            logger.info(f"Analyzing Solana account {account_address} for gaming patterns")
            
            # Get account information
            account_info = await self.get_account_info(account_address)
            tokens = await self.get_account_tokens(account_address)
            recent_txs = await self.get_account_transactions(account_address, limit=50)
            
            analysis_result = {
                'account_address': account_address,
                'blockchain': 'solana',
                'account_exists': bool(account_info),
                'token_count': len(tokens) if tokens else 0,
                'recent_transaction_count': len(recent_txs) if recent_txs else 0,
                'gaming_indicators': [],
                'confidence_score': 0.0,
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
            
            # Analyze account type and data
            if account_info:
                account_type = account_info.get('type', '')
                if account_type in ['program', 'nft']:
                    analysis_result['gaming_indicators'].append(f"account_type:{account_type}")
                    analysis_result['confidence_score'] += 0.1
            
            # Analyze tokens for gaming patterns
            gaming_token_keywords = [
                'game', 'gaming', 'play', 'player', 'character', 'item', 'weapon',
                'armor', 'spell', 'battle', 'fight', 'quest', 'adventure', 'level',
                'experience', 'skill', 'pet', 'monster', 'creature', 'card', 'deck',
                'guild', 'clan', 'tournament', 'arena', 'dungeon', 'raid'
            ]
            
            if tokens:
                for token in tokens:
                    token_info = token.get('tokenAccount', {})
                    token_name = token_info.get('tokenName', '').lower()
                    token_symbol = token_info.get('tokenSymbol', '').lower()
                    
                    for keyword in gaming_token_keywords:
                        if keyword in token_name or keyword in token_symbol:
                            analysis_result['gaming_indicators'].append(f"token_keyword:{keyword}")
                            analysis_result['confidence_score'] += 0.05
                            break
            
            # Analyze transaction patterns
            if recent_txs:
                # Look for gaming-related program interactions
                gaming_programs = [
                    'game', 'gaming', 'play', 'battle', 'quest', 'adventure',
                    'metaplex', 'nft', 'token', 'mint', 'craft', 'upgrade'
                ]
                
                for tx in recent_txs:
                    tx_type = tx.get('txType', '').lower()
                    for program in gaming_programs:
                        if program in tx_type:
                            analysis_result['gaming_indicators'].append(f"tx_type:{tx_type}")
                            analysis_result['confidence_score'] += 0.02
                            break
            
            # Check if account is an NFT collection
            if account_info and account_info.get('type') == 'nft':
                nft_info = await self.get_nft_collection_info(account_address)
                if nft_info:
                    collection_name = nft_info.get('name', '').lower()
                    for keyword in gaming_token_keywords:
                        if keyword in collection_name:
                            analysis_result['gaming_indicators'].append(f"nft_collection_keyword:{keyword}")
                            analysis_result['confidence_score'] += 0.15
                            break
            
            # Cap confidence score at 1.0
            analysis_result['confidence_score'] = min(1.0, analysis_result['confidence_score'])
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error analyzing Solana account {account_address} for gaming: {e}")
            return {
                'account_address': account_address,
                'blockchain': 'solana',
                'error': str(e),
                'confidence_score': 0.0,
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Solscan API health"""
        try:
            # Test with a simple API call to public endpoint
            response = await self._make_public_request('chaininfo')
            
            return {
                'service': 'Solscan',
                'status': 'healthy' if response and response.get('success') else 'unhealthy',
                'api_key_configured': bool(self.api_key and self.api_key != 'your_solscan_api_key'),
                'base_url': self.base_url,
                'public_url': self.public_url,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'service': 'Solscan',
                'status': 'unhealthy',
                'error': str(e),
                'api_key_configured': bool(self.api_key and self.api_key != 'your_solscan_api_key'),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global instance
solscan_client = SolscanClient()
