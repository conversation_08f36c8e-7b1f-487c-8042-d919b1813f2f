"""
CryptoRank API client for cryptocurrency data
"""
import logging
from typing import Dict, List, Optional, Any
from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class CryptoRankClient(BaseBlockchainDataClient):
    """Client for CryptoRank API"""
    
    def __init__(self):
        super().__init__(
            api_key=settings.blockchain_data.cryptorank_api_key,
            base_url=settings.blockchain_data.cryptorank_base_url,
            rate_limit=settings.blockchain_data.rate_limit_per_minute
        )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for CryptoRank API"""
        return {
            'Content-Type': 'application/json',
            'X-API-KEY': self.api_key
        }

    def _get_auth_params(self, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Get authentication parameters for CryptoRank API"""
        # v2 API uses header authentication, not query params
        if params:
            return params
        return {}

    async def _test_endpoint(self):
        """Test CryptoRank API connection"""
        # Test with currencies endpoint - v2 API requires limit to be 100, 500, or 1000
        await self._make_request('GET', '/currencies', params={'limit': 100})
    
    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """Get gaming token data from CryptoRank"""
        if not tokens:
            return []

        results = []

        try:
            # Get all currencies and filter for gaming tokens
            params = {
                'limit': 1000,  # Get more data to find gaming tokens
                'sortBy': 'rank',
                'sortDirection': 'ASC'
            }

            response = await self._make_request('GET', '/currencies', params=params)
            all_currencies = response.get('data', [])

            # Filter for requested tokens
            for currency in all_currencies:
                symbol = currency.get('symbol', '').upper()
                if symbol in [token.upper() for token in tokens]:
                    results.append({
                        'symbol': currency.get('symbol'),
                        'name': currency.get('name'),
                        'price_usd': float(currency.get('price', 0)),
                        'market_cap_usd': float(currency.get('marketCap', 0)),
                        'volume_24h_usd': float(currency.get('volume24h', 0)),
                        'price_change_24h_percent': currency.get('percentChange24h'),
                        'rank': currency.get('rank'),
                        'category_id': currency.get('categoryId'),
                        'last_updated': currency.get('lastUpdated'),
                        'circulating_supply': currency.get('circulatingSupply'),
                        'total_supply': currency.get('totalSupply'),
                        'max_supply': currency.get('maxSupply')
                    })

        except Exception as e:
            logger.error(f"Failed to get gaming tokens data: {e}")

        return results
    
    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data from CryptoRank"""
        # CryptoRank may not have direct NFT collection endpoints
        # This would need to be implemented based on their actual API structure
        try:
            # Placeholder - would need actual NFT endpoints
            params = {
                'address': collection_address
            }

            # This endpoint may not exist - check CryptoRank API docs
            response = await self._make_request('GET', '/nft/collections', params=self._get_auth_params(params))
            return response.get('data', {})
            
        except Exception as e:
            logger.error(f"Failed to get NFT collection data: {e}")
            return {}
    
    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics from CryptoRank"""
        try:
            # Get currencies and search for the protocol
            params = {
                'limit': 1000,
                'sortBy': 'rank',
                'sortDirection': 'ASC'
            }

            response = await self._make_request('GET', '/currencies', params=params)
            data = response.get('data', [])

            # Filter for gaming-related protocols matching the name
            gaming_protocols = [
                item for item in data
                if protocol_name.lower() in item.get('name', '').lower() or
                   protocol_name.lower() in item.get('symbol', '').lower() or
                   protocol_name.lower() in item.get('key', '').lower()
            ]

            return {
                'protocol_name': protocol_name,
                'protocols': gaming_protocols[:10],  # Limit to top 10 matches
                'total_found': len(gaming_protocols)
            }

        except Exception as e:
            logger.error(f"Failed to get gaming protocol metrics: {e}")
            return {}
    
    async def get_gaming_categories_data(self) -> Dict[str, Any]:
        """Get data for gaming category cryptocurrencies"""
        try:
            # Get all currencies and filter for gaming-related ones
            params = {
                'limit': 1000,
                'sortBy': 'marketCap',
                'sortDirection': 'DESC'
            }

            response = await self._make_request('GET', '/currencies', params=params)
            all_currencies = response.get('data', [])

            # Filter for gaming-related tokens by name and known gaming category IDs
            gaming_keywords = ['game', 'gaming', 'play', 'nft', 'metaverse', 'virtual', 'sandbox', 'axie', 'decentraland']
            gaming_currencies = []

            for currency in all_currencies:
                name_lower = currency.get('name', '').lower()
                symbol_lower = currency.get('symbol', '').lower()

                if any(keyword in name_lower or keyword in symbol_lower for keyword in gaming_keywords):
                    gaming_currencies.append(currency)

            return {
                'gaming_category': gaming_currencies[:100]  # Limit to top 100
            }

        except Exception as e:
            logger.error(f"Failed to get gaming categories data: {e}")
            return {}
    
    async def get_metaverse_tokens_data(self) -> Dict[str, Any]:
        """Get metaverse token data"""
        try:
            params = {
                'category': 'metaverse',
                'limit': 50,
                'sortBy': 'marketCap',
                'sortDirection': 'desc'
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            return {
                'metaverse_tokens': response.get('data', [])
            }
            
        except Exception as e:
            logger.error(f"Failed to get metaverse tokens data: {e}")
            return {}
    
    async def get_nft_tokens_data(self) -> Dict[str, Any]:
        """Get NFT-related token data"""
        try:
            params = {
                'category': 'nft',
                'limit': 50,
                'sortBy': 'marketCap',
                'sortDirection': 'desc'
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            return {
                'nft_tokens': response.get('data', [])
            }
            
        except Exception as e:
            logger.error(f"Failed to get NFT tokens data: {e}")
            return {}
    
    async def get_token_historical_data(self, symbol: str, days: int = 30) -> Dict[str, Any]:
        """Get historical price data for a token"""
        try:
            # First get the currency ID
            params = {
                'symbol': symbol,
                'limit': 1
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            data = response.get('data', [])
            
            if not data:
                return {}
            
            currency_id = data[0].get('id')
            
            # Get historical data
            params = {
                'days': days,
                'interval': 'daily'
            }
            
            historical_response = await self._make_request(
                'GET', 
                f'/currencies/{currency_id}/chart', 
                params=params
            )
            
            return {
                'symbol': symbol,
                'historical_data': historical_response.get('data', [])
            }
            
        except Exception as e:
            logger.error(f"Failed to get historical data for {symbol}: {e}")
            return {}
    
    async def get_trending_gaming_tokens(self) -> Dict[str, Any]:
        """Get trending gaming tokens"""
        try:
            params = {
                'category': 'gaming',
                'sortBy': 'percentChange24h',
                'sortDirection': 'desc',
                'limit': 20
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            return {
                'trending_gaming_tokens': response.get('data', [])
            }
            
        except Exception as e:
            logger.error(f"Failed to get trending gaming tokens: {e}")
            return {}
    
    async def search_gaming_projects(self, query: str) -> Dict[str, Any]:
        """Search for gaming-related projects"""
        try:
            params = {
                'query': query,
                'limit': 20
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            data = response.get('data', [])
            
            # Filter for gaming-related results
            gaming_results = [
                item for item in data
                if any(keyword in item.get('name', '').lower() or 
                      keyword in item.get('category', '').lower()
                      for keyword in ['gaming', 'game', 'nft', 'metaverse', 'play'])
            ]
            
            return {
                'query': query,
                'gaming_projects': gaming_results
            }
            
        except Exception as e:
            logger.error(f"Failed to search gaming projects: {e}")
            return {}
