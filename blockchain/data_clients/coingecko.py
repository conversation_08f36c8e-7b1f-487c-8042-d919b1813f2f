"""
CoinGecko API client for gaming token data with rate limiting
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
from dataclasses import dataclass

from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class CoinGeckoTokenData:
    """CoinGecko token data structure"""
    id: str
    symbol: str
    name: str
    current_price: float
    market_cap: Optional[float]
    market_cap_rank: Optional[int]
    price_change_24h: float
    price_change_percentage_24h: float
    volume_24h: Optional[float]
    circulating_supply: Optional[float]
    total_supply: Optional[float]
    max_supply: Optional[float]
    last_updated: str


class CoinGeckoClient(BaseBlockchainDataClient):
    """CoinGecko API client with rate limiting (10 calls per hour)"""
    
    def __init__(self):
        super().__init__(
            api_key=settings.gaming_api.coingecko_api_key,
            base_url="https://api.coingecko.com/api/v3/",
            rate_limit=10  # 10 calls per hour
        )
        
        # Rate limiting: 10 calls per hour = 1 call every 6 minutes
        self.rate_limit_delay = 360  # 6 minutes in seconds
        self.last_request_time = None
        self.request_count = 0
        self.hour_start = datetime.now()
        
        # Gaming token categories and IDs
        self.gaming_categories = [
            'gaming',
            'play-to-earn',
            'metaverse',
            'nft',
            'virtual-reality'
        ]
        
        # Known gaming token IDs for direct queries
        self.gaming_token_ids = [
            'axie-infinity',
            'the-sandbox',
            'decentraland',
            'gala',
            'enjincoin',
            'immutable-x',
            'smooth-love-potion',
            'star-atlas',
            'illuvium',
            'gods-unchained',
            'alien-worlds',
            'splinterlands',
            'my-neighbor-alice',
            'chromia',
            'ultra',
            'wax',
            'flow',
            'ronin'
        ]
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting (10 calls per hour)"""
        now = datetime.now()
        
        # Reset counter if hour has passed
        if now - self.hour_start >= timedelta(hours=1):
            self.request_count = 0
            self.hour_start = now
        
        # Check if we've exceeded rate limit
        if self.request_count >= 10:
            time_to_wait = 3600 - (now - self.hour_start).total_seconds()
            if time_to_wait > 0:
                logger.warning(f"CoinGecko rate limit reached. Waiting {time_to_wait:.0f} seconds")
                await asyncio.sleep(time_to_wait)
                self.request_count = 0
                self.hour_start = datetime.now()
        
        # Ensure minimum delay between requests
        if self.last_request_time:
            time_since_last = (now - self.last_request_time).total_seconds()
            if time_since_last < self.rate_limit_delay:
                wait_time = self.rate_limit_delay - time_since_last
                logger.info(f"CoinGecko rate limiting: waiting {wait_time:.0f} seconds")
                await asyncio.sleep(wait_time)
        
        self.last_request_time = datetime.now()
        self.request_count += 1
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for CoinGecko API"""
        headers = {
            'Accept': 'application/json',
            'User-Agent': 'Web3GamingTracker/1.0'
        }
        
        if self.api_key:
            headers['x-cg-demo-api-key'] = self.api_key
        
        return headers
    
    async def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """Make rate-limited request to CoinGecko API"""
        await self._check_rate_limit()
        
        url = f"{self.base_url}{endpoint}"
        headers = self._get_auth_headers()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params, timeout=30) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 429:
                        logger.warning("CoinGecko rate limit exceeded, backing off")
                        await asyncio.sleep(60)  # Wait 1 minute on rate limit
                        return None
                    else:
                        logger.error(f"CoinGecko API error {response.status}: {await response.text()}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error making CoinGecko request to {endpoint}: {e}")
            return None
    
    async def get_gaming_tokens_data(self, token_ids: Optional[List[str]] = None) -> List[CoinGeckoTokenData]:
        """Get gaming tokens data from CoinGecko"""
        if not token_ids:
            token_ids = self.gaming_token_ids
        
        # CoinGecko allows up to 250 IDs per request
        batch_size = 100  # Use smaller batches to be safe
        all_tokens = []
        
        for i in range(0, len(token_ids), batch_size):
            batch = token_ids[i:i + batch_size]
            ids_param = ','.join(batch)
            
            params = {
                'ids': ids_param,
                'vs_currencies': 'usd',
                'include_market_cap': 'true',
                'include_24hr_vol': 'true',
                'include_24hr_change': 'true',
                'include_last_updated_at': 'true'
            }
            
            data = await self._make_request('simple/price', params)
            
            if data:
                for token_id, token_data in data.items():
                    try:
                        token_info = CoinGeckoTokenData(
                            id=token_id,
                            symbol=token_id,  # Will be updated with actual symbol if available
                            name=token_id.replace('-', ' ').title(),
                            current_price=float(token_data.get('usd', 0)),
                            market_cap=float(token_data.get('usd_market_cap', 0)) if token_data.get('usd_market_cap') else None,
                            market_cap_rank=None,
                            price_change_24h=float(token_data.get('usd_24h_change', 0)),
                            price_change_percentage_24h=float(token_data.get('usd_24h_change', 0)),
                            volume_24h=float(token_data.get('usd_24h_vol', 0)) if token_data.get('usd_24h_vol') else None,
                            circulating_supply=None,
                            total_supply=None,
                            max_supply=None,
                            last_updated=token_data.get('last_updated_at', '')
                        )
                        all_tokens.append(token_info)
                        
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error parsing token data for {token_id}: {e}")
                        continue
        
        logger.info(f"Retrieved {len(all_tokens)} gaming tokens from CoinGecko")
        return all_tokens
    
    async def get_gaming_categories_data(self) -> List[Dict[str, Any]]:
        """Get gaming categories data from CoinGecko"""
        all_category_data = []
        
        for category in self.gaming_categories:
            params = {
                'category': category,
                'order': 'market_cap_desc',
                'per_page': 50,
                'page': 1,
                'sparkline': 'false'
            }
            
            data = await self._make_request('coins/markets', params)
            
            if data:
                all_category_data.extend(data)
        
        logger.info(f"Retrieved {len(all_category_data)} tokens from gaming categories")
        return all_category_data
    
    async def get_token_by_contract(self, platform: str, contract_address: str) -> Optional[CoinGeckoTokenData]:
        """Get token data by contract address"""
        endpoint = f"coins/{platform}/contract/{contract_address}"
        
        data = await self._make_request(endpoint)
        
        if data:
            try:
                market_data = data.get('market_data', {})
                
                return CoinGeckoTokenData(
                    id=data.get('id', ''),
                    symbol=data.get('symbol', '').upper(),
                    name=data.get('name', ''),
                    current_price=float(market_data.get('current_price', {}).get('usd', 0)),
                    market_cap=float(market_data.get('market_cap', {}).get('usd', 0)) if market_data.get('market_cap', {}).get('usd') else None,
                    market_cap_rank=market_data.get('market_cap_rank'),
                    price_change_24h=float(market_data.get('price_change_24h', 0)),
                    price_change_percentage_24h=float(market_data.get('price_change_percentage_24h', 0)),
                    volume_24h=float(market_data.get('total_volume', {}).get('usd', 0)) if market_data.get('total_volume', {}).get('usd') else None,
                    circulating_supply=float(market_data.get('circulating_supply', 0)) if market_data.get('circulating_supply') else None,
                    total_supply=float(market_data.get('total_supply', 0)) if market_data.get('total_supply') else None,
                    max_supply=float(market_data.get('max_supply', 0)) if market_data.get('max_supply') else None,
                    last_updated=market_data.get('last_updated', '')
                )
                
            except (ValueError, TypeError) as e:
                logger.error(f"Error parsing contract token data: {e}")
                return None
        
        return None
    
    async def search_gaming_tokens(self, query: str = "gaming") -> List[Dict[str, Any]]:
        """Search for gaming-related tokens"""
        params = {'query': query}
        
        data = await self._make_request('search', params)
        
        if data and 'coins' in data:
            return data['coins']
        
        return []
    
    async def get_trending_gaming_tokens(self) -> List[Dict[str, Any]]:
        """Get trending tokens and filter for gaming ones"""
        data = await self._make_request('search/trending')
        
        if data and 'coins' in data:
            # Filter for gaming-related tokens
            gaming_trending = []
            for coin in data['coins']:
                coin_data = coin.get('item', {})
                name = coin_data.get('name', '').lower()
                symbol = coin_data.get('symbol', '').lower()
                
                # Check if it's gaming-related
                gaming_keywords = ['game', 'gaming', 'play', 'nft', 'metaverse', 'virtual']
                if any(keyword in name or keyword in symbol for keyword in gaming_keywords):
                    gaming_trending.append(coin_data)
            
            return gaming_trending
        
        return []
    
    async def close(self):
        """Close the client"""
        # No persistent connections to close for this client
        pass

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for CoinGecko API"""
        if self.api_key:
            return {"x-cg-demo-api-key": self.api_key}
        return {}

    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """Get data for gaming tokens"""
        if not tokens:
            return []

        # Convert token list to comma-separated string
        token_ids = ','.join(tokens)
        params = {
            'ids': token_ids,
            'vs_currencies': 'usd',
            'include_market_cap': 'true',
            'include_24hr_vol': 'true',
            'include_24hr_change': 'true'
        }

        data = await self._make_request('simple/price', params)

        # Convert to list format
        result = []
        if data:
            for token_id, token_data in data.items():
                result.append({
                    'id': token_id,
                    'price_usd': token_data.get('usd', 0),
                    'market_cap': token_data.get('usd_market_cap'),
                    'volume_24h': token_data.get('usd_24h_vol'),
                    'price_change_24h': token_data.get('usd_24h_change', 0)
                })

        return result

    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data"""
        # CoinGecko doesn't have direct NFT collection endpoints
        # Return empty data structure
        return {
            'collection_address': collection_address,
            'floor_price': None,
            'volume_24h': None,
            'market_cap': None,
            'owners': None,
            'total_supply': None,
            'error': 'NFT data not available via CoinGecko API'
        }

    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics"""
        try:
            # Search for the protocol first
            search_data = await self.search_gaming_tokens(protocol_name)

            if not search_data:
                return {
                    'protocol_name': protocol_name,
                    'tvl': None,
                    'market_cap': None,
                    'volume_24h': None,
                    'price_change_24h': None,
                    'error': f'Protocol {protocol_name} not found'
                }

            # Get the first matching result
            protocol_data = search_data[0]
            protocol_id = protocol_data.get('id')

            if protocol_id:
                # Get detailed data for the protocol
                token_data = await self.get_gaming_tokens_data([protocol_id])

                if token_data:
                    token_info = token_data[0]
                    return {
                        'protocol_name': protocol_name,
                        'protocol_id': protocol_id,
                        'price_usd': token_info.get('price_usd'),
                        'market_cap': token_info.get('market_cap'),
                        'volume_24h': token_info.get('volume_24h'),
                        'price_change_24h': token_info.get('price_change_24h'),
                        'tvl': None  # CoinGecko doesn't provide TVL data
                    }

            return {
                'protocol_name': protocol_name,
                'error': 'Unable to fetch protocol metrics'
            }

        except Exception as e:
            logger.error(f"Error fetching protocol metrics for {protocol_name}: {e}")
            return {
                'protocol_name': protocol_name,
                'error': str(e)
            }

    async def _test_endpoint(self):
        """Test a simple endpoint to verify connection"""
        try:
            data = await self._make_request('ping')
            return data is not None
        except Exception as e:
            logger.error(f"CoinGecko test endpoint failed: {e}")
            return False
