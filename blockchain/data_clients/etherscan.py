"""
Etherscan API Client
Provides access to Ethereum blockchain data via Etherscan API
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class EtherscanClient(BaseBlockchainDataClient):
    """Client for Etherscan API"""
    
    def __init__(self):
        super().__init__(
            api_key=settings.blockchain_data.etherscan_api_key,
            base_url=settings.blockchain_data.etherscan_base_url,
            rate_limit=60
        )

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Etherscan API"""
        return {}  # Etherscan uses API key in query params

    async def _test_endpoint(self):
        """Test Etherscan API connection"""
        params = {
            'module': 'stats',
            'action': 'ethsupply',
            'apikey': self.api_key
        }
        await self._make_request('GET', '', params=params)

    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """Get data for gaming tokens"""
        results = []
        for token in tokens:
            token_info = await self.get_token_info(token)
            if token_info:
                results.append(token_info)
        return results

    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data"""
        return await self.get_token_info(collection_address) or {}

    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics"""
        # This would require mapping protocol names to contract addresses
        # For now, return empty dict
        return {}

    async def get_contract_abi(self, contract_address: str) -> Optional[List[Dict]]:
        """Get contract ABI from Etherscan"""
        try:
            params = {
                'module': 'contract',
                'action': 'getabi',
                'address': contract_address,
                'apikey': self.api_key
            }
            
            response = await self._make_request('GET', '', params=params)
            
            if response and response.get('status') == '1':
                import json
                return json.loads(response.get('result', '[]'))
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting contract ABI for {contract_address}: {e}")
            return None
    
    async def get_contract_source_code(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """Get contract source code from Etherscan"""
        try:
            params = {
                'module': 'contract',
                'action': 'getsourcecode',
                'address': contract_address,
                'apikey': self.api_key
            }
            
            response = await self._make_request('GET', '', params=params)
            
            if response and response.get('status') == '1':
                result = response.get('result', [])
                if result and len(result) > 0:
                    return result[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting contract source code for {contract_address}: {e}")
            return None
    
    async def get_contract_transactions(
        self, 
        contract_address: str, 
        start_block: int = 0, 
        end_block: int = ********,
        page: int = 1,
        offset: int = 100
    ) -> Optional[List[Dict]]:
        """Get transactions for a contract"""
        try:
            params = {
                'module': 'account',
                'action': 'txlist',
                'address': contract_address,
                'startblock': start_block,
                'endblock': end_block,
                'page': page,
                'offset': offset,
                'sort': 'desc',
                'apikey': self.api_key
            }
            
            response = await self._make_request('GET', '', params=params)
            
            if response and response.get('status') == '1':
                return response.get('result', [])
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting contract transactions for {contract_address}: {e}")
            return []
    
    async def get_contract_events(
        self, 
        contract_address: str,
        topic0: Optional[str] = None,
        from_block: int = 0,
        to_block: int = ********
    ) -> Optional[List[Dict]]:
        """Get contract events/logs"""
        try:
            params = {
                'module': 'logs',
                'action': 'getLogs',
                'address': contract_address,
                'fromBlock': from_block,
                'toBlock': to_block,
                'apikey': self.api_key
            }
            
            if topic0:
                params['topic0'] = topic0
            
            response = await self._make_request('GET', '', params=params)
            
            if response and response.get('status') == '1':
                return response.get('result', [])
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting contract events for {contract_address}: {e}")
            return []
    
    async def get_token_info(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """Get ERC-20/ERC-721 token information"""
        try:
            # Get contract source to determine token type
            source_info = await self.get_contract_source_code(contract_address)
            if not source_info:
                return None
            
            token_info = {
                'contract_address': contract_address,
                'contract_name': source_info.get('ContractName', ''),
                'compiler_version': source_info.get('CompilerVersion', ''),
                'optimization_used': source_info.get('OptimizationUsed', ''),
                'source_code': source_info.get('SourceCode', ''),
                'abi': source_info.get('ABI', ''),
                'constructor_arguments': source_info.get('ConstructorArguments', ''),
                'evm_version': source_info.get('EVMVersion', ''),
                'library': source_info.get('Library', ''),
                'license_type': source_info.get('LicenseType', ''),
                'proxy': source_info.get('Proxy', ''),
                'implementation': source_info.get('Implementation', ''),
                'swarm_source': source_info.get('SwarmSource', '')
            }
            
            return token_info
            
        except Exception as e:
            logger.error(f"Error getting token info for {contract_address}: {e}")
            return None
    
    async def analyze_contract_for_gaming(self, contract_address: str) -> Dict[str, Any]:
        """Analyze contract for gaming-related patterns using Etherscan data"""
        try:
            logger.info(f"Analyzing contract {contract_address} for gaming patterns via Etherscan")
            
            # Get contract information
            source_info = await self.get_contract_source_code(contract_address)
            abi_info = await self.get_contract_abi(contract_address)
            recent_txs = await self.get_contract_transactions(contract_address, offset=50)
            
            analysis_result = {
                'contract_address': contract_address,
                'blockchain': 'ethereum',
                'has_source_code': bool(source_info and source_info.get('SourceCode')),
                'has_abi': bool(abi_info),
                'contract_name': source_info.get('ContractName', '') if source_info else '',
                'recent_transaction_count': len(recent_txs) if recent_txs else 0,
                'gaming_indicators': [],
                'confidence_score': 0.0,
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
            
            # Analyze contract name for gaming keywords
            gaming_keywords = [
                'game', 'gaming', 'play', 'player', 'character', 'item', 'weapon',
                'armor', 'spell', 'battle', 'fight', 'quest', 'adventure', 'level',
                'experience', 'skill', 'pet', 'monster', 'creature', 'card', 'deck'
            ]
            
            contract_name = analysis_result['contract_name'].lower()
            for keyword in gaming_keywords:
                if keyword in contract_name:
                    analysis_result['gaming_indicators'].append(f"name_keyword:{keyword}")
                    analysis_result['confidence_score'] += 0.1
            
            # Analyze ABI for gaming functions
            if abi_info:
                gaming_functions = [
                    'mint', 'burn', 'craft', 'upgrade', 'battle', 'fight', 'levelUp',
                    'gainExperience', 'breed', 'hatch', 'evolve', 'equip', 'unequip',
                    'useItem', 'trade', 'stake', 'unstake', 'claimRewards', 'harvest'
                ]
                
                for item in abi_info:
                    if item.get('type') == 'function':
                        func_name = item.get('name', '').lower()
                        for gaming_func in gaming_functions:
                            if gaming_func.lower() in func_name:
                                analysis_result['gaming_indicators'].append(f"function:{item.get('name')}")
                                analysis_result['confidence_score'] += 0.15
                                break
            
            # Analyze source code for gaming patterns (if available)
            if source_info and source_info.get('SourceCode'):
                source_code = source_info['SourceCode'].lower()
                gaming_patterns = [
                    'gaming', 'player', 'character', 'item', 'weapon', 'battle',
                    'level', 'experience', 'quest', 'achievement', 'guild'
                ]
                
                for pattern in gaming_patterns:
                    if pattern in source_code:
                        analysis_result['gaming_indicators'].append(f"source_pattern:{pattern}")
                        analysis_result['confidence_score'] += 0.05
            
            # Cap confidence score at 1.0
            analysis_result['confidence_score'] = min(1.0, analysis_result['confidence_score'])
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error analyzing contract {contract_address} for gaming: {e}")
            return {
                'contract_address': contract_address,
                'blockchain': 'ethereum',
                'error': str(e),
                'confidence_score': 0.0,
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Etherscan API health"""
        try:
            # Test with a simple API call
            params = {
                'module': 'stats',
                'action': 'ethsupply',
                'apikey': self.api_key
            }
            
            response = await self._make_request('GET', '', params=params)
            
            return {
                'service': 'Etherscan',
                'status': 'healthy' if response and response.get('status') == '1' else 'unhealthy',
                'api_key_configured': bool(self.api_key and self.api_key != 'your_etherscan_api_key'),
                'base_url': self.base_url,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'service': 'Etherscan',
                'status': 'unhealthy',
                'error': str(e),
                'api_key_configured': bool(self.api_key and self.api_key != 'your_etherscan_api_key'),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global instance
etherscan_client = EtherscanClient()
