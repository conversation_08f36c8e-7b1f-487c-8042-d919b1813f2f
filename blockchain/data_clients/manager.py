"""
Blockchain data manager for coordinating multiple data sources
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import sys
import os

# Add the config directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))
from config.gaming_config import gaming_project_manager

from .flipside import FlipsideClient
from .bitquery import BitQueryClient
from .cryptorank import CryptoRankClient
from .dextools import DexToolsClient
from .coingecko import CoinGeckoClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class BlockchainDataManager:
    """Manages multiple blockchain data sources"""
    
    def __init__(self):
        self.clients = {}
        self.last_update = {}
        self.cache = {}
        self.cache_ttl = timedelta(minutes=15)  # Cache for 15 minutes
    
    async def initialize_clients(self):
        """Initialize all available data clients"""
        try:
            # Initialize Flipside if API key is available
            if settings.blockchain_data.flipside_api_key:
                self.clients['flipside'] = FlipsideClient()
                logger.info("✅ Flipside client initialized")
            
            # Initialize BitQuery if API key is available
            if settings.blockchain_data.bitquery_api_key:
                self.clients['bitquery'] = BitQueryClient()
                logger.info("✅ BitQuery client initialized")
            
            # Initialize CryptoRank if API key is available
            if settings.blockchain_data.cryptorank_api_key:
                self.clients['cryptorank'] = CryptoRankClient()
                logger.info("✅ CryptoRank client initialized")
            
            # Initialize DexTools if API key is available
            if settings.blockchain_data.dextools_api_key:
                self.clients['dextools'] = DexToolsClient()
                logger.info("✅ DexTools client initialized")

            # Initialize CoinGecko if API key is available
            if settings.gaming.coingecko_api_key:
                self.clients['coingecko'] = CoinGeckoClient()
                logger.info("✅ CoinGecko client initialized")

            if not self.clients:
                logger.warning("⚠️ No blockchain data clients initialized - check API keys")
            
        except Exception as e:
            logger.error(f"Failed to initialize blockchain data clients: {e}")
    
    async def test_all_connections(self) -> Dict[str, bool]:
        """Test connections to all initialized clients"""
        results = {}
        
        for client_name, client in self.clients.items():
            try:
                async with client:
                    is_connected = await client.test_connection()
                    results[client_name] = is_connected
                    logger.info(f"{'✅' if is_connected else '❌'} {client_name} connection test")
            except Exception as e:
                logger.error(f"❌ {client_name} connection failed: {e}")
                results[client_name] = False
        
        return results
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self.cache or cache_key not in self.last_update:
            return False
        
        return datetime.utcnow() - self.last_update[cache_key] < self.cache_ttl
    
    def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get cached data if valid"""
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]
        return None
    
    def _cache_data(self, cache_key: str, data: Any):
        """Cache data with timestamp"""
        self.cache[cache_key] = data
        self.last_update[cache_key] = datetime.utcnow()
    
    async def get_gaming_tokens_data(self, tokens: List[str]) -> Dict[str, Any]:
        """Get gaming token data from all available sources"""
        cache_key = f"gaming_tokens_{'-'.join(sorted(tokens))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        results = {}
        
        # Collect data from all available sources
        tasks = []
        for client_name, client in self.clients.items():
            async def get_client_data(name, client_instance):
                try:
                    async with client_instance:
                        data = await client_instance.get_gaming_tokens_data(tokens)
                        return name, data
                except Exception as e:
                    logger.error(f"Failed to get gaming tokens from {name}: {e}")
                    return name, []
            
            tasks.append(get_client_data(client_name, client))
        
        # Execute all requests concurrently
        if tasks:
            client_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in client_results:
                if isinstance(result, tuple) and len(result) == 2:
                    client_name, data = result
                    results[client_name] = data
        
        # Cache and return results
        self._cache_data(cache_key, results)
        return results
    
    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data from all available sources"""
        cache_key = f"nft_collection_{collection_address}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        results = {}
        
        tasks = []
        for client_name, client in self.clients.items():
            async def get_client_data(name, client_instance):
                try:
                    async with client_instance:
                        data = await client_instance.get_nft_collection_data(collection_address)
                        return name, data
                except Exception as e:
                    logger.error(f"Failed to get NFT data from {name}: {e}")
                    return name, {}
            
            tasks.append(get_client_data(client_name, client))
        
        if tasks:
            client_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in client_results:
                if isinstance(result, tuple) and len(result) == 2:
                    client_name, data = result
                    results[client_name] = data
        
        self._cache_data(cache_key, results)
        return results
    
    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics from all available sources"""
        cache_key = f"protocol_metrics_{protocol_name}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        results = {}
        
        tasks = []
        for client_name, client in self.clients.items():
            async def get_client_data(name, client_instance):
                try:
                    async with client_instance:
                        data = await client_instance.get_gaming_protocol_metrics(protocol_name)
                        return name, data
                except Exception as e:
                    logger.error(f"Failed to get protocol metrics from {name}: {e}")
                    return name, {}
            
            tasks.append(get_client_data(client_name, client))
        
        if tasks:
            client_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in client_results:
                if isinstance(result, tuple) and len(result) == 2:
                    client_name, data = result
                    results[client_name] = data
        
        self._cache_data(cache_key, results)
        return results
    
    async def get_comprehensive_gaming_data(self) -> Dict[str, Any]:
        """Get comprehensive gaming data from all sources"""
        cache_key = "comprehensive_gaming_data"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        # Get gaming configuration from CSV data
        enabled_projects = gaming_project_manager.get_enabled_projects()
        live_projects = gaming_project_manager.get_live_projects()

        # Get gaming tokens from CSV data (fallback to settings if empty)
        gaming_tokens = gaming_project_manager.get_all_token_symbols()
        if not gaming_tokens:
            gaming_tokens = settings.gaming.gaming_tokens
            logger.warning("⚠️ No tokens found in CSV, using settings fallback")

        # Get gaming protocols from CSV data
        gaming_protocols = list(live_projects.keys())
        if not gaming_protocols:
            gaming_protocols = ['axie-infinity', 'the-sandbox', 'decentraland', 'splinterlands']
            logger.warning("⚠️ No live protocols found in CSV, using hardcoded fallback")

        # Collect all data concurrently
        tasks = [
            self.get_gaming_tokens_data(gaming_tokens),
            self.get_coingecko_gaming_data(),
        ]

        # Add protocol metrics for each live project
        for protocol_key in gaming_protocols[:5]:  # Limit to first 5 to avoid too many concurrent requests
            tasks.append(self.get_gaming_protocol_metrics(protocol_key))

        # Get NFT collection data from CSV
        gaming_nft_collections = []
        for project in enabled_projects.values():
            for nft in project.nfts:
                if nft.contract_address:
                    gaming_nft_collections.append(nft.contract_address)

        # Fallback to settings if no NFT collections found in CSV
        if not gaming_nft_collections:
            gaming_nft_collections = [
                settings.gaming.axie_infinity_contract,
                settings.gaming.sandbox_contract,
                settings.gaming.decentraland_contract
            ]
            logger.warning("⚠️ No NFT collections found in CSV, using settings fallback")

        # Add NFT collection data tasks (limit to first 5)
        for collection in gaming_nft_collections[:5]:
            if collection:
                tasks.append(self.get_nft_collection_data(collection))
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results dynamically based on task structure
            result_index = 0

            comprehensive_data = {
                'gaming_tokens': results[result_index] if len(results) > result_index else {},
                'protocol_metrics': {},
                'coingecko_data': {},
                'nft_collections': {},
                'csv_projects_summary': gaming_project_manager.get_project_summary()
            }
            result_index += 1

            # Get CoinGecko data
            if len(results) > result_index:
                comprehensive_data['coingecko_data'] = results[result_index]
            result_index += 1

            # Process protocol metrics dynamically
            for protocol_key in gaming_protocols[:5]:
                if len(results) > result_index:
                    comprehensive_data['protocol_metrics'][protocol_key] = results[result_index]
                result_index += 1

            # Add NFT collection data
            for collection in gaming_nft_collections[:5]:
                if collection and len(results) > result_index:
                    comprehensive_data['nft_collections'][collection] = results[result_index]
                result_index += 1

            comprehensive_data['last_updated'] = datetime.now().isoformat()
            comprehensive_data['data_sources'] = list(self.clients.keys())
            
            self._cache_data(cache_key, comprehensive_data)
            return comprehensive_data
            
        except Exception as e:
            logger.error(f"Failed to get comprehensive gaming data: {e}")
            return {}
    
    async def get_client_status(self) -> Dict[str, Any]:
        """Get status of all data clients"""
        status = {
            'initialized_clients': list(self.clients.keys()),
            'total_clients': len(self.clients),
            'cache_entries': len(self.cache),
            'last_cache_update': max(self.last_update.values()).isoformat() if self.last_update else None
        }
        
        # Test connections
        connection_status = await self.test_all_connections()
        status['connection_status'] = connection_status
        status['healthy_clients'] = sum(1 for connected in connection_status.values() if connected)
        
        return status
    
    async def get_coingecko_gaming_data(self) -> Dict[str, Any]:
        """Get gaming token data from CoinGecko"""
        if 'coingecko' not in self.clients:
            logger.warning("CoinGecko client not available")
            return {}

        cache_key = "coingecko_gaming_data"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        try:
            client = self.clients['coingecko']

            # Get gaming tokens data
            gaming_tokens = await client.get_gaming_tokens_data()

            # Get gaming categories data
            categories_data = await client.get_gaming_categories_data()

            # Get trending gaming tokens
            trending_data = await client.get_trending_gaming_tokens()

            coingecko_data = {
                'gaming_tokens': [
                    {
                        'id': token.id,
                        'symbol': token.symbol,
                        'name': token.name,
                        'current_price': token.current_price,
                        'market_cap': token.market_cap,
                        'price_change_24h': token.price_change_24h,
                        'volume_24h': token.volume_24h,
                        'last_updated': token.last_updated
                    }
                    for token in gaming_tokens
                ],
                'categories_data': categories_data,
                'trending_gaming': trending_data,
                'total_gaming_tokens': len(gaming_tokens),
                'collection_timestamp': datetime.now().isoformat()
            }

            self._cache_data(cache_key, coingecko_data)
            logger.info(f"Retrieved {len(gaming_tokens)} gaming tokens from CoinGecko")

            return coingecko_data

        except Exception as e:
            logger.error(f"Failed to get CoinGecko gaming data: {e}")
            return {}

    def clear_cache(self):
        """Clear all cached data"""
        self.cache.clear()
        self.last_update.clear()
        logger.info("Blockchain data cache cleared")


# Global instance
blockchain_data_manager = BlockchainDataManager()
