"""
Gaming Protocol Analytics System
Comprehensive on-chain gaming data collection and analytics
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import sys
import os

# Add the config directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.gaming_config import gaming_project_manager, GamingProjectConfig

from .multi_chain_client import multi_chain_manager
from .data_clients.manager import BlockchainDataManager
from .gaming_contracts import gaming_contract_manager
from .market_data import gaming_market_data
from services.tvl_tracker import tvl_tracker
from services.user_activity_tracker import user_activity_tracker
from services.p2e_economics_tracker import p2e_economics_tracker
from services.nft_floor_tracker import nft_floor_tracker
from models.gaming import GamingProject, BlockchainData
from models.base import SessionLocal
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of gaming protocol metrics"""
    USER_ACTIVITY = "user_activity"
    TRANSACTION_VOLUME = "transaction_volume"
    TOKEN_METRICS = "token_metrics"
    NFT_ACTIVITY = "nft_activity"
    TVL = "total_value_locked"
    PROTOCOL_HEALTH = "protocol_health"
    P2E_ECONOMICS = "p2e_economics"


@dataclass
class GamingProtocolMetrics:
    """Gaming protocol metrics data structure"""
    protocol_name: str
    chain: str
    timestamp: datetime
    
    # User metrics
    daily_active_users: Optional[int] = None
    monthly_active_users: Optional[int] = None
    new_users_24h: Optional[int] = None
    user_retention_rate: Optional[float] = None
    
    # Transaction metrics
    transaction_count_24h: Optional[int] = None
    transaction_volume_24h: Optional[float] = None
    average_transaction_value: Optional[float] = None
    gas_fees_24h: Optional[float] = None
    
    # Token metrics
    token_price: Optional[float] = None
    token_price_change_24h: Optional[float] = None
    market_cap: Optional[float] = None
    trading_volume_24h: Optional[float] = None
    circulating_supply: Optional[float] = None
    
    # NFT metrics
    nft_trades_24h: Optional[int] = None
    nft_volume_24h: Optional[float] = None
    floor_price: Optional[float] = None
    unique_holders: Optional[int] = None
    
    # Protocol-specific metrics
    total_value_locked: Optional[float] = None
    protocol_revenue_24h: Optional[float] = None
    staking_rewards_distributed: Optional[float] = None
    
    # P2E economics
    average_earnings_per_user: Optional[float] = None
    reward_token_distribution: Optional[float] = None
    gameplay_sessions_24h: Optional[int] = None
    
    # Health indicators
    protocol_uptime: Optional[float] = None
    smart_contract_interactions: Optional[int] = None
    developer_activity_score: Optional[float] = None
    
    # Additional metadata
    raw_data: Dict[str, Any] = field(default_factory=dict)


class GamingProtocolAnalytics:
    """Main gaming protocol analytics system"""
    
    def __init__(self):
        self.blockchain_data_manager = None  # Lazy initialization
        self.gaming_market_data = None  # Lazy initialization
        self.metrics_cache: Dict[str, GamingProtocolMetrics] = {}
        self.cache_duration = timedelta(minutes=30)
        self.supported_protocols = self._load_supported_protocols()
        self._initialized = False

        # Circuit breaker for failed APIs
        self.failed_apis = set()
        self.api_failure_counts = {}
        self.max_failures = 3
        self.failure_reset_time = timedelta(minutes=15)

        # Background task management
        self.background_task = None
        # Use settings for background refresh interval (30 minutes)
        from config.settings import get_settings
        settings = get_settings()
        self.background_refresh_interval = settings.gaming_analytics_interval  # 30 minutes
        self.is_background_running = False
        
    def _load_supported_protocols(self) -> Dict[str, Dict[str, Any]]:
        """Load supported gaming protocols configuration from CSV data"""
        logger.info("🔄 Loading gaming protocols from CSV data...")

        protocols = {}

        # Get all enabled projects from CSV
        enabled_projects = gaming_project_manager.get_enabled_projects()

        for project_key, project in enabled_projects.items():
            if project.has_tokens() or project.has_nfts():
                protocol_config = self._create_protocol_config(project_key, project)
                if protocol_config:
                    protocols[project_key] = protocol_config

        # Add fallback protocols if CSV is empty
        if not protocols:
            logger.warning("⚠️ No protocols found in CSV, loading fallback protocols")
            protocols = self._get_fallback_protocols()

        logger.info(f"✅ Loaded {len(protocols)} gaming protocols from CSV data")
        return protocols

    def _create_protocol_config(self, project_key: str, project: GamingProjectConfig) -> Optional[Dict[str, Any]]:
        """Create protocol configuration from project data"""
        try:
            # Get primary token symbol
            primary_token = project.get_primary_token()
            token_symbol = primary_token.symbol if primary_token else project.project_name[:4].upper()

            # Get blockchain list
            chains = project.get_blockchain_list()
            if not chains:
                logger.warning(f"⚠️ No blockchain specified for {project.project_name}")
                return None

            # Build contracts configuration
            contracts = {}
            for chain in chains:
                chain_contracts = {}

                # Add token contracts
                for i, token in enumerate(project.tokens):
                    if token.contract_address:
                        key = 'token' if i == 0 else f'token_{i+1}'
                        chain_contracts[key] = token.contract_address

                # Add NFT contracts
                for i, nft in enumerate(project.nfts):
                    if nft.contract_address:
                        key = 'nft' if i == 0 else f'nft_{i+1}'
                        chain_contracts[key] = nft.contract_address

                if chain_contracts:
                    contracts[chain] = chain_contracts

            # Determine metrics sources based on blockchain
            metrics_sources = self._get_metrics_sources_for_chains(chains)

            return {
                'name': project.project_name,
                'token_symbol': token_symbol,
                'chains': chains,
                'contracts': contracts,
                'metrics_sources': metrics_sources,
                'website': project.website,
                'status': project.status,
                'genre': project.genre,
                'daily_active_users': project.daily_active_users,
                'daily_unique_wallets': project.daily_unique_wallets,
                'enabled': project.enabled
            }

        except Exception as e:
            logger.error(f"❌ Failed to create protocol config for {project.project_name}: {e}")
            return None

    def _get_metrics_sources_for_chains(self, chains: List[str]) -> List[str]:
        """Get appropriate metrics sources based on blockchain chains"""
        sources = set()

        for chain in chains:
            if chain in ['ethereum', 'polygon', 'bsc', 'avalanche', 'arbitrum', 'optimism']:
                sources.update(['flipside', 'dextools', 'cryptorank'])
            elif chain in ['solana']:
                sources.update(['flipside', 'cryptorank'])
            elif chain in ['ronin']:
                sources.update(['cryptorank'])
            else:
                sources.add('cryptorank')  # Default fallback

        return list(sources)

    def _get_fallback_protocols(self) -> Dict[str, Dict[str, Any]]:
        """Get fallback protocols when CSV data is not available"""
        logger.warning("⚠️ Using minimal fallback protocols - CSV data should be loaded instead")
        return {
            'axie-infinity': {
                'name': 'Axie Infinity',
                'token_symbol': 'AXS',
                'chains': ['ethereum', 'ronin'],
                'contracts': {
                    'ethereum': {
                        'token': '******************************************',  # AXS
                    }
                },
                'metrics_sources': ['cryptorank'],
                'website': 'https://axieinfinity.com',
                'status': 'live',
                'genre': 'strategy',
                'daily_active_users': None,
                'daily_unique_wallets': None,
                'enabled': True
            }
        }

    def reload_protocols_from_csv(self):
        """Reload protocols from CSV data"""
        logger.info("🔄 Reloading gaming protocols from CSV...")
        self.supported_protocols = self._load_supported_protocols()
        logger.info(f"✅ Reloaded {len(self.supported_protocols)} protocols")

    def get_enabled_protocols(self) -> Dict[str, Dict[str, Any]]:
        """Get only enabled protocols"""
        return {k: v for k, v in self.supported_protocols.items()
                if v.get('enabled', True)}

    def get_live_protocols(self) -> Dict[str, Dict[str, Any]]:
        """Get only live/active protocols"""
        live_statuses = ['live', 'beta - closer to its final form']
        return {k: v for k, v in self.supported_protocols.items()
                if v.get('status', '').lower() in live_statuses and v.get('enabled', True)}

    def get_protocols_by_chain(self, chain: str) -> Dict[str, Dict[str, Any]]:
        """Get protocols that operate on a specific blockchain"""
        return {k: v for k, v in self.supported_protocols.items()
                if chain in v.get('chains', []) and v.get('enabled', True)}

    def get_protocol_token_symbols(self) -> List[str]:
        """Get all unique token symbols from protocols"""
        symbols = set()
        for protocol in self.supported_protocols.values():
            if protocol.get('enabled', True) and protocol.get('token_symbol'):
                symbols.add(protocol['token_symbol'])
        return list(symbols)

    def get_protocol_contract_addresses(self) -> List[str]:
        """Get all unique contract addresses from protocols"""
        addresses = set()
        for protocol in self.supported_protocols.values():
            if protocol.get('enabled', True):
                contracts = protocol.get('contracts', {})
                for chain_contracts in contracts.values():
                    for address in chain_contracts.values():
                        if address:
                            addresses.add(address)
        return list(addresses)

    async def _ensure_initialized(self):
        """Ensure the analytics system is initialized"""
        if not self._initialized:
            logger.info("🎮 Initializing Gaming Protocol Analytics...")

            # Initialize blockchain data manager
            if self.blockchain_data_manager is None:
                self.blockchain_data_manager = BlockchainDataManager()
                await self.blockchain_data_manager.initialize_clients()

            # Initialize gaming market data
            if self.gaming_market_data is None:
                from .market_data import gaming_market_data
                self.gaming_market_data = gaming_market_data

            self._initialized = True
            logger.info(f"✅ Gaming Protocol Analytics initialized with {len(self.supported_protocols)} protocols")

    def _handle_api_failure(self, api_name: str):
        """Handle API failure with circuit breaker pattern"""
        self.api_failure_counts[api_name] = self.api_failure_counts.get(api_name, 0) + 1

        if self.api_failure_counts[api_name] >= self.max_failures:
            self.failed_apis.add(api_name)
            logger.warning(f"🚫 API {api_name} marked as failed after {self.max_failures} failures")

    def _is_api_available(self, api_name: str) -> bool:
        """Check if API is available (not in circuit breaker state)"""
        if api_name not in self.failed_apis:
            return True

        # Check if we should reset the failure state
        if api_name in self.api_failure_counts:
            # For simplicity, reset after failure_reset_time (would need timestamp tracking in production)
            return False

        return True

    async def _background_cache_refresh(self):
        """Background task to refresh cache periodically and collect historical data"""
        while self.is_background_running:
            try:
                logger.info("🔄 Background cache refresh starting...")

                # Refresh cache
                updated_metrics = await self.collect_all_protocols_metrics()
                logger.info("✅ Background cache refresh completed")

                # Broadcast updates via WebSocket (import here to avoid circular imports)
                try:
                    from api.websocket_manager import manager

                    # Broadcast dashboard update
                    dashboard_data = await self.get_dashboard_summary()
                    await manager.broadcast_gaming_analytics_update(dashboard_data)

                    # Broadcast individual protocol updates
                    for protocol_name, metrics in updated_metrics.items():
                        if metrics:
                            await manager.broadcast_protocol_update(protocol_name, metrics.__dict__)

                    logger.info("📡 WebSocket updates broadcasted")
                except Exception as e:
                    logger.error(f"⚠️ WebSocket broadcast failed: {e}")

                # Collect historical data (import here to avoid circular imports)
                try:
                    from services.historical_data_collector import historical_data_collector
                    await historical_data_collector.collect_current_metrics()
                    logger.info("📊 Historical data collection completed")
                except Exception as e:
                    logger.error(f"⚠️ Historical data collection failed: {e}")

                # Wait for next refresh
                await asyncio.sleep(self.background_refresh_interval)

            except Exception as e:
                logger.error(f"❌ Background cache refresh failed: {e}")
                # Wait a bit before retrying
                await asyncio.sleep(60)

    def start_background_refresh(self):
        """Start background cache refresh task"""
        if not self.is_background_running:
            self.is_background_running = True
            self.background_task = asyncio.create_task(self._background_cache_refresh())
            logger.info("🚀 Background cache refresh started")

    def stop_background_refresh(self):
        """Stop background cache refresh task"""
        self.is_background_running = False
        if self.background_task:
            self.background_task.cancel()
            logger.info("🛑 Background cache refresh stopped")

    async def initialize(self):
        """Initialize the analytics system"""
        await self._ensure_initialized()
        # Start background refresh for historical data collection
        if not self.is_background_running:
            self.start_background_refresh()
    
    async def collect_protocol_metrics(self, protocol_name: str) -> Optional[GamingProtocolMetrics]:
        """Collect comprehensive metrics for a gaming protocol"""
        await self._ensure_initialized()

        if protocol_name not in self.supported_protocols:
            logger.warning(f"Protocol {protocol_name} not supported")
            return None
        
        # Check cache first
        cache_key = f"{protocol_name}_{datetime.now().strftime('%Y%m%d_%H')}"
        if cache_key in self.metrics_cache:
            cached_metrics = self.metrics_cache[cache_key]
            if datetime.now() - cached_metrics.timestamp < self.cache_duration:
                return cached_metrics
        
        protocol_config = self.supported_protocols[protocol_name]
        logger.info(f"📊 Collecting metrics for {protocol_config['name']}...")
        
        # Initialize metrics object
        metrics = GamingProtocolMetrics(
            protocol_name=protocol_name,
            chain=protocol_config['chains'][0],  # Primary chain
            timestamp=datetime.now()
        )
        
        # Collect metrics from different sources with individual timeouts
        try:
            # Collect token metrics (most important, uses CryptoRank)
            await asyncio.wait_for(
                self._collect_token_metrics(protocol_name, protocol_config, metrics),
                timeout=10.0
            )
        except Exception as e:
            logger.warning(f"Token metrics collection failed for {protocol_name}: {e}")

        try:
            # Collect protocol health metrics (quick, uses RPC)
            await asyncio.wait_for(
                self._collect_protocol_health_metrics(protocol_name, protocol_config, metrics),
                timeout=5.0
            )
        except Exception as e:
            logger.warning(f"Protocol health metrics collection failed for {protocol_name}: {e}")

        # Skip user activity and NFT metrics for now since they depend on external APIs
        # These can be re-enabled when API access is stable
        # await self._collect_user_activity_metrics(protocol_name, protocol_config, metrics)
        # await self._collect_nft_metrics(protocol_name, protocol_config, metrics)

        try:
            
            # Cache the results
            self.metrics_cache[cache_key] = metrics
            
            logger.info(f"✅ Collected metrics for {protocol_config['name']}")
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Failed to collect metrics for {protocol_name}: {e}")
            return None
    
    async def _collect_token_metrics(self, protocol_name: str, config: Dict, metrics: GamingProtocolMetrics):
        """Collect token-related metrics"""
        try:
            primary_chain = config['chains'][0]
            contracts = config['contracts'].get(primary_chain, {})
            
            if 'token' in contracts:
                token_address = contracts['token']
                
                # Get token price and market data with circuit breaker
                if self._is_api_available('cryptorank'):
                    token_symbol = config.get('token_symbol', '')
                    token_data = await self.gaming_market_data.get_token_price(primary_chain, token_address, token_symbol)
                    if token_data:
                        metrics.token_price = token_data.price_usd
                        metrics.token_price_change_24h = token_data.price_change_24h
                        metrics.market_cap = getattr(token_data, 'market_cap', None)
                        metrics.trading_volume_24h = getattr(token_data, 'volume_24h', None)
                        metrics.circulating_supply = getattr(token_data, 'circulating_supply', None)
                        logger.debug(f"✅ Token metrics collected for {protocol_name}: ${token_data.price_usd:.2f}")
                    else:
                        logger.warning(f"⚠️ No token data available for {protocol_name}")
                        self._handle_api_failure('cryptorank')
                else:
                    logger.warning(f"⚠️ CryptoRank API unavailable for {protocol_name} (circuit breaker)")
                    # Set default values when API is unavailable
                    metrics.token_price = 0.0
                    metrics.token_price_change_24h = 0.0
                
                # Get additional token metrics from blockchain data clients
                if 'flipside' in self.blockchain_data_manager.clients:
                    try:
                        flipside_data = await self.blockchain_data_manager.get_gaming_tokens_data([token_address])
                        if flipside_data and 'flipside' in flipside_data:
                            token_metrics = flipside_data['flipside']
                            metrics.transaction_count_24h = token_metrics.get('transaction_count_24h')
                            metrics.transaction_volume_24h = token_metrics.get('volume_24h_usd')
                            metrics.unique_holders = token_metrics.get('unique_holders')
                    except Exception as e:
                        logger.debug(f"Flipside token metrics failed for {protocol_name}: {e}")
                
        except Exception as e:
            logger.error(f"Failed to collect token metrics for {protocol_name}: {e}")

    async def _collect_user_activity_metrics(self, protocol_name: str, config: Dict, metrics: GamingProtocolMetrics):
        """Collect user activity metrics"""
        try:
            # Get user activity data from blockchain data clients
            if 'flipside' in self.blockchain_data_manager.clients:
                try:
                    protocol_data = await self.blockchain_data_manager.get_gaming_protocol_metrics(protocol_name)
                    if protocol_data and 'flipside' in protocol_data:
                        flipside_metrics = protocol_data['flipside']
                        metrics.daily_active_users = flipside_metrics.get('daily_active_users')
                        metrics.monthly_active_users = flipside_metrics.get('monthly_active_users')
                        metrics.new_users_24h = flipside_metrics.get('new_users_24h')
                        metrics.user_retention_rate = flipside_metrics.get('user_retention_rate')
                        metrics.gameplay_sessions_24h = flipside_metrics.get('gameplay_sessions_24h')
                except Exception as e:
                    logger.debug(f"Flipside user metrics failed for {protocol_name}: {e}")

            # Get on-chain activity from direct blockchain analysis
            primary_chain = config['chains'][0]
            contracts = config['contracts'].get(primary_chain, {})

            if contracts:
                # Analyze recent transactions for user activity
                chain_client = multi_chain_manager.get_client(primary_chain)
                if chain_client:
                    # Get recent blocks and analyze unique addresses
                    try:
                        latest_block = await chain_client.get_latest_block_number()
                        if latest_block:
                            # Analyze last 24 hours of blocks (approximate)
                            blocks_per_day = 7200 if primary_chain == 'ethereum' else 28800  # Rough estimates
                            start_block = max(1, latest_block - blocks_per_day)

                            unique_addresses = set()
                            transaction_count = 0

                            # Sample every 100th block to avoid overwhelming the RPC
                            for block_num in range(start_block, latest_block, 100):
                                try:
                                    block_data = await chain_client.get_block(block_num)
                                    if block_data and 'transactions' in block_data:
                                        for tx in block_data['transactions']:
                                            if isinstance(tx, dict):
                                                # Check if transaction involves our contracts
                                                tx_to = tx.get('to', '').lower()
                                                if any(addr.lower() == tx_to for addr in contracts.values()):
                                                    unique_addresses.add(tx.get('from', '').lower())
                                                    transaction_count += 1
                                except Exception as e:
                                    logger.debug(f"Block analysis failed for block {block_num}: {e}")
                                    continue

                            if not metrics.daily_active_users and unique_addresses:
                                # Estimate daily active users based on sampled data
                                estimated_dau = len(unique_addresses) * 100  # Scale up from sampling
                                metrics.daily_active_users = estimated_dau

                            if not metrics.transaction_count_24h and transaction_count:
                                estimated_tx_count = transaction_count * 100  # Scale up from sampling
                                metrics.transaction_count_24h = estimated_tx_count

                    except Exception as e:
                        logger.debug(f"On-chain analysis failed for {protocol_name}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect user activity metrics for {protocol_name}: {e}")

    async def _collect_nft_metrics(self, protocol_name: str, config: Dict, metrics: GamingProtocolMetrics):
        """Collect NFT-related metrics"""
        try:
            primary_chain = config['chains'][0]
            contracts = config['contracts'].get(primary_chain, {})

            # Look for NFT contracts (land, assets, cards, etc.)
            nft_contracts = {k: v for k, v in contracts.items() if k in ['nft', 'land', 'assets', 'cards']}

            if nft_contracts:
                for contract_type, contract_address in nft_contracts.items():
                    try:
                        # Get NFT collection data
                        nft_data = await self.blockchain_data_manager.get_nft_collection_data(contract_address)

                        if nft_data:
                            for source, data in nft_data.items():
                                if isinstance(data, dict):
                                    # Extract NFT metrics
                                    if not metrics.floor_price and 'floor_price' in data:
                                        metrics.floor_price = data['floor_price']

                                    if not metrics.nft_volume_24h and 'volume_24h' in data:
                                        metrics.nft_volume_24h = data['volume_24h']

                                    if not metrics.nft_trades_24h and 'trades_24h' in data:
                                        metrics.nft_trades_24h = data['trades_24h']

                                    if not metrics.unique_holders and 'unique_holders' in data:
                                        metrics.unique_holders = data['unique_holders']

                    except Exception as e:
                        logger.debug(f"NFT metrics failed for {contract_address}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect NFT metrics for {protocol_name}: {e}")

    async def _collect_protocol_health_metrics(self, protocol_name: str, config: Dict, metrics: GamingProtocolMetrics):
        """Collect protocol health and performance metrics"""
        try:
            # Test protocol uptime by checking RPC connectivity
            uptime_checks = []
            for chain in config['chains']:
                chain_client = multi_chain_manager.get_client(chain)
                if chain_client:
                    try:
                        is_connected = await chain_client.test_connection()
                        uptime_checks.append(1.0 if is_connected else 0.0)
                    except Exception:
                        uptime_checks.append(0.0)

            if uptime_checks:
                metrics.protocol_uptime = sum(uptime_checks) / len(uptime_checks)

            # Get smart contract interaction count
            primary_chain = config['chains'][0]
            contracts = config['contracts'].get(primary_chain, {})

            if contracts and metrics.transaction_count_24h:
                # Use transaction count as proxy for smart contract interactions
                metrics.smart_contract_interactions = metrics.transaction_count_24h

            # Calculate developer activity score based on recent updates
            # This is a simplified metric - could be enhanced with GitHub API integration
            metrics.developer_activity_score = 0.8  # Default score - can be enhanced with real data

            # Calculate TVL if we have DeFi components
            if protocol_name in ['axie-infinity', 'the-sandbox', 'decentraland']:
                try:
                    # Get TVL data from DeFi protocols associated with gaming
                    tvl_data = await self._get_protocol_tvl(protocol_name, config)
                    if tvl_data:
                        metrics.total_value_locked = tvl_data
                except Exception as e:
                    logger.debug(f"TVL calculation failed for {protocol_name}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect protocol health metrics for {protocol_name}: {e}")

    async def _get_protocol_tvl(self, protocol_name: str, config: Dict) -> Optional[float]:
        """Calculate Total Value Locked for gaming protocol"""
        try:
            # This would integrate with DeFi TVL APIs like DefiLlama
            # For now, return estimated values based on known protocols
            tvl_estimates = {
                'axie-infinity': 45000000.0,  # $45M estimated
                'the-sandbox': 25000000.0,   # $25M estimated
                'decentraland': 15000000.0,  # $15M estimated
            }
            return tvl_estimates.get(protocol_name)
        except Exception as e:
            logger.error(f"TVL calculation failed for {protocol_name}: {e}")
            return None

    async def collect_all_protocols_metrics(self) -> Dict[str, GamingProtocolMetrics]:
        """Collect metrics for all supported protocols with optimized caching"""
        logger.info("📊 Collecting metrics for all gaming protocols...")

        results = {}

        # Check cache first
        cache_key = "all_protocols_summary"
        cached_result = self.metrics_cache.get(cache_key)
        if cached_result and datetime.now() - cached_result.timestamp < self.cache_duration:
            logger.info("✅ Using cached all protocols metrics")
            # Return cached data as dict
            cached_dict = {}
            for protocol_name in self.supported_protocols.keys():
                protocol_cache_key = f"{protocol_name}_metrics"
                if protocol_cache_key in self.metrics_cache:
                    cached_dict[protocol_name] = self.metrics_cache[protocol_cache_key]
            if cached_dict:
                return cached_dict

        # Collect fresh data with limited concurrency to avoid overwhelming APIs
        semaphore = asyncio.Semaphore(3)  # Limit to 3 concurrent requests

        async def collect_with_semaphore(protocol_name):
            async with semaphore:
                return await self.collect_protocol_metrics(protocol_name)

        tasks = [collect_with_semaphore(protocol_name) for protocol_name in self.supported_protocols.keys()]

        try:
            # Add timeout to prevent hanging
            metrics_list = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=90.0  # 90 second timeout for all protocols
            )

            for i, protocol_name in enumerate(self.supported_protocols.keys()):
                if i < len(metrics_list) and not isinstance(metrics_list[i], Exception):
                    results[protocol_name] = metrics_list[i]
                else:
                    logger.warning(f"Failed to collect metrics for {protocol_name}")

            logger.info(f"✅ Collected metrics for {len(results)} protocols")
            return results

        except Exception as e:
            logger.error(f"❌ Failed to collect all protocol metrics: {e}")
            return {}

    async def get_protocol_summary(self) -> Dict[str, Any]:
        """Get summary of all gaming protocol metrics"""
        all_metrics = await self.collect_all_protocols_metrics()

        summary = {
            'total_protocols': len(all_metrics),
            'total_market_cap': 0.0,
            'total_daily_active_users': 0,
            'total_transaction_volume_24h': 0.0,
            'total_nft_volume_24h': 0.0,
            'average_protocol_uptime': 0.0,
            'protocols': {}
        }

        uptime_values = []

        for protocol_name, metrics in all_metrics.items():
            if metrics:
                # Add to totals
                if metrics.market_cap:
                    summary['total_market_cap'] += metrics.market_cap
                if metrics.daily_active_users:
                    summary['total_daily_active_users'] += metrics.daily_active_users
                if metrics.transaction_volume_24h:
                    summary['total_transaction_volume_24h'] += metrics.transaction_volume_24h
                if metrics.nft_volume_24h:
                    summary['total_nft_volume_24h'] += metrics.nft_volume_24h
                if metrics.protocol_uptime:
                    uptime_values.append(metrics.protocol_uptime)

                # Add individual protocol summary
                summary['protocols'][protocol_name] = {
                    'name': self.supported_protocols[protocol_name]['name'],
                    'market_cap': metrics.market_cap,
                    'daily_active_users': metrics.daily_active_users,
                    'token_price': metrics.token_price,
                    'token_price_change_24h': metrics.token_price_change_24h,
                    'protocol_uptime': metrics.protocol_uptime,
                    'last_updated': metrics.timestamp.isoformat()
                }

        if uptime_values:
            summary['average_protocol_uptime'] = sum(uptime_values) / len(uptime_values)

        summary['last_updated'] = datetime.now().isoformat()
        return summary

    def clear_cache(self):
        """Clear all cached metrics"""
        self.metrics_cache.clear()
        logger.info("Gaming analytics cache cleared")

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status and performance metrics"""
        now = datetime.now()

        # Cache statistics
        fresh_cache_entries = sum(
            1 for metrics in self.metrics_cache.values()
            if now - metrics.timestamp < self.cache_duration
        )

        # API status
        available_apis = [api for api in ['cryptorank', 'flipside', 'bitquery'] if self._is_api_available(api)]

        return {
            "system_health": {
                "initialized": self._initialized,
                "background_refresh_running": self.is_background_running,
                "supported_protocols": len(self.supported_protocols),
                "available_apis": available_apis,
                "failed_apis": list(self.failed_apis)
            },
            "cache_metrics": {
                "total_cached_protocols": len(self.metrics_cache),
                "fresh_cache_entries": fresh_cache_entries,
                "stale_cache_entries": len(self.metrics_cache) - fresh_cache_entries,
                "cache_duration_minutes": self.cache_duration.total_seconds() / 60
            },
            "api_health": {
                "failure_counts": self.api_failure_counts,
                "circuit_breaker_active": len(self.failed_apis) > 0
            },
            "performance": {
                "background_refresh_interval_seconds": self.background_refresh_interval,
                "max_api_failures_before_circuit_break": self.max_failures
            },
            "timestamp": now.isoformat()
        }

    async def get_enhanced_protocol_analytics(self, protocol_name: str) -> Dict[str, Any]:
        """Get comprehensive enhanced analytics for a gaming protocol"""
        try:
            logger.info(f"🎮 Collecting enhanced analytics for {protocol_name}...")

            # Collect all analytics data concurrently
            tasks = [
                tvl_tracker.get_protocol_tvl(protocol_name),
                user_activity_tracker.get_protocol_user_activity(protocol_name),
                p2e_economics_tracker.get_protocol_token_economics(protocol_name),
                p2e_economics_tracker.get_earning_mechanics(protocol_name),
                nft_floor_tracker.get_all_collections_floor_data()
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            tvl_data = results[0] if not isinstance(results[0], Exception) else None
            user_activity = results[1] if not isinstance(results[1], Exception) else None
            token_economics = results[2] if not isinstance(results[2], Exception) else None
            earning_mechanics = results[3] if not isinstance(results[3], Exception) else None
            nft_data = results[4] if not isinstance(results[4], Exception) else None

            # Get NFT data for this protocol
            protocol_nft_data = {}
            if nft_data and protocol_name in nft_data:
                protocol_nft_data = nft_data[protocol_name]

            # Combine all analytics
            enhanced_analytics = {
                'protocol_name': protocol_name,
                'timestamp': datetime.now().isoformat(),
                'tvl_metrics': tvl_data.__dict__ if tvl_data else None,
                'user_activity_metrics': user_activity.__dict__ if user_activity else None,
                'token_economics': {
                    token: econ.__dict__ for token, econ in token_economics.items()
                } if token_economics else {},
                'earning_mechanics': earning_mechanics.__dict__ if earning_mechanics else None,
                'nft_collections': {
                    collection: data.__dict__ for collection, data in protocol_nft_data.items()
                } if protocol_nft_data else {},
                'summary_metrics': self._calculate_summary_metrics(
                    tvl_data, user_activity, token_economics, earning_mechanics, protocol_nft_data
                )
            }

            logger.info(f"✅ Enhanced analytics collected for {protocol_name}")
            return enhanced_analytics

        except Exception as e:
            logger.error(f"Error getting enhanced analytics for {protocol_name}: {e}")
            return {
                'protocol_name': protocol_name,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }

    def _calculate_summary_metrics(self, tvl_data, user_activity, token_economics, earning_mechanics, nft_data) -> Dict[str, Any]:
        """Calculate summary metrics from all analytics data"""
        try:
            summary = {
                'overall_health_score': 0.0,
                'economic_sustainability': 'unknown',
                'user_engagement': 'unknown',
                'market_performance': 'unknown',
                'nft_market_health': 'unknown'
            }

            health_factors = []

            # TVL health factor
            if tvl_data:
                tvl_change = tvl_data.tvl_change_24h
                if tvl_change > 5:
                    health_factors.append(80)
                elif tvl_change > 0:
                    health_factors.append(60)
                elif tvl_change > -10:
                    health_factors.append(40)
                else:
                    health_factors.append(20)

            # User activity health factor
            if user_activity:
                retention = user_activity.user_retention_24h
                if retention > 70:
                    health_factors.append(90)
                    summary['user_engagement'] = 'excellent'
                elif retention > 50:
                    health_factors.append(70)
                    summary['user_engagement'] = 'good'
                elif retention > 30:
                    health_factors.append(50)
                    summary['user_engagement'] = 'moderate'
                else:
                    health_factors.append(30)
                    summary['user_engagement'] = 'poor'

            # Economic sustainability
            if earning_mechanics:
                sustainability_score = earning_mechanics.sustainability_score
                if sustainability_score > 80:
                    health_factors.append(85)
                    summary['economic_sustainability'] = 'excellent'
                elif sustainability_score > 60:
                    health_factors.append(65)
                    summary['economic_sustainability'] = 'good'
                elif sustainability_score > 40:
                    health_factors.append(45)
                    summary['economic_sustainability'] = 'moderate'
                else:
                    health_factors.append(25)
                    summary['economic_sustainability'] = 'poor'

            # Token performance
            if token_economics:
                avg_price_change = sum(
                    econ.price_change_24h for econ in token_economics.values()
                ) / len(token_economics)

                if avg_price_change > 10:
                    health_factors.append(80)
                    summary['market_performance'] = 'bullish'
                elif avg_price_change > 0:
                    health_factors.append(60)
                    summary['market_performance'] = 'positive'
                elif avg_price_change > -10:
                    health_factors.append(40)
                    summary['market_performance'] = 'stable'
                else:
                    health_factors.append(20)
                    summary['market_performance'] = 'bearish'

            # NFT market health
            if nft_data:
                avg_floor_change = sum(
                    data.floor_change_24h for data in nft_data.values()
                ) / len(nft_data)

                if avg_floor_change > 15:
                    health_factors.append(85)
                    summary['nft_market_health'] = 'bullish'
                elif avg_floor_change > 0:
                    health_factors.append(65)
                    summary['nft_market_health'] = 'positive'
                elif avg_floor_change > -15:
                    health_factors.append(45)
                    summary['nft_market_health'] = 'stable'
                else:
                    health_factors.append(25)
                    summary['nft_market_health'] = 'bearish'

            # Calculate overall health score
            if health_factors:
                summary['overall_health_score'] = sum(health_factors) / len(health_factors)

            return summary

        except Exception as e:
            logger.error(f"Error calculating summary metrics: {e}")
            return {'error': str(e)}

    async def get_all_protocols_enhanced_analytics(self) -> Dict[str, Dict[str, Any]]:
        """Get enhanced analytics for all supported gaming protocols"""
        try:
            logger.info("🎮 Collecting enhanced analytics for all gaming protocols...")

            all_analytics = {}

            # Collect enhanced analytics for all protocols
            for protocol_name in self.supported_protocols.keys():
                try:
                    analytics = await self.get_enhanced_protocol_analytics(protocol_name)
                    all_analytics[protocol_name] = analytics
                except Exception as e:
                    logger.error(f"Error getting enhanced analytics for {protocol_name}: {e}")
                    all_analytics[protocol_name] = {
                        'protocol_name': protocol_name,
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    }

            logger.info(f"✅ Enhanced analytics collected for {len(all_analytics)} protocols")
            return all_analytics

        except Exception as e:
            logger.error(f"Error collecting all protocols enhanced analytics: {e}")
            return {}


# Create global instance
gaming_analytics = GamingProtocolAnalytics()
