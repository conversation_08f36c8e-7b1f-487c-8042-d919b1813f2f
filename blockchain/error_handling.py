"""
Error Handling and Retry Mechanisms for Blockchain Operations
Comprehensive error handling, circuit breakers, and monitoring
"""
import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import functools
import traceback

from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class ErrorType(Enum):
    """Types of blockchain errors"""
    CONNECTION_ERROR = "connection_error"
    TIMEOUT_ERROR = "timeout_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    INVALID_RESPONSE = "invalid_response"
    CONTRACT_ERROR = "contract_error"
    INSUFFICIENT_FUNDS = "insufficient_funds"
    NONCE_ERROR = "nonce_error"
    GAS_ERROR = "gas_error"
    REORG_ERROR = "reorg_error"
    API_ERROR = "api_error"
    UNKNOWN_ERROR = "unknown_error"


class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit is open, failing fast
    HALF_OPEN = "half_open"  # Testing if service is back


@dataclass
class ErrorMetrics:
    """Error tracking metrics"""
    total_errors: int = 0
    error_rate: float = 0.0
    last_error_time: Optional[datetime] = None
    error_types: Dict[ErrorType, int] = field(default_factory=dict)
    consecutive_errors: int = 0
    success_count: int = 0


@dataclass
class CircuitBreaker:
    """Circuit breaker for service protection"""
    name: str
    failure_threshold: int = 5
    recovery_timeout: int = 60  # seconds
    half_open_max_calls: int = 3
    
    state: CircuitState = CircuitState.CLOSED
    failure_count: int = 0
    last_failure_time: Optional[datetime] = None
    half_open_calls: int = 0
    
    def should_allow_request(self) -> bool:
        """Check if request should be allowed"""
        now = datetime.utcnow()
        
        if self.state == CircuitState.CLOSED:
            return True
        
        elif self.state == CircuitState.OPEN:
            if (self.last_failure_time and 
                (now - self.last_failure_time).total_seconds() > self.recovery_timeout):
                self.state = CircuitState.HALF_OPEN
                self.half_open_calls = 0
                return True
            return False
        
        elif self.state == CircuitState.HALF_OPEN:
            return self.half_open_calls < self.half_open_max_calls
        
        return False
    
    def record_success(self):
        """Record successful operation"""
        if self.state == CircuitState.HALF_OPEN:
            self.half_open_calls += 1
            if self.half_open_calls >= self.half_open_max_calls:
                self.state = CircuitState.CLOSED
                self.failure_count = 0
        elif self.state == CircuitState.CLOSED:
            self.failure_count = 0
    
    def record_failure(self):
        """Record failed operation"""
        self.failure_count += 1
        self.last_failure_time = datetime.utcnow()
        
        if self.state == CircuitState.HALF_OPEN:
            self.state = CircuitState.OPEN
        elif (self.state == CircuitState.CLOSED and 
              self.failure_count >= self.failure_threshold):
            self.state = CircuitState.OPEN


class RetryConfig:
    """Retry configuration"""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    def get_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt"""
        delay = self.base_delay * (self.exponential_base ** (attempt - 1))
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
        
        return delay


class BlockchainErrorHandler:
    """Handles blockchain operation errors with retry and circuit breaking"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.error_metrics: Dict[str, ErrorMetrics] = {}
        self.retry_configs: Dict[str, RetryConfig] = {}
        self._setup_default_configs()
    
    def _setup_default_configs(self):
        """Setup default retry configurations"""
        self.retry_configs = {
            'rpc_call': RetryConfig(max_attempts=3, base_delay=1.0),
            'api_call': RetryConfig(max_attempts=5, base_delay=2.0),
            'contract_call': RetryConfig(max_attempts=3, base_delay=1.5),
            'transaction': RetryConfig(max_attempts=2, base_delay=5.0),
            'metadata_fetch': RetryConfig(max_attempts=4, base_delay=2.0)
        }
    
    def get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for service"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker(
                name=service_name,
                failure_threshold=settings.blockchain.circuit_breaker_threshold,
                recovery_timeout=settings.blockchain.circuit_breaker_timeout
            )
        return self.circuit_breakers[service_name]
    
    def get_error_metrics(self, service_name: str) -> ErrorMetrics:
        """Get or create error metrics for service"""
        if service_name not in self.error_metrics:
            self.error_metrics[service_name] = ErrorMetrics()
        return self.error_metrics[service_name]
    
    def classify_error(self, error: Exception) -> ErrorType:
        """Classify error type"""
        error_str = str(error).lower()
        
        if 'timeout' in error_str or 'timed out' in error_str:
            return ErrorType.TIMEOUT_ERROR
        elif 'connection' in error_str or 'network' in error_str:
            return ErrorType.CONNECTION_ERROR
        elif 'rate limit' in error_str or '429' in error_str:
            return ErrorType.RATE_LIMIT_ERROR
        elif 'invalid response' in error_str or 'json' in error_str:
            return ErrorType.INVALID_RESPONSE
        elif 'contract' in error_str or 'revert' in error_str:
            return ErrorType.CONTRACT_ERROR
        elif 'insufficient funds' in error_str or 'balance' in error_str:
            return ErrorType.INSUFFICIENT_FUNDS
        elif 'nonce' in error_str:
            return ErrorType.NONCE_ERROR
        elif 'gas' in error_str:
            return ErrorType.GAS_ERROR
        elif 'reorg' in error_str or 'reorganization' in error_str:
            return ErrorType.REORG_ERROR
        elif 'api' in error_str or 'http' in error_str:
            return ErrorType.API_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR
    
    def should_retry(self, error_type: ErrorType) -> bool:
        """Determine if error type should be retried"""
        retryable_errors = {
            ErrorType.CONNECTION_ERROR,
            ErrorType.TIMEOUT_ERROR,
            ErrorType.RATE_LIMIT_ERROR,
            ErrorType.INVALID_RESPONSE,
            ErrorType.API_ERROR,
            ErrorType.UNKNOWN_ERROR
        }
        return error_type in retryable_errors
    
    def record_error(self, service_name: str, error: Exception):
        """Record error occurrence"""
        error_type = self.classify_error(error)
        metrics = self.get_error_metrics(service_name)
        circuit_breaker = self.get_circuit_breaker(service_name)
        
        # Update metrics
        metrics.total_errors += 1
        metrics.consecutive_errors += 1
        metrics.last_error_time = datetime.utcnow()
        
        if error_type not in metrics.error_types:
            metrics.error_types[error_type] = 0
        metrics.error_types[error_type] += 1
        
        # Calculate error rate
        total_operations = metrics.total_errors + metrics.success_count
        metrics.error_rate = metrics.total_errors / max(total_operations, 1)
        
        # Update circuit breaker
        circuit_breaker.record_failure()
        
        logger.error(f"Error in {service_name} ({error_type.value}): {error}")
    
    def record_success(self, service_name: str):
        """Record successful operation"""
        metrics = self.get_error_metrics(service_name)
        circuit_breaker = self.get_circuit_breaker(service_name)
        
        # Update metrics
        metrics.success_count += 1
        metrics.consecutive_errors = 0
        
        # Calculate error rate
        total_operations = metrics.total_errors + metrics.success_count
        metrics.error_rate = metrics.total_errors / max(total_operations, 1)
        
        # Update circuit breaker
        circuit_breaker.record_success()
    
    async def execute_with_retry(
        self,
        func: Callable,
        service_name: str,
        retry_config_name: str = 'rpc_call',
        *args,
        **kwargs
    ) -> Any:
        """Execute function with retry logic and circuit breaking"""
        circuit_breaker = self.get_circuit_breaker(service_name)
        retry_config = self.retry_configs.get(retry_config_name, self.retry_configs['rpc_call'])
        
        # Check circuit breaker
        if not circuit_breaker.should_allow_request():
            raise Exception(f"Circuit breaker open for {service_name}")
        
        last_error = None
        
        for attempt in range(1, retry_config.max_attempts + 1):
            try:
                # Execute function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Record success
                self.record_success(service_name)
                return result
                
            except Exception as error:
                last_error = error
                error_type = self.classify_error(error)
                
                # Record error
                self.record_error(service_name, error)
                
                # Check if we should retry
                if attempt == retry_config.max_attempts or not self.should_retry(error_type):
                    break
                
                # Calculate delay
                delay = retry_config.get_delay(attempt)
                
                # Special handling for rate limits
                if error_type == ErrorType.RATE_LIMIT_ERROR:
                    delay = max(delay, 5.0)  # Minimum 5 seconds for rate limits
                
                logger.warning(f"Attempt {attempt} failed for {service_name}, "
                             f"retrying in {delay:.2f}s: {error}")
                
                await asyncio.sleep(delay)
        
        # All attempts failed
        raise last_error
    
    def get_service_health(self, service_name: str) -> Dict[str, Any]:
        """Get health status for a service"""
        metrics = self.error_metrics.get(service_name, ErrorMetrics())
        circuit_breaker = self.circuit_breakers.get(service_name)
        
        health = {
            'service_name': service_name,
            'total_operations': metrics.total_errors + metrics.success_count,
            'success_count': metrics.success_count,
            'error_count': metrics.total_errors,
            'error_rate': metrics.error_rate,
            'consecutive_errors': metrics.consecutive_errors,
            'last_error_time': metrics.last_error_time.isoformat() if metrics.last_error_time else None,
            'error_types': {error_type.value: count for error_type, count in metrics.error_types.items()},
            'circuit_breaker': {
                'state': circuit_breaker.state.value if circuit_breaker else 'closed',
                'failure_count': circuit_breaker.failure_count if circuit_breaker else 0,
                'last_failure_time': circuit_breaker.last_failure_time.isoformat() if circuit_breaker and circuit_breaker.last_failure_time else None
            }
        }
        
        return health
    
    def get_overall_health(self) -> Dict[str, Any]:
        """Get overall health status"""
        services = list(set(list(self.error_metrics.keys()) + list(self.circuit_breakers.keys())))
        
        overall_health = {
            'total_services': len(services),
            'healthy_services': 0,
            'degraded_services': 0,
            'unhealthy_services': 0,
            'services': {}
        }
        
        for service_name in services:
            service_health = self.get_service_health(service_name)
            overall_health['services'][service_name] = service_health
            
            # Classify service health
            error_rate = service_health['error_rate']
            circuit_state = service_health['circuit_breaker']['state']
            
            if circuit_state == 'open' or error_rate > 0.5:
                overall_health['unhealthy_services'] += 1
            elif error_rate > 0.1 or circuit_state == 'half_open':
                overall_health['degraded_services'] += 1
            else:
                overall_health['healthy_services'] += 1
        
        return overall_health


# Decorator for automatic error handling
def with_error_handling(
    service_name: str,
    retry_config_name: str = 'rpc_call'
):
    """Decorator to add error handling to functions"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await error_handler.execute_with_retry(
                func, service_name, retry_config_name, *args, **kwargs
            )
        return wrapper
    return decorator


# Global error handler instance
error_handler = BlockchainErrorHandler()


# Example usage decorators
@with_error_handling('ethereum_rpc', 'rpc_call')
async def safe_rpc_call(client, method, *args):
    """Example of safe RPC call with error handling"""
    return await getattr(client, method)(*args)


@with_error_handling('metadata_api', 'metadata_fetch')
async def safe_metadata_fetch(url):
    """Example of safe metadata fetch with error handling"""
    import aiohttp
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.json()


# Health check endpoint helper
async def get_blockchain_health_status() -> Dict[str, Any]:
    """Get comprehensive blockchain service health status"""
    return error_handler.get_overall_health()
