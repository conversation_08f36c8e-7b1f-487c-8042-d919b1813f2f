"""
Gaming Protocol Event Monitoring System
Monitors blockchain events for gaming protocols, NFT transfers, and token activities
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

from web3 import Web3
from web3.contract import Contract
from web3.types import FilterParams, LogR<PERSON>eipt

from .multi_chain_client import multi_chain_manager, EVMChainClient
from .gaming_contracts import gaming_contract_manager, GamingContract, ContractType
from models.gaming import BlockchainData
from models.crud import create_blockchain_data
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class EventType(Enum):
    """Types of gaming events to monitor"""
    TOKEN_TRANSFER = "token_transfer"
    NFT_TRANSFER = "nft_transfer"
    NFT_MINT = "nft_mint"
    NFT_BURN = "nft_burn"
    MARKETPLACE_SALE = "marketplace_sale"
    STAKING_DEPOSIT = "staking_deposit"
    STAKING_WITHDRAW = "staking_withdraw"
    GAME_ACTION = "game_action"
    GUILD_ACTION = "guild_action"


@dataclass
class EventFilter:
    """Event filter configuration"""
    contract_address: str
    chain: str
    event_name: str
    event_type: EventType
    abi: List[Dict]
    from_block: int = 0
    to_block: str = "latest"
    is_active: bool = True
    last_processed_block: int = 0


@dataclass
class GameEvent:
    """Processed gaming event data"""
    event_type: EventType
    chain: str
    contract_address: str
    transaction_hash: str
    block_number: int
    block_timestamp: datetime
    event_data: Dict[str, Any]
    from_address: Optional[str] = None
    to_address: Optional[str] = None
    token_id: Optional[str] = None
    amount: Optional[float] = None
    gas_used: Optional[int] = None
    gas_price: Optional[float] = None


class GamingEventMonitor:
    """Monitors gaming protocol events across multiple chains"""
    
    def __init__(self):
        self.event_filters: Dict[str, EventFilter] = {}
        self.event_handlers: Dict[EventType, List[Callable]] = {}
        self.monitoring_active = False
        self.poll_interval = 30  # seconds
        self.max_blocks_per_query = 1000
        self._setup_default_filters()
    
    def _setup_default_filters(self):
        """Setup default event filters for popular gaming contracts"""
        # Get all gaming contracts
        gaming_tokens = gaming_contract_manager.get_gaming_tokens()
        gaming_nfts = gaming_contract_manager.get_gaming_nfts()
        
        # Setup token transfer filters
        for contract in gaming_tokens:
            self.add_token_transfer_filter(contract)
        
        # Setup NFT transfer filters
        for contract in gaming_nfts:
            self.add_nft_transfer_filter(contract)
    
    def add_token_transfer_filter(self, contract: GamingContract):
        """Add token transfer event filter"""
        filter_id = f"{contract.chain}:{contract.address}:transfer"
        
        event_filter = EventFilter(
            contract_address=contract.address,
            chain=contract.chain,
            event_name="Transfer",
            event_type=EventType.TOKEN_TRANSFER,
            abi=contract.abi
        )
        
        self.event_filters[filter_id] = event_filter
        logger.info(f"Added token transfer filter for {contract.name} on {contract.chain}")
    
    def add_nft_transfer_filter(self, contract: GamingContract):
        """Add NFT transfer event filter"""
        filter_id = f"{contract.chain}:{contract.address}:transfer"
        
        event_filter = EventFilter(
            contract_address=contract.address,
            chain=contract.chain,
            event_name="Transfer",
            event_type=EventType.NFT_TRANSFER,
            abi=contract.abi
        )
        
        self.event_filters[filter_id] = event_filter
        logger.info(f"Added NFT transfer filter for {contract.name} on {contract.chain}")
    
    def add_custom_event_filter(
        self,
        chain: str,
        contract_address: str,
        event_name: str,
        event_type: EventType,
        abi: List[Dict],
        from_block: int = 0
    ):
        """Add custom event filter"""
        filter_id = f"{chain}:{contract_address}:{event_name}"
        
        event_filter = EventFilter(
            contract_address=contract_address,
            chain=chain,
            event_name=event_name,
            event_type=event_type,
            abi=abi,
            from_block=from_block
        )
        
        self.event_filters[filter_id] = event_filter
        logger.info(f"Added custom event filter: {filter_id}")
    
    def register_event_handler(self, event_type: EventType, handler: Callable):
        """Register event handler for specific event type"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        logger.info(f"Registered event handler for {event_type.value}")
    
    async def start_monitoring(self):
        """Start event monitoring"""
        self.monitoring_active = True
        logger.info("Starting gaming event monitoring...")
        
        # Start monitoring tasks for each chain
        tasks = []
        chains = set(filter_config.chain for filter_config in self.event_filters.values())
        
        for chain in chains:
            task = asyncio.create_task(self._monitor_chain_events(chain))
            tasks.append(task)
        
        # Wait for all monitoring tasks
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Error in event monitoring: {e}")
        finally:
            self.monitoring_active = False
    
    async def stop_monitoring(self):
        """Stop event monitoring"""
        self.monitoring_active = False
        logger.info("Stopping gaming event monitoring...")
    
    async def _monitor_chain_events(self, chain: str):
        """Monitor events for a specific chain"""
        logger.info(f"Starting event monitoring for {chain}")
        
        client = multi_chain_manager.get_client(chain)
        if not isinstance(client, EVMChainClient):
            logger.warning(f"Event monitoring not supported for non-EVM chain: {chain}")
            return
        
        while self.monitoring_active:
            try:
                # Get chain-specific filters
                chain_filters = {
                    filter_id: filter_config
                    for filter_id, filter_config in self.event_filters.items()
                    if filter_config.chain == chain and filter_config.is_active
                }
                
                if not chain_filters:
                    await asyncio.sleep(self.poll_interval)
                    continue
                
                # Get latest block
                latest_block = await client.get_latest_block_number()
                if not latest_block:
                    await asyncio.sleep(self.poll_interval)
                    continue
                
                # Process each filter
                for filter_id, filter_config in chain_filters.items():
                    await self._process_event_filter(client, filter_config, latest_block)
                
                await asyncio.sleep(self.poll_interval)
                
            except Exception as e:
                logger.error(f"Error monitoring {chain} events: {e}")
                await asyncio.sleep(self.poll_interval)
    
    async def _process_event_filter(
        self,
        client: EVMChainClient,
        filter_config: EventFilter,
        latest_block: int
    ):
        """Process events for a specific filter"""
        try:
            # Determine block range
            from_block = max(filter_config.last_processed_block + 1, filter_config.from_block)
            to_block = min(from_block + self.max_blocks_per_query, latest_block)
            
            if from_block > to_block:
                return
            
            # Create contract instance
            contract = client.web3.eth.contract(
                address=Web3.toChecksumAddress(filter_config.contract_address),
                abi=filter_config.abi
            )
            
            # Get event filter
            event_filter = getattr(contract.events, filter_config.event_name).createFilter(
                fromBlock=from_block,
                toBlock=to_block
            )
            
            # Get events
            events = event_filter.get_all_entries()
            
            if events:
                logger.info(f"Found {len(events)} {filter_config.event_name} events "
                          f"for {filter_config.contract_address} on {filter_config.chain}")
                
                # Process each event
                for event in events:
                    await self._process_event(client, filter_config, event)
            
            # Update last processed block
            filter_config.last_processed_block = to_block
            
        except Exception as e:
            logger.error(f"Error processing event filter {filter_config.contract_address}: {e}")
    
    async def _process_event(
        self,
        client: EVMChainClient,
        filter_config: EventFilter,
        event: LogReceipt
    ):
        """Process a single event"""
        try:
            # Get transaction details
            tx = await client.get_transaction(event['transactionHash'].hex())
            if not tx:
                return
            
            # Get block details for timestamp
            block = await client.get_block(event['blockNumber'])
            if not block:
                return
            
            # Extract event data
            event_data = dict(event['args']) if 'args' in event else {}
            
            # Create GameEvent object
            game_event = GameEvent(
                event_type=filter_config.event_type,
                chain=filter_config.chain,
                contract_address=filter_config.contract_address,
                transaction_hash=event['transactionHash'].hex(),
                block_number=event['blockNumber'],
                block_timestamp=datetime.fromtimestamp(block['timestamp']),
                event_data=event_data,
                from_address=event_data.get('from') or tx.get('from'),
                to_address=event_data.get('to') or tx.get('to'),
                token_id=str(event_data.get('tokenId')) if 'tokenId' in event_data else None,
                amount=self._extract_amount(event_data),
                gas_used=tx.get('gas'),
                gas_price=float(tx.get('gasPrice', 0)) / 1e18 if tx.get('gasPrice') else None
            )
            
            # Store in database
            await self._store_event(game_event)
            
            # Call event handlers
            await self._call_event_handlers(game_event)
            
        except Exception as e:
            logger.error(f"Error processing event: {e}")
    
    def _extract_amount(self, event_data: Dict) -> Optional[float]:
        """Extract amount from event data"""
        # Try different common field names for amounts
        amount_fields = ['value', 'amount', 'tokens', 'wad']
        
        for field in amount_fields:
            if field in event_data:
                try:
                    # Convert from wei to ether for most tokens
                    return float(event_data[field]) / 1e18
                except (ValueError, TypeError):
                    continue
        
        return None
    
    async def _store_event(self, game_event: GameEvent):
        """Store event in database"""
        try:
            # Get gaming contract info
            contract = gaming_contract_manager.get_contract(
                game_event.chain,
                game_event.contract_address
            )
            
            # Create blockchain data entry
            blockchain_data = {
                'blockchain': game_event.chain,
                'block_number': game_event.block_number,
                'transaction_hash': game_event.transaction_hash,
                'contract_address': game_event.contract_address,
                'event_type': game_event.event_type.value,
                'event_data': game_event.event_data,
                'from_address': game_event.from_address,
                'to_address': game_event.to_address,
                'gas_used': game_event.gas_used,
                'gas_price': game_event.gas_price,
                'block_timestamp': game_event.block_timestamp,
                'nft_token_id': game_event.token_id,
                'token_amount': game_event.amount
            }
            
            if contract:
                blockchain_data['token_symbol'] = contract.token_symbol
                blockchain_data['nft_collection'] = contract.name
            
            # Store in database
            await create_blockchain_data(blockchain_data)
            
        except Exception as e:
            logger.error(f"Error storing event in database: {e}")
    
    async def _call_event_handlers(self, game_event: GameEvent):
        """Call registered event handlers"""
        handlers = self.event_handlers.get(game_event.event_type, [])
        
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(game_event)
                else:
                    handler(game_event)
            except Exception as e:
                logger.error(f"Error in event handler: {e}")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get monitoring status"""
        status = {
            'monitoring_active': self.monitoring_active,
            'total_filters': len(self.event_filters),
            'active_filters': sum(1 for f in self.event_filters.values() if f.is_active),
            'chains': list(set(f.chain for f in self.event_filters.values())),
            'event_types': list(set(f.event_type.value for f in self.event_filters.values())),
            'filters': {}
        }
        
        for filter_id, filter_config in self.event_filters.items():
            status['filters'][filter_id] = {
                'chain': filter_config.chain,
                'contract_address': filter_config.contract_address,
                'event_name': filter_config.event_name,
                'event_type': filter_config.event_type.value,
                'is_active': filter_config.is_active,
                'last_processed_block': filter_config.last_processed_block
            }
        
        return status


# Global gaming event monitor instance
gaming_event_monitor = GamingEventMonitor()


# Example event handlers
async def log_token_transfer(event: GameEvent):
    """Log token transfer events"""
    logger.info(f"Token Transfer: {event.amount} tokens from {event.from_address} "
               f"to {event.to_address} on {event.chain}")


async def log_nft_transfer(event: GameEvent):
    """Log NFT transfer events"""
    logger.info(f"NFT Transfer: Token ID {event.token_id} from {event.from_address} "
               f"to {event.to_address} on {event.chain}")


# Register default event handlers
gaming_event_monitor.register_event_handler(EventType.TOKEN_TRANSFER, log_token_transfer)
gaming_event_monitor.register_event_handler(EventType.NFT_TRANSFER, log_nft_transfer)
