"""
Enhanced Blockchain RPC connection management with failover, load balancing, and health monitoring
"""
import asyncio
import logging
import time
import random
from typing import Dict, Optional, Any, List, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from web3 import Web3
from web3.middleware import geth_poa_middleware
import aiohttp
import json
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class RPCEndpoint:
    """RPC endpoint configuration with health tracking"""
    url: str
    chain: str
    priority: int = 1  # Lower number = higher priority
    is_healthy: bool = True
    last_check: datetime = field(default_factory=datetime.utcnow)
    response_time: float = 0.0
    error_count: int = 0
    success_count: int = 0
    
    @property
    def health_score(self) -> float:
        """Calculate health score based on success rate and response time"""
        if self.success_count + self.error_count == 0:
            return 1.0
        
        success_rate = self.success_count / (self.success_count + self.error_count)
        # Penalize slow response times (>2s gets penalty)
        time_penalty = max(0, (self.response_time - 2.0) / 10.0)
        return max(0, success_rate - time_penalty)


class EnhancedRPCManager:
    """Enhanced RPC manager with failover, load balancing, and health monitoring"""
    
    def __init__(self):
        self.connections: Dict[str, List[Web3]] = {}
        self.endpoints: Dict[str, List[RPCEndpoint]] = {}
        self.health_check_interval = 300  # 5 minutes
        self.last_health_check = {}
        self.circuit_breaker_threshold = 5  # Errors before circuit break
        self.circuit_breaker_timeout = 300  # 5 minutes
        self._initialize_endpoints()
        self._initialize_connections()
    
    def _initialize_endpoints(self):
        """Initialize RPC endpoints with multiple URLs per chain for redundancy"""
        # Define multiple RPC endpoints per chain for failover
        endpoint_configs = {
            'ethereum': [
                (settings.blockchain.ethereum_rpc_url, 1),
                ('https://eth-mainnet.alchemyapi.io/v2/demo', 2),
                ('https://mainnet.infura.io/v3/demo', 3),
            ],
            'polygon': [
                (settings.blockchain.polygon_rpc_url, 1),
                ('https://polygon-rpc.com', 2),
                ('https://rpc-mainnet.matic.network', 3),
            ],
            'bsc': [
                (settings.blockchain.bsc_rpc_url, 1),
                ('https://bsc-dataseed.binance.org', 2),
                ('https://bsc-dataseed1.defibit.io', 3),
            ],
            'arbitrum': [
                (settings.blockchain.arbitrum_rpc_url, 1),
                ('https://arb1.arbitrum.io/rpc', 2),
            ],
            'optimism': [
                (settings.blockchain.optimism_rpc_url, 1),
                ('https://mainnet.optimism.io', 2),
            ],
            'immutable': [
                (settings.blockchain.immutable_rpc_url, 1),
            ],
            'ronin': [
                (settings.blockchain.ronin_rpc_url, 1),
                ('https://api.roninchain.com/rpc', 2),
            ],
        }
        
        for chain, urls in endpoint_configs.items():
            self.endpoints[chain] = []
            for url, priority in urls:
                if url and url != "":  # Only add non-empty URLs
                    endpoint = RPCEndpoint(
                        url=url,
                        chain=chain,
                        priority=priority
                    )
                    self.endpoints[chain].append(endpoint)
            
            # Sort by priority (lower number = higher priority)
            self.endpoints[chain].sort(key=lambda x: x.priority)
    
    def _initialize_connections(self):
        """Initialize Web3 connections for each endpoint"""
        for chain, endpoints in self.endpoints.items():
            self.connections[chain] = []
            
            for endpoint in endpoints:
                try:
                    w3 = Web3(Web3.HTTPProvider(
                        endpoint.url,
                        request_kwargs={'timeout': settings.blockchain.request_timeout}
                    ))
                    
                    # Add PoA middleware for chains that need it
                    if chain in ['bsc', 'polygon', 'ronin']:
                        w3.middleware_onion.inject(geth_poa_middleware, layer=0)
                    
                    self.connections[chain].append(w3)
                    logger.info(f"✅ Initialized {chain} RPC connection: {endpoint.url}")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to initialize {chain} RPC {endpoint.url}: {e}")
                    endpoint.is_healthy = False
    
    async def get_best_connection(self, chain: str) -> Optional[Tuple[Web3, RPCEndpoint]]:
        """Get the best available connection for a chain based on health scores"""
        if chain not in self.endpoints:
            logger.error(f"Chain {chain} not configured")
            return None
        
        # Check if health check is needed
        await self._health_check_if_needed(chain)
        
        # Get healthy endpoints sorted by health score
        healthy_endpoints = [
            (conn, endpoint) for conn, endpoint in 
            zip(self.connections.get(chain, []), self.endpoints[chain])
            if endpoint.is_healthy
        ]
        
        if not healthy_endpoints:
            logger.warning(f"No healthy endpoints for {chain}, trying all endpoints")
            # If no healthy endpoints, try all endpoints as last resort
            healthy_endpoints = [
                (conn, endpoint) for conn, endpoint in 
                zip(self.connections.get(chain, []), self.endpoints[chain])
            ]
        
        if not healthy_endpoints:
            return None
        
        # Sort by health score (descending) and priority (ascending)
        healthy_endpoints.sort(key=lambda x: (-x[1].health_score, x[1].priority))
        
        return healthy_endpoints[0]
    
    async def _health_check_if_needed(self, chain: str):
        """Perform health check if enough time has passed"""
        now = datetime.utcnow()
        last_check = self.last_health_check.get(chain, datetime.min)
        
        if (now - last_check).total_seconds() > self.health_check_interval:
            await self._perform_health_check(chain)
            self.last_health_check[chain] = now
    
    async def _perform_health_check(self, chain: str):
        """Perform health check on all endpoints for a chain"""
        if chain not in self.endpoints:
            return
        
        logger.info(f"Performing health check for {chain}")
        
        for i, endpoint in enumerate(self.endpoints[chain]):
            try:
                connection = self.connections[chain][i]
                start_time = time.time()
                
                # Simple health check - get latest block number
                block_number = connection.eth.block_number
                response_time = time.time() - start_time
                
                # Update endpoint health metrics
                endpoint.response_time = response_time
                endpoint.success_count += 1
                endpoint.last_check = datetime.utcnow()
                
                # Mark as healthy if response time is reasonable
                endpoint.is_healthy = response_time < 10.0  # 10 second timeout
                
                logger.debug(f"Health check {chain} {endpoint.url}: "
                           f"block={block_number}, time={response_time:.2f}s")
                
            except Exception as e:
                endpoint.error_count += 1
                endpoint.last_check = datetime.utcnow()
                
                # Circuit breaker logic
                if endpoint.error_count >= self.circuit_breaker_threshold:
                    endpoint.is_healthy = False
                    logger.warning(f"Circuit breaker triggered for {chain} {endpoint.url}: {e}")
                else:
                    logger.debug(f"Health check failed for {chain} {endpoint.url}: {e}")
    
    async def get_latest_block(self, chain: str) -> Optional[int]:
        """Get latest block number with automatic failover"""
        connection_info = await self.get_best_connection(chain)
        if not connection_info:
            return None
        
        connection, endpoint = connection_info
        
        try:
            start_time = time.time()
            block_number = connection.eth.block_number
            response_time = time.time() - start_time
            
            # Update success metrics
            endpoint.success_count += 1
            endpoint.response_time = response_time
            
            return block_number
            
        except Exception as e:
            endpoint.error_count += 1
            logger.error(f"Error getting latest block for {chain}: {e}")
            
            # Try next best endpoint
            return await self._retry_with_fallback(chain, 'get_latest_block')
    
    async def _retry_with_fallback(self, chain: str, operation: str, *args, **kwargs):
        """Retry operation with fallback to other endpoints"""
        if chain not in self.endpoints:
            return None
        
        # Mark current best endpoint as unhealthy temporarily
        current_best = await self.get_best_connection(chain)
        if current_best:
            current_best[1].is_healthy = False
        
        # Try next best endpoint
        next_best = await self.get_best_connection(chain)
        if not next_best:
            return None
        
        connection, endpoint = next_best
        
        try:
            if operation == 'get_latest_block':
                return connection.eth.block_number
            elif operation == 'get_block':
                block_number = args[0]
                return dict(connection.eth.get_block(block_number, full_transactions=True))
            elif operation == 'get_transaction':
                tx_hash = args[0]
                return dict(connection.eth.get_transaction(tx_hash))
            # Add more operations as needed
            
        except Exception as e:
            endpoint.error_count += 1
            logger.error(f"Fallback failed for {chain} {operation}: {e}")
            return None
    
    async def get_block(self, chain: str, block_number: int) -> Optional[Dict]:
        """Get block data with automatic failover"""
        connection_info = await self.get_best_connection(chain)
        if not connection_info:
            return None
        
        connection, endpoint = connection_info
        
        try:
            block = connection.eth.get_block(block_number, full_transactions=True)
            endpoint.success_count += 1
            return dict(block)
            
        except Exception as e:
            endpoint.error_count += 1
            logger.error(f"Error getting block {block_number} for {chain}: {e}")
            return await self._retry_with_fallback(chain, 'get_block', block_number)
    
    async def get_transaction(self, chain: str, tx_hash: str) -> Optional[Dict]:
        """Get transaction data with automatic failover"""
        connection_info = await self.get_best_connection(chain)
        if not connection_info:
            return None
        
        connection, endpoint = connection_info
        
        try:
            tx = connection.eth.get_transaction(tx_hash)
            endpoint.success_count += 1
            return dict(tx)
            
        except Exception as e:
            endpoint.error_count += 1
            logger.error(f"Error getting transaction {tx_hash} for {chain}: {e}")
            return await self._retry_with_fallback(chain, 'get_transaction', tx_hash)
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all endpoints"""
        status = {}
        
        for chain, endpoints in self.endpoints.items():
            chain_status = {
                'total_endpoints': len(endpoints),
                'healthy_endpoints': sum(1 for ep in endpoints if ep.is_healthy),
                'endpoints': []
            }
            
            for endpoint in endpoints:
                ep_status = {
                    'url': endpoint.url,
                    'priority': endpoint.priority,
                    'is_healthy': endpoint.is_healthy,
                    'health_score': endpoint.health_score,
                    'response_time': endpoint.response_time,
                    'success_count': endpoint.success_count,
                    'error_count': endpoint.error_count,
                    'last_check': endpoint.last_check.isoformat()
                }
                chain_status['endpoints'].append(ep_status)
            
            status[chain] = chain_status
        
        return status


# Global enhanced RPC manager instance
enhanced_rpc_manager = EnhancedRPCManager()


async def test_enhanced_connections():
    """Test all enhanced RPC connections"""
    results = {}
    
    for chain in enhanced_rpc_manager.endpoints.keys():
        try:
            block_number = await enhanced_rpc_manager.get_latest_block(chain)
            results[chain] = block_number is not None
            if block_number:
                logger.info(f"✅ {chain}: Latest block {block_number}")
            else:
                logger.error(f"❌ {chain}: Failed to get latest block")
        except Exception as e:
            results[chain] = False
            logger.error(f"❌ {chain}: Connection test failed - {e}")
    
    return results
