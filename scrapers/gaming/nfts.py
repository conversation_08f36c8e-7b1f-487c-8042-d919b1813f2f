"""
NFT collections synchronization module
"""
import asyncio
import logging

logger = logging.getLogger(__name__)


async def sync_nft_collections():
    """Sync NFT collections data from blockchain data manager"""
    logger.info("Starting NFT collections sync")

    try:
        # Import blockchain data manager
        from blockchain.data_clients.manager import blockchain_data_manager

        # Get NFT collection data for major gaming collections
        from config.settings import get_settings
        settings = get_settings()

        gaming_nft_collections = [
            settings.gaming.axie_infinity_contract,
            settings.gaming.sandbox_contract,
            settings.gaming.decentraland_contract
        ]

        nft_data = {}
        for collection in gaming_nft_collections:
            if collection:
                collection_data = await blockchain_data_manager.get_nft_collection_data(collection)
                if collection_data:
                    nft_data[collection] = collection_data

        if nft_data:
            logger.info(f"Successfully synced {len(nft_data)} NFT collections")
            return nft_data
        else:
            logger.warning("No NFT collections data retrieved")
            return {}

    except Exception as e:
        logger.error(f"Error syncing NFT collections: {e}")
        return {}

    logger.info("NFT collections sync completed")
