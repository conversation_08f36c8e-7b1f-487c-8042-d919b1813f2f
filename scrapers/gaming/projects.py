"""
Gaming projects synchronization module
"""
import asyncio
import logging

logger = logging.getLogger(__name__)


async def sync_gaming_projects():
    """Sync gaming projects data from blockchain data manager"""
    logger.info("Starting gaming projects sync")

    try:
        # Import blockchain data manager
        from blockchain.data_clients.manager import blockchain_data_manager

        # Get comprehensive gaming data
        gaming_data = await blockchain_data_manager.get_comprehensive_gaming_data()

        if gaming_data:
            logger.info(f"Successfully synced gaming projects data: {len(gaming_data.get('gaming_tokens', []))} tokens")
            return gaming_data
        else:
            logger.warning("No gaming projects data retrieved")
            return {}

    except Exception as e:
        logger.error(f"Error syncing gaming projects: {e}")
        return {}

    logger.info("Gaming projects sync completed")
