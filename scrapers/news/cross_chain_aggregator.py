"""
Cross-Chain Gaming Content Aggregation System
Combines news from multiple chains and provides unified gaming content feed
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum
import asyncio

from .base import NewsItem
from .gaming_sources import blockchain_classifier, BlockchainNetwork, GamingCategory
from .entity_recognition import entity_engine

logger = logging.getLogger(__name__)


class ContentPriority(Enum):
    """Content priority levels"""
    CRITICAL = "critical"  # Major project launches, hacks, etc.
    HIGH = "high"         # Important updates, partnerships
    MEDIUM = "medium"     # Regular news, announcements
    LOW = "low"          # Minor updates, community posts


class AggregationStrategy(Enum):
    """Content aggregation strategies"""
    CHRONOLOGICAL = "chronological"
    RELEVANCE_BASED = "relevance_based"
    NETWORK_BALANCED = "network_balanced"
    PROJECT_FOCUSED = "project_focused"


@dataclass
class AggregatedContent:
    """Aggregated gaming content item"""
    news_item: NewsItem
    blockchain_classification: Dict[str, Any]
    entity_analysis: Dict[str, Any]
    priority_score: float
    relevance_score: float
    network_focus: str
    gaming_categories: List[str]
    detected_projects: List[str]
    cross_references: Dict[str, Any]
    aggregation_metadata: Dict[str, Any]


class CrossChainContentAggregator:
    """Main aggregation system for cross-chain gaming content"""
    
    def __init__(self):
        self.content_cache = {}
        self.network_weights = self._initialize_network_weights()
        self.category_weights = self._initialize_category_weights()
        self.project_importance = self._initialize_project_importance()
    
    def _initialize_network_weights(self) -> Dict[str, float]:
        """Initialize network importance weights"""
        return {
            BlockchainNetwork.ETHEREUM.value: 1.0,
            BlockchainNetwork.SOLANA.value: 0.9,  # High weight for Solana
            BlockchainNetwork.POLYGON.value: 0.8,
            BlockchainNetwork.BSC.value: 0.7,
            BlockchainNetwork.ARBITRUM.value: 0.7,
            BlockchainNetwork.OPTIMISM.value: 0.6,
            BlockchainNetwork.BASE.value: 0.6,
            BlockchainNetwork.AVALANCHE.value: 0.6,
            BlockchainNetwork.IMMUTABLE_X.value: 0.8,  # Gaming-focused
            BlockchainNetwork.RONIN.value: 0.7,       # Axie Infinity
            BlockchainNetwork.TON.value: 0.5,
            BlockchainNetwork.UNKNOWN.value: 0.3
        }
    
    def _initialize_category_weights(self) -> Dict[str, float]:
        """Initialize gaming category importance weights"""
        return {
            GamingCategory.P2E.value: 1.0,
            GamingCategory.NFT_GAMING.value: 0.9,
            GamingCategory.METAVERSE.value: 0.9,
            GamingCategory.DEFI_GAMING.value: 0.8,
            GamingCategory.GAMING_INFRASTRUCTURE.value: 0.8,
            GamingCategory.GAMING_TOKENS.value: 0.7,
            GamingCategory.GAMING_NFTS.value: 0.7,
            GamingCategory.ESPORTS.value: 0.6,
            GamingCategory.GENERAL_GAMING.value: 0.5
        }
    
    def _initialize_project_importance(self) -> Dict[str, float]:
        """Initialize project importance scores"""
        return {
            'axie_infinity': 1.0,
            'the_sandbox': 0.9,
            'decentraland': 0.9,
            'star_atlas': 0.8,      # Major Solana game
            'gala_games': 0.8,
            'enjin': 0.8,
            'immutable_x': 0.8,
            'aurory': 0.7,          # Solana game
            'genopets': 0.7,        # Solana game
            'solana_monkey_business': 0.6
        }
    
    def calculate_priority_score(self, content: NewsItem, 
                               blockchain_classification: Dict[str, Any],
                               entity_analysis: Dict[str, Any]) -> float:
        """Calculate content priority score"""
        base_score = 0.5
        
        # Network importance
        networks = blockchain_classification.get('blockchain_networks', [])
        network_score = max([self.network_weights.get(net, 0.3) for net in networks], default=0.3)
        
        # Category importance
        categories = blockchain_classification.get('gaming_categories', [])
        category_score = max([self.category_weights.get(cat, 0.5) for cat in categories], default=0.5)
        
        # Project importance
        projects = entity_analysis.get('detected_projects', [])
        project_score = max([self.project_importance.get(proj, 0.5) for proj in projects], default=0.5)
        
        # Recency boost (newer content gets higher priority)
        if content.published_at:
            hours_old = (datetime.now() - content.published_at).total_seconds() / 3600
            recency_multiplier = max(0.5, 1.0 - (hours_old / 168))  # Decay over a week
        else:
            recency_multiplier = 0.8
        
        # Solana boost (as requested by user)
        solana_boost = 1.2 if blockchain_classification.get('is_solana_focused', False) else 1.0
        
        # Multi-chain content boost
        multi_chain_boost = 1.1 if blockchain_classification.get('is_multi_chain', False) else 1.0
        
        # Calculate final score
        priority_score = (
            base_score * 0.2 +
            network_score * 0.25 +
            category_score * 0.25 +
            project_score * 0.3
        ) * recency_multiplier * solana_boost * multi_chain_boost
        
        return min(1.0, priority_score)  # Cap at 1.0
    
    def calculate_relevance_score(self, content: NewsItem,
                                blockchain_classification: Dict[str, Any],
                                entity_analysis: Dict[str, Any]) -> float:
        """Calculate content relevance score for gaming audience"""
        base_relevance = 0.5
        
        # Gaming keyword density
        gaming_keywords = [
            'gaming', 'game', 'play-to-earn', 'p2e', 'nft', 'metaverse',
            'blockchain game', 'crypto game', 'web3 game', 'gamefi'
        ]
        
        full_text = f"{content.title} {content.content or ''} {content.summary or ''}".lower()
        keyword_matches = sum(1 for keyword in gaming_keywords if keyword in full_text)
        keyword_score = min(1.0, keyword_matches / len(gaming_keywords))
        
        # Project mention relevance
        project_count = len(entity_analysis.get('detected_projects', []))
        project_relevance = min(1.0, project_count * 0.3)
        
        # Network relevance (Solana and Ethereum get higher scores)
        networks = blockchain_classification.get('blockchain_networks', [])
        high_relevance_networks = ['solana', 'ethereum', 'polygon', 'immutable_x']
        network_relevance = 0.8 if any(net in high_relevance_networks for net in networks) else 0.5
        
        # Category relevance
        categories = blockchain_classification.get('gaming_categories', [])
        high_relevance_categories = ['play_to_earn', 'nft_gaming', 'metaverse']
        category_relevance = 0.9 if any(cat in high_relevance_categories for cat in categories) else 0.6
        
        # Calculate final relevance
        relevance_score = (
            base_relevance * 0.2 +
            keyword_score * 0.3 +
            project_relevance * 0.2 +
            network_relevance * 0.15 +
            category_relevance * 0.15
        )
        
        return min(1.0, relevance_score)
    
    def determine_network_focus(self, blockchain_classification: Dict[str, Any],
                              entity_analysis: Dict[str, Any]) -> str:
        """Determine primary network focus of content"""
        # Check if explicitly Solana or Ethereum focused
        if blockchain_classification.get('is_solana_focused', False):
            return 'solana'
        if blockchain_classification.get('is_ethereum_focused', False):
            return 'ethereum'
        
        # Check entity analysis
        if entity_analysis.get('has_solana_projects', False):
            return 'solana'
        if entity_analysis.get('has_ethereum_projects', False):
            return 'ethereum'
        
        # Check primary network from entity analysis
        primary_network = entity_analysis.get('primary_network')
        if primary_network:
            return primary_network
        
        # Check blockchain networks
        networks = blockchain_classification.get('blockchain_networks', [])
        if 'solana' in networks:
            return 'solana'
        if 'ethereum' in networks:
            return 'ethereum'
        if networks:
            return networks[0]
        
        return 'unknown'
    
    async def aggregate_content(self, news_items: List[NewsItem],
                              strategy: AggregationStrategy = AggregationStrategy.RELEVANCE_BASED) -> List[AggregatedContent]:
        """Aggregate and score gaming content"""
        aggregated_items = []
        
        for item in news_items:
            try:
                # Perform blockchain classification
                blockchain_classification = blockchain_classifier.classify_content(
                    item.title, item.content or "", item.summary or ""
                )
                
                # Perform entity recognition
                entity_analysis = entity_engine.analyze_content(
                    item.title, item.content or "", item.summary or ""
                )
                
                # Calculate scores
                priority_score = self.calculate_priority_score(
                    item, blockchain_classification, entity_analysis
                )
                relevance_score = self.calculate_relevance_score(
                    item, blockchain_classification, entity_analysis
                )
                
                # Determine network focus
                network_focus = self.determine_network_focus(
                    blockchain_classification, entity_analysis
                )
                
                # Get cross-reference data
                cross_references = entity_engine.get_cross_reference_data(
                    entity_analysis.get('detected_projects', [])
                )
                
                # Create aggregated content
                aggregated_item = AggregatedContent(
                    news_item=item,
                    blockchain_classification=blockchain_classification,
                    entity_analysis=entity_analysis,
                    priority_score=priority_score,
                    relevance_score=relevance_score,
                    network_focus=network_focus,
                    gaming_categories=blockchain_classification.get('gaming_categories', []),
                    detected_projects=entity_analysis.get('detected_projects', []),
                    cross_references=cross_references,
                    aggregation_metadata={
                        'aggregation_timestamp': datetime.now().isoformat(),
                        'strategy_used': strategy.value,
                        'solana_focused': blockchain_classification.get('is_solana_focused', False),
                        'multi_chain': blockchain_classification.get('is_multi_chain', False)
                    }
                )
                
                aggregated_items.append(aggregated_item)
                
            except Exception as e:
                logger.error(f"Error aggregating content item: {e}")
                continue
        
        # Sort based on strategy
        return self._sort_by_strategy(aggregated_items, strategy)
    
    def _sort_by_strategy(self, items: List[AggregatedContent],
                         strategy: AggregationStrategy) -> List[AggregatedContent]:
        """Sort aggregated items by chosen strategy"""
        if strategy == AggregationStrategy.CHRONOLOGICAL:
            return sorted(items, key=lambda x: x.news_item.published_at or datetime.min, reverse=True)
        
        elif strategy == AggregationStrategy.RELEVANCE_BASED:
            return sorted(items, key=lambda x: (x.relevance_score, x.priority_score), reverse=True)
        
        elif strategy == AggregationStrategy.NETWORK_BALANCED:
            # Group by network and interleave
            network_groups = {}
            for item in items:
                network = item.network_focus
                if network not in network_groups:
                    network_groups[network] = []
                network_groups[network].append(item)
            
            # Sort each group by relevance
            for network in network_groups:
                network_groups[network].sort(key=lambda x: x.relevance_score, reverse=True)
            
            # Interleave items from different networks
            result = []
            max_items = max(len(group) for group in network_groups.values()) if network_groups else 0
            
            for i in range(max_items):
                for network in ['solana', 'ethereum', 'polygon']:  # Priority order
                    if network in network_groups and i < len(network_groups[network]):
                        result.append(network_groups[network][i])
                
                # Add remaining networks
                for network, group in network_groups.items():
                    if network not in ['solana', 'ethereum', 'polygon'] and i < len(group):
                        result.append(group[i])
            
            return result
        
        elif strategy == AggregationStrategy.PROJECT_FOCUSED:
            return sorted(items, key=lambda x: (len(x.detected_projects), x.priority_score), reverse=True)
        
        else:
            return sorted(items, key=lambda x: x.priority_score, reverse=True)
    
    def filter_by_network(self, items: List[AggregatedContent], 
                         networks: List[str]) -> List[AggregatedContent]:
        """Filter content by blockchain networks"""
        return [
            item for item in items
            if any(net in item.blockchain_classification.get('blockchain_networks', []) 
                  for net in networks)
        ]
    
    def filter_by_projects(self, items: List[AggregatedContent],
                          projects: List[str]) -> List[AggregatedContent]:
        """Filter content by gaming projects"""
        return [
            item for item in items
            if any(proj in item.detected_projects for proj in projects)
        ]
    
    def get_network_distribution(self, items: List[AggregatedContent]) -> Dict[str, int]:
        """Get distribution of content across networks"""
        distribution = {}
        for item in items:
            network = item.network_focus
            distribution[network] = distribution.get(network, 0) + 1
        return distribution
    
    def get_solana_focused_content(self, items: List[AggregatedContent]) -> List[AggregatedContent]:
        """Get content specifically focused on Solana ecosystem"""
        return [
            item for item in items
            if item.aggregation_metadata.get('solana_focused', False) or
               item.network_focus == 'solana'
        ]


# Global aggregator instance
cross_chain_aggregator = CrossChainContentAggregator()
