"""
Enhanced Duplicate Detection for Gaming News
Handles cross-posting between different gaming news sources and similar content across blockchain ecosystems
"""
import re
import logging
from typing import List, Dict, Set, Tuple, Optional
from dataclasses import dataclass
from difflib import SequenceMatcher
import hashlib
from datetime import datetime, timedelta

from .base import NewsItem
from .gaming_sources import blockchain_classifier
from .entity_recognition import entity_engine

logger = logging.getLogger(__name__)


@dataclass
class DuplicateMatch:
    """Represents a duplicate match between articles"""
    article1_url: str
    article2_url: str
    similarity_score: float
    match_type: str  # 'exact', 'near_duplicate', 'cross_post', 'similar_content'
    matching_elements: List[str]  # What elements matched
    confidence: float


class ContentNormalizer:
    """Normalizes content for duplicate detection"""
    
    @staticmethod
    def normalize_title(title: str) -> str:
        """Normalize title for comparison"""
        # Convert to lowercase
        normalized = title.lower()
        
        # Remove common prefixes/suffixes
        prefixes_to_remove = [
            'breaking:', 'exclusive:', 'news:', 'update:', 'alert:',
            'announcement:', 'press release:', 'pr:'
        ]
        
        for prefix in prefixes_to_remove:
            if normalized.startswith(prefix):
                normalized = normalized[len(prefix):].strip()
        
        # Remove special characters and extra whitespace
        normalized = re.sub(r'[^\w\s]', ' ', normalized)
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        return normalized
    
    @staticmethod
    def normalize_content(content: str) -> str:
        """Normalize content for comparison"""
        if not content:
            return ""
        
        # Convert to lowercase
        normalized = content.lower()
        
        # Remove URLs
        normalized = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', normalized)
        
        # Remove email addresses
        normalized = re.sub(r'\S+@\S+', '', normalized)
        
        # Remove extra whitespace and punctuation
        normalized = re.sub(r'[^\w\s]', ' ', normalized)
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        return normalized
    
    @staticmethod
    def extract_key_phrases(text: str) -> Set[str]:
        """Extract key phrases from text"""
        # Simple key phrase extraction
        words = text.lower().split()
        
        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
        }
        
        # Extract 2-3 word phrases
        phrases = set()
        for i in range(len(words) - 1):
            if words[i] not in stop_words and words[i+1] not in stop_words:
                phrases.add(f"{words[i]} {words[i+1]}")
        
        for i in range(len(words) - 2):
            if all(word not in stop_words for word in words[i:i+3]):
                phrases.add(f"{words[i]} {words[i+1]} {words[i+2]}")
        
        return phrases


class EnhancedDuplicateDetector:
    """Enhanced duplicate detection system"""
    
    def __init__(self):
        self.content_normalizer = ContentNormalizer()
        self.similarity_thresholds = {
            'exact': 0.95,
            'near_duplicate': 0.85,
            'cross_post': 0.75,
            'similar_content': 0.65
        }
    
    def detect_duplicates(self, articles: List[NewsItem]) -> List[DuplicateMatch]:
        """Detect duplicates in a list of articles"""
        duplicates = []
        
        for i in range(len(articles)):
            for j in range(i + 1, len(articles)):
                match = self._compare_articles(articles[i], articles[j])
                if match:
                    duplicates.append(match)
        
        return duplicates
    
    def _compare_articles(self, article1: NewsItem, article2: NewsItem) -> Optional[DuplicateMatch]:
        """Compare two articles for similarity"""
        # Skip if same URL
        if article1.url == article2.url:
            return None
        
        # Calculate various similarity scores
        title_similarity = self._calculate_title_similarity(article1.title, article2.title)
        content_similarity = self._calculate_content_similarity(
            article1.content or "", article2.content or ""
        )
        
        # Check for blockchain/gaming context similarity
        context_similarity = self._calculate_context_similarity(article1, article2)
        
        # Check for URL pattern similarity (same domain, similar paths)
        url_similarity = self._calculate_url_similarity(article1.url, article2.url)
        
        # Determine overall similarity and match type
        overall_similarity, match_type, matching_elements = self._determine_match_type(
            title_similarity, content_similarity, context_similarity, url_similarity
        )
        
        # Check if it meets threshold for any match type
        if overall_similarity >= self.similarity_thresholds['similar_content']:
            confidence = self._calculate_confidence(
                title_similarity, content_similarity, context_similarity, url_similarity
            )
            
            return DuplicateMatch(
                article1_url=article1.url,
                article2_url=article2.url,
                similarity_score=overall_similarity,
                match_type=match_type,
                matching_elements=matching_elements,
                confidence=confidence
            )
        
        return None
    
    def _calculate_title_similarity(self, title1: str, title2: str) -> float:
        """Calculate title similarity"""
        norm_title1 = self.content_normalizer.normalize_title(title1)
        norm_title2 = self.content_normalizer.normalize_title(title2)
        
        return SequenceMatcher(None, norm_title1, norm_title2).ratio()
    
    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """Calculate content similarity"""
        if not content1 or not content2:
            return 0.0
        
        norm_content1 = self.content_normalizer.normalize_content(content1)
        norm_content2 = self.content_normalizer.normalize_content(content2)
        
        # Use sequence matcher for overall similarity
        sequence_similarity = SequenceMatcher(None, norm_content1, norm_content2).ratio()
        
        # Also check key phrase overlap
        phrases1 = self.content_normalizer.extract_key_phrases(norm_content1)
        phrases2 = self.content_normalizer.extract_key_phrases(norm_content2)
        
        if phrases1 and phrases2:
            phrase_overlap = len(phrases1.intersection(phrases2)) / len(phrases1.union(phrases2))
        else:
            phrase_overlap = 0.0
        
        # Combine both measures
        return (sequence_similarity * 0.7) + (phrase_overlap * 0.3)
    
    def _calculate_context_similarity(self, article1: NewsItem, article2: NewsItem) -> float:
        """Calculate blockchain/gaming context similarity"""
        try:
            # Get blockchain classifications
            classification1 = blockchain_classifier.classify_content(
                article1.title, article1.content or "", article1.summary or ""
            )
            classification2 = blockchain_classifier.classify_content(
                article2.title, article2.content or "", article2.summary or ""
            )
            
            # Get entity analyses
            entities1 = entity_engine.analyze_content(
                article1.title, article1.content or "", article1.summary or ""
            )
            entities2 = entity_engine.analyze_content(
                article2.title, article2.content or "", article2.summary or ""
            )
            
            # Compare blockchain networks
            networks1 = set(classification1.get('blockchain_networks', []))
            networks2 = set(classification2.get('blockchain_networks', []))
            network_similarity = (
                len(networks1.intersection(networks2)) / len(networks1.union(networks2))
                if networks1.union(networks2) else 0.0
            )
            
            # Compare gaming categories
            categories1 = set(classification1.get('gaming_categories', []))
            categories2 = set(classification2.get('gaming_categories', []))
            category_similarity = (
                len(categories1.intersection(categories2)) / len(categories1.union(categories2))
                if categories1.union(categories2) else 0.0
            )
            
            # Compare detected projects
            projects1 = set(entities1.get('detected_projects', []))
            projects2 = set(entities2.get('detected_projects', []))
            project_similarity = (
                len(projects1.intersection(projects2)) / len(projects1.union(projects2))
                if projects1.union(projects2) else 0.0
            )
            
            # Weighted average
            context_similarity = (
                network_similarity * 0.4 +
                category_similarity * 0.3 +
                project_similarity * 0.3
            )
            
            return context_similarity
            
        except Exception as e:
            logger.warning(f"Error calculating context similarity: {e}")
            return 0.0
    
    def _calculate_url_similarity(self, url1: str, url2: str) -> float:
        """Calculate URL similarity"""
        try:
            from urllib.parse import urlparse
            
            parsed1 = urlparse(url1)
            parsed2 = urlparse(url2)
            
            # Same domain gets high score
            if parsed1.netloc == parsed2.netloc:
                # Check path similarity
                path_similarity = SequenceMatcher(None, parsed1.path, parsed2.path).ratio()
                return 0.8 + (path_similarity * 0.2)  # Base 0.8 for same domain
            
            # Different domains but similar structure
            domain_similarity = SequenceMatcher(None, parsed1.netloc, parsed2.netloc).ratio()
            path_similarity = SequenceMatcher(None, parsed1.path, parsed2.path).ratio()
            
            return (domain_similarity * 0.6) + (path_similarity * 0.4)
            
        except Exception as e:
            logger.warning(f"Error calculating URL similarity: {e}")
            return 0.0
    
    def _determine_match_type(self, title_sim: float, content_sim: float, 
                            context_sim: float, url_sim: float) -> Tuple[float, str, List[str]]:
        """Determine match type and overall similarity"""
        matching_elements = []
        
        # Check for exact match
        if title_sim >= 0.95 and content_sim >= 0.95:
            return 0.98, 'exact', ['title', 'content']
        
        # Check for near duplicate
        if title_sim >= 0.85 and content_sim >= 0.80:
            matching_elements = ['title', 'content']
            overall_sim = (title_sim * 0.6) + (content_sim * 0.4)
            return overall_sim, 'near_duplicate', matching_elements
        
        # Check for cross-post (same content, different source)
        if url_sim < 0.3 and title_sim >= 0.75 and content_sim >= 0.70:
            matching_elements = ['title', 'content']
            if context_sim >= 0.6:
                matching_elements.append('context')
            overall_sim = (title_sim * 0.4) + (content_sim * 0.4) + (context_sim * 0.2)
            return overall_sim, 'cross_post', matching_elements
        
        # Check for similar content
        overall_sim = (title_sim * 0.3) + (content_sim * 0.3) + (context_sim * 0.3) + (url_sim * 0.1)
        
        if title_sim >= 0.6:
            matching_elements.append('title')
        if content_sim >= 0.6:
            matching_elements.append('content')
        if context_sim >= 0.6:
            matching_elements.append('context')
        if url_sim >= 0.6:
            matching_elements.append('url')
        
        return overall_sim, 'similar_content', matching_elements
    
    def _calculate_confidence(self, title_sim: float, content_sim: float, 
                            context_sim: float, url_sim: float) -> float:
        """Calculate confidence in the duplicate detection"""
        # Higher confidence when multiple factors align
        factors_above_threshold = sum([
            title_sim >= 0.7,
            content_sim >= 0.7,
            context_sim >= 0.6,
            url_sim >= 0.6
        ])
        
        base_confidence = (title_sim + content_sim + context_sim + url_sim) / 4
        factor_bonus = factors_above_threshold * 0.1
        
        return min(1.0, base_confidence + factor_bonus)
    
    def filter_duplicates(self, articles: List[NewsItem], 
                         keep_strategy: str = 'highest_priority') -> List[NewsItem]:
        """Filter out duplicates from article list"""
        duplicates = self.detect_duplicates(articles)
        
        # Group articles by duplicate clusters
        duplicate_clusters = self._group_duplicate_clusters(articles, duplicates)
        
        # Keep one article from each cluster
        filtered_articles = []
        processed_urls = set()
        
        for cluster in duplicate_clusters:
            if keep_strategy == 'highest_priority':
                # Keep article with highest priority score (if available)
                best_article = max(cluster, key=lambda a: getattr(a, 'priority_score', 0))
            elif keep_strategy == 'earliest':
                # Keep earliest published article
                best_article = min(cluster, key=lambda a: a.published_at or datetime.min)
            elif keep_strategy == 'latest':
                # Keep latest published article
                best_article = max(cluster, key=lambda a: a.published_at or datetime.min)
            else:
                # Default: keep first article
                best_article = cluster[0]
            
            if best_article.url not in processed_urls:
                filtered_articles.append(best_article)
                processed_urls.add(best_article.url)
        
        # Add non-duplicate articles
        for article in articles:
            if article.url not in processed_urls:
                filtered_articles.append(article)
        
        return filtered_articles
    
    def _group_duplicate_clusters(self, articles: List[NewsItem], 
                                duplicates: List[DuplicateMatch]) -> List[List[NewsItem]]:
        """Group articles into duplicate clusters"""
        # Create URL to article mapping
        url_to_article = {article.url: article for article in articles}
        
        # Build adjacency list for duplicate relationships
        adjacency = {}
        for duplicate in duplicates:
            url1, url2 = duplicate.article1_url, duplicate.article2_url
            
            if url1 not in adjacency:
                adjacency[url1] = set()
            if url2 not in adjacency:
                adjacency[url2] = set()
            
            adjacency[url1].add(url2)
            adjacency[url2].add(url1)
        
        # Find connected components (duplicate clusters)
        visited = set()
        clusters = []
        
        for url in adjacency:
            if url not in visited:
                cluster_urls = self._dfs_cluster(url, adjacency, visited)
                cluster_articles = [url_to_article[url] for url in cluster_urls if url in url_to_article]
                if cluster_articles:
                    clusters.append(cluster_articles)
        
        return clusters
    
    def _dfs_cluster(self, url: str, adjacency: Dict[str, Set[str]], visited: Set[str]) -> Set[str]:
        """DFS to find all URLs in a duplicate cluster"""
        if url in visited:
            return set()
        
        visited.add(url)
        cluster = {url}
        
        for neighbor in adjacency.get(url, set()):
            cluster.update(self._dfs_cluster(neighbor, adjacency, visited))
        
        return cluster


# Global duplicate detector instance
duplicate_detector = EnhancedDuplicateDetector()
