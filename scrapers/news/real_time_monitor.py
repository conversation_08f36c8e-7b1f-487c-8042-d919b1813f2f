"""
Real-time Gaming News Monitoring System
Live news collection and processing with blockchain-aware classification
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
import json

from .gaming_sources import GAMING_SCRAPERS, get_scraper_for_source, save_scraped_articles
from .cross_chain_aggregator import cross_chain_aggregator, AggregationStrategy
from .entity_recognition import entity_engine

logger = logging.getLogger(__name__)


@dataclass
class MonitoringConfig:
    """Configuration for real-time monitoring"""
    check_interval_minutes: int = 60  # 1 hour for news sources
    priority_sources_interval_minutes: int = 30  # 30 minutes for priority sources
    max_articles_per_check: int = 100
    enable_notifications: bool = True
    notification_threshold_score: float = 0.8
    focus_networks: List[str] = None
    focus_projects: List[str] = None


class NewsAlert:
    """News alert for high-priority content"""
    
    def __init__(self, article_data: Dict[str, Any], alert_type: str, priority: str):
        self.article_data = article_data
        self.alert_type = alert_type  # 'new_project', 'major_update', 'security', 'partnership'
        self.priority = priority      # 'critical', 'high', 'medium'
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'article_data': self.article_data,
            'alert_type': self.alert_type,
            'priority': self.priority,
            'timestamp': self.timestamp.isoformat()
        }


class RealTimeNewsMonitor:
    """Real-time news monitoring and processing system"""
    
    def __init__(self, config: MonitoringConfig = None):
        self.config = config or MonitoringConfig()
        self.is_running = False
        self.last_check_times = {}
        self.processed_urls = set()
        self.alert_handlers = []
        self.monitoring_stats = {
            'total_checks': 0,
            'articles_processed': 0,
            'alerts_generated': 0,
            'last_check': None,
            'errors': 0
        }
    
    def add_alert_handler(self, handler):
        """Add alert handler function"""
        self.alert_handlers.append(handler)
    
    async def start_monitoring(self):
        """Start real-time monitoring"""
        if self.is_running:
            logger.warning("Monitoring is already running")
            return
        
        self.is_running = True
        logger.info("Starting real-time gaming news monitoring")
        
        try:
            await self._monitoring_loop()
        except Exception as e:
            logger.error(f"Monitoring loop error: {e}")
        finally:
            self.is_running = False
    
    async def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.is_running = False
        logger.info("Stopping real-time gaming news monitoring")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_running:
            try:
                await self._check_all_sources()
                
                # Wait for next check
                await asyncio.sleep(self.config.check_interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                self.monitoring_stats['errors'] += 1
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _check_all_sources(self):
        """Check all gaming news sources for new content"""
        self.monitoring_stats['total_checks'] += 1
        self.monitoring_stats['last_check'] = datetime.now()
        
        logger.info("Checking all gaming news sources for updates")
        
        # Determine which sources to check
        priority_sources = self._get_priority_sources()
        regular_sources = [name for name in GAMING_SCRAPERS.keys() if name not in priority_sources]
        
        # Check priority sources more frequently
        sources_to_check = priority_sources
        
        # Check regular sources based on interval
        current_time = datetime.now()
        for source in regular_sources:
            last_check = self.last_check_times.get(source)
            if not last_check or (current_time - last_check).total_seconds() >= (self.config.check_interval_minutes * 60):
                sources_to_check.append(source)
        
        # Process sources
        new_articles = []
        for source_name in sources_to_check:
            try:
                articles = await self._check_source(source_name)
                new_articles.extend(articles)
                self.last_check_times[source_name] = current_time
                
            except Exception as e:
                logger.error(f"Error checking source {source_name}: {e}")
                continue
        
        if new_articles:
            await self._process_new_articles(new_articles)
    
    def _get_priority_sources(self) -> List[str]:
        """Get list of priority sources for frequent checking"""
        # Solana-focused sources get priority
        priority_sources = [
            'solana-news',
            'magic-eden-blog',
            'star-atlas',
            'coindesk-gaming',  # Major crypto news
            'decrypt-gaming'    # Major crypto news
        ]
        
        return [source for source in priority_sources if source in GAMING_SCRAPERS]
    
    async def _check_source(self, source_name: str) -> List[Any]:
        """Check a specific source for new articles"""
        try:
            scraper_class = GAMING_SCRAPERS.get(source_name)
            if not scraper_class:
                logger.warning(f"No scraper found for source: {source_name}")
                return []
            
            # Create scraper instance (using monitoring source_id)
            scraper = scraper_class(source_id=0)
            
            # Scrape articles
            articles = await scraper.scrape()
            
            # Filter out already processed articles
            new_articles = []
            for article in articles:
                if article.url not in self.processed_urls:
                    new_articles.append(article)
                    self.processed_urls.add(article.url)
            
            if new_articles:
                logger.info(f"Found {len(new_articles)} new articles from {source_name}")
            
            return new_articles
            
        except Exception as e:
            logger.error(f"Error scraping {source_name}: {e}")
            return []
    
    async def _process_new_articles(self, articles: List[Any]):
        """Process new articles with enhanced classification"""
        logger.info(f"Processing {len(articles)} new articles")
        
        # Aggregate and classify content
        aggregated_content = await cross_chain_aggregator.aggregate_content(
            articles, AggregationStrategy.RELEVANCE_BASED
        )
        
        # Filter by focus networks/projects if configured
        if self.config.focus_networks:
            aggregated_content = cross_chain_aggregator.filter_by_network(
                aggregated_content, self.config.focus_networks
            )
        
        if self.config.focus_projects:
            aggregated_content = cross_chain_aggregator.filter_by_projects(
                aggregated_content, self.config.focus_projects
            )
        
        # Process high-priority content
        high_priority_content = [
            item for item in aggregated_content
            if item.priority_score >= self.config.notification_threshold_score
        ]
        
        # Generate alerts for high-priority content
        for item in high_priority_content:
            await self._generate_alert(item)
        
        # Save to database (if database session available)
        try:
            from models.base import SessionLocal
            db = SessionLocal()
            
            try:
                # Convert aggregated content back to news items for saving
                news_items = [item.news_item for item in aggregated_content]
                scraped_data = {'real_time_monitor': news_items}
                saved_count = await save_scraped_articles(scraped_data)
                
                self.monitoring_stats['articles_processed'] += saved_count
                logger.info(f"Saved {saved_count} articles to database")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error saving articles to database: {e}")
    
    async def _generate_alert(self, aggregated_item):
        """Generate alert for high-priority content"""
        try:
            # Determine alert type
            alert_type = self._determine_alert_type(aggregated_item)
            priority = self._determine_priority(aggregated_item)
            
            # Create alert
            alert = NewsAlert(
                article_data={
                    'title': aggregated_item.news_item.title,
                    'url': aggregated_item.news_item.url,
                    'summary': aggregated_item.news_item.summary,
                    'blockchain_networks': aggregated_item.blockchain_classification.get('blockchain_networks', []),
                    'detected_projects': aggregated_item.detected_projects,
                    'priority_score': aggregated_item.priority_score,
                    'relevance_score': aggregated_item.relevance_score,
                    'network_focus': aggregated_item.network_focus
                },
                alert_type=alert_type,
                priority=priority
            )
            
            # Send alert to handlers
            for handler in self.alert_handlers:
                try:
                    await handler(alert)
                except Exception as e:
                    logger.error(f"Error in alert handler: {e}")
            
            self.monitoring_stats['alerts_generated'] += 1
            logger.info(f"Generated {priority} priority alert: {alert_type}")
            
        except Exception as e:
            logger.error(f"Error generating alert: {e}")
    
    def _determine_alert_type(self, aggregated_item) -> str:
        """Determine the type of alert based on content"""
        title_lower = aggregated_item.news_item.title.lower()
        content_lower = (aggregated_item.news_item.content or "").lower()
        
        # Security-related
        if any(keyword in title_lower for keyword in ['hack', 'exploit', 'vulnerability', 'security', 'breach']):
            return 'security'
        
        # Partnership announcements
        if any(keyword in title_lower for keyword in ['partnership', 'collaboration', 'integration', 'announces']):
            return 'partnership'
        
        # New project launches
        if any(keyword in title_lower for keyword in ['launch', 'debut', 'introduces', 'unveils']):
            return 'new_project'
        
        # Major updates
        if any(keyword in title_lower for keyword in ['update', 'upgrade', 'release', 'version']):
            return 'major_update'
        
        return 'general'
    
    def _determine_priority(self, aggregated_item) -> str:
        """Determine priority level of alert"""
        priority_score = aggregated_item.priority_score
        
        # Check for critical keywords
        title_lower = aggregated_item.news_item.title.lower()
        critical_keywords = ['hack', 'exploit', 'vulnerability', 'breach', 'emergency']
        
        if any(keyword in title_lower for keyword in critical_keywords):
            return 'critical'
        
        if priority_score >= 0.9:
            return 'critical'
        elif priority_score >= 0.8:
            return 'high'
        else:
            return 'medium'
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """Get monitoring statistics"""
        stats = self.monitoring_stats.copy()
        stats['is_running'] = self.is_running
        stats['processed_urls_count'] = len(self.processed_urls)
        stats['last_check_formatted'] = (
            stats['last_check'].strftime('%Y-%m-%d %H:%M:%S') 
            if stats['last_check'] else None
        )
        return stats
    
    async def force_check(self) -> Dict[str, Any]:
        """Force an immediate check of all sources"""
        logger.info("Forcing immediate check of all sources")
        
        try:
            await self._check_all_sources()
            return {
                'status': 'success',
                'message': 'Forced check completed',
                'stats': self.get_monitoring_stats()
            }
        except Exception as e:
            logger.error(f"Error in forced check: {e}")
            return {
                'status': 'error',
                'message': f'Forced check failed: {str(e)}',
                'stats': self.get_monitoring_stats()
            }


# Global monitor instance
real_time_monitor = RealTimeNewsMonitor()


# Example alert handlers
async def console_alert_handler(alert: NewsAlert):
    """Simple console alert handler"""
    print(f"\n🚨 {alert.priority.upper()} ALERT - {alert.alert_type.upper()}")
    print(f"Title: {alert.article_data['title']}")
    print(f"Networks: {', '.join(alert.article_data['blockchain_networks'])}")
    print(f"Projects: {', '.join(alert.article_data['detected_projects'])}")
    print(f"URL: {alert.article_data['url']}")
    print(f"Priority Score: {alert.article_data['priority_score']:.2f}")
    print("-" * 80)


async def webhook_alert_handler(alert: NewsAlert):
    """Webhook alert handler - disabled to avoid external API calls during development"""
    # Webhook integration disabled to prevent external API calls during testing
    logger.info(f"Webhook alert (disabled): {alert.alert_type} - {alert.priority}")


# Register default alert handlers
real_time_monitor.add_alert_handler(console_alert_handler)
