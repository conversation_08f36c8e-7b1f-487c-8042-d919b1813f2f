"""
Gaming Project Entity Recognition System
Identifies gaming projects mentioned in news articles and cross-references with blockchain data
"""
import re
import logging
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


@dataclass
class GamingProject:
    """Gaming project entity"""
    name: str
    aliases: List[str]
    blockchain_networks: List[str]
    project_type: str
    token_symbols: List[str]
    contract_addresses: Dict[str, str]  # network -> address
    official_urls: List[str]
    description: str
    is_active: bool = True


class ProjectType(Enum):
    """Gaming project types"""
    P2E_GAME = "p2e_game"
    NFT_GAME = "nft_game"
    METAVERSE = "metaverse"
    GAMING_PLATFORM = "gaming_platform"
    GAMING_INFRASTRUCTURE = "gaming_infrastructure"
    GAMING_TOKEN = "gaming_token"
    GAMING_DAO = "gaming_dao"
    GAMING_GUILD = "gaming_guild"


class GamingProjectRegistry:
    """Registry of known gaming projects with their metadata"""
    
    def __init__(self):
        self.projects = self._initialize_projects()
        self.name_patterns = self._build_name_patterns()
        self.token_patterns = self._build_token_patterns()
    
    def _initialize_projects(self) -> Dict[str, GamingProject]:
        """Initialize known gaming projects"""
        projects = {
            'axie_infinity': GamingProject(
                name="Axie Infinity",
                aliases=["axie", "axies", "axie infinity", "sky mavis"],
                blockchain_networks=["ronin", "ethereum"],
                project_type=ProjectType.P2E_GAME.value,
                token_symbols=["AXS", "SLP"],
                contract_addresses={
                    "ethereum": "******************************************",  # AXS
                    "ronin": "******************************************"  # AXS on Ronin
                },
                official_urls=["https://axieinfinity.com"],
                description="Play-to-earn game featuring collectible creatures called Axies"
            ),
            
            'the_sandbox': GamingProject(
                name="The Sandbox",
                aliases=["sandbox", "the sandbox", "sand game"],
                blockchain_networks=["ethereum", "polygon"],
                project_type=ProjectType.METAVERSE.value,
                token_symbols=["SAND"],
                contract_addresses={
                    "ethereum": "******************************************"
                },
                official_urls=["https://www.sandbox.game"],
                description="Decentralized virtual gaming world and metaverse"
            ),
            
            'decentraland': GamingProject(
                name="Decentraland",
                aliases=["decentraland", "dcl", "mana world"],
                blockchain_networks=["ethereum", "polygon"],
                project_type=ProjectType.METAVERSE.value,
                token_symbols=["MANA"],
                contract_addresses={
                    "ethereum": "******************************************"
                },
                official_urls=["https://decentraland.org"],
                description="Virtual reality platform powered by Ethereum blockchain"
            ),
            
            'enjin': GamingProject(
                name="Enjin",
                aliases=["enjin", "enjin coin", "enjin platform"],
                blockchain_networks=["ethereum", "enjin_blockchain"],
                project_type=ProjectType.GAMING_PLATFORM.value,
                token_symbols=["ENJ"],
                contract_addresses={
                    "ethereum": "******************************************"
                },
                official_urls=["https://enjin.io"],
                description="Blockchain gaming platform and NFT ecosystem"
            ),
            
            'gala_games': GamingProject(
                name="Gala Games",
                aliases=["gala", "gala games", "gala network"],
                blockchain_networks=["ethereum", "bsc"],
                project_type=ProjectType.GAMING_PLATFORM.value,
                token_symbols=["GALA"],
                contract_addresses={
                    "ethereum": "******************************************"
                },
                official_urls=["https://gala.games"],
                description="Blockchain gaming ecosystem and platform"
            ),
            
            'immutable_x': GamingProject(
                name="Immutable X",
                aliases=["immutable", "immutable x", "imx"],
                blockchain_networks=["ethereum", "immutable_x"],
                project_type=ProjectType.GAMING_INFRASTRUCTURE.value,
                token_symbols=["IMX"],
                contract_addresses={
                    "ethereum": "******************************************"
                },
                official_urls=["https://immutable.com"],
                description="Layer 2 scaling solution for NFTs and gaming"
            ),
            
            # Solana gaming projects
            'star_atlas': GamingProject(
                name="Star Atlas",
                aliases=["star atlas", "staratlas", "atlas game"],
                blockchain_networks=["solana"],
                project_type=ProjectType.P2E_GAME.value,
                token_symbols=["ATLAS", "POLIS"],
                contract_addresses={
                    "solana": "ATLASXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx"  # ATLAS token
                },
                official_urls=["https://staratlas.com"],
                description="Space exploration metaverse game on Solana"
            ),
            
            'solana_monkey_business': GamingProject(
                name="Solana Monkey Business",
                aliases=["smb", "solana monkey", "monkey business"],
                blockchain_networks=["solana"],
                project_type=ProjectType.NFT_GAME.value,
                token_symbols=["SMB"],
                contract_addresses={
                    "solana": "SMBtHCCC6RYRutFEPb4gZqeBLUZbMNhRKaMKZZLHi7W"
                },
                official_urls=["https://solanamonkey.business"],
                description="NFT collection and gaming ecosystem on Solana"
            ),
            
            'aurory': GamingProject(
                name="Aurory",
                aliases=["aurory", "aurory game"],
                blockchain_networks=["solana"],
                project_type=ProjectType.P2E_GAME.value,
                token_symbols=["AURY"],
                contract_addresses={
                    "solana": "AURYydfxJib1ZkTir1Jn1J9ECYUtjb6rKQVmtYaixWPP"
                },
                official_urls=["https://aurory.io"],
                description="Japanese-inspired RPG game on Solana"
            ),
            
            'genopets': GamingProject(
                name="Genopets",
                aliases=["genopets", "geno pets"],
                blockchain_networks=["solana"],
                project_type=ProjectType.P2E_GAME.value,
                token_symbols=["GENE", "KI"],
                contract_addresses={
                    "solana": "GENEtH5amGSi8kHAtQoezp1XEXwZJ8vcuePYnXdKrMYz"
                },
                official_urls=["https://genopets.me"],
                description="Move-to-earn NFT game on Solana"
            )
        }
        
        return projects
    
    def _build_name_patterns(self) -> Dict[str, re.Pattern]:
        """Build regex patterns for project name detection"""
        patterns = {}
        
        for project_id, project in self.projects.items():
            # Create pattern from name and aliases
            all_names = [project.name] + project.aliases
            # Escape special regex characters and create word boundary pattern
            escaped_names = [re.escape(name) for name in all_names]
            pattern_str = r'\b(?:' + '|'.join(escaped_names) + r')\b'
            patterns[project_id] = re.compile(pattern_str, re.IGNORECASE)
        
        return patterns
    
    def _build_token_patterns(self) -> Dict[str, re.Pattern]:
        """Build regex patterns for token symbol detection"""
        patterns = {}
        
        for project_id, project in self.projects.items():
            if project.token_symbols:
                # Create pattern for token symbols
                symbols = [re.escape(symbol) for symbol in project.token_symbols]
                pattern_str = r'\b(?:' + '|'.join(symbols) + r')\b'
                patterns[project_id] = re.compile(pattern_str, re.IGNORECASE)
        
        return patterns
    
    def detect_projects_in_text(self, text: str) -> Set[str]:
        """Detect gaming projects mentioned in text"""
        detected_projects = set()
        
        # Check name patterns
        for project_id, pattern in self.name_patterns.items():
            if pattern.search(text):
                detected_projects.add(project_id)
        
        # Check token patterns
        for project_id, pattern in self.token_patterns.items():
            if pattern.search(text):
                detected_projects.add(project_id)
        
        return detected_projects
    
    def get_project_info(self, project_id: str) -> Optional[GamingProject]:
        """Get detailed information about a project"""
        return self.projects.get(project_id)
    
    def get_projects_by_network(self, network: str) -> List[str]:
        """Get all projects on a specific blockchain network"""
        return [
            project_id for project_id, project in self.projects.items()
            if network in project.blockchain_networks
        ]
    
    def get_projects_by_type(self, project_type: str) -> List[str]:
        """Get all projects of a specific type"""
        return [
            project_id for project_id, project in self.projects.items()
            if project.project_type == project_type
        ]


class EntityRecognitionEngine:
    """Main entity recognition engine for gaming content"""
    
    def __init__(self):
        self.project_registry = GamingProjectRegistry()
    
    def analyze_content(self, title: str, content: str = "", summary: str = "") -> Dict[str, Any]:
        """Comprehensive entity analysis of gaming content"""
        full_text = f"{title} {content} {summary}".strip()
        
        # Detect gaming projects
        detected_projects = self.project_registry.detect_projects_in_text(full_text)
        
        # Get detailed project information
        project_details = {}
        for project_id in detected_projects:
            project_info = self.project_registry.get_project_info(project_id)
            if project_info:
                project_details[project_id] = {
                    'name': project_info.name,
                    'type': project_info.project_type,
                    'networks': project_info.blockchain_networks,
                    'tokens': project_info.token_symbols,
                    'description': project_info.description
                }
        
        # Analyze network distribution
        network_distribution = {}
        for project_id in detected_projects:
            project_info = self.project_registry.get_project_info(project_id)
            if project_info:
                for network in project_info.blockchain_networks:
                    network_distribution[network] = network_distribution.get(network, 0) + 1
        
        # Determine primary focus
        primary_network = max(network_distribution.items(), key=lambda x: x[1])[0] if network_distribution else None
        
        return {
            'detected_projects': list(detected_projects),
            'project_details': project_details,
            'network_distribution': network_distribution,
            'primary_network': primary_network,
            'total_projects_mentioned': len(detected_projects),
            'has_solana_projects': any(
                'solana' in self.project_registry.get_project_info(pid).blockchain_networks
                for pid in detected_projects
                if self.project_registry.get_project_info(pid)
            ),
            'has_ethereum_projects': any(
                'ethereum' in self.project_registry.get_project_info(pid).blockchain_networks
                for pid in detected_projects
                if self.project_registry.get_project_info(pid)
            )
        }
    
    def get_cross_reference_data(self, project_ids: List[str]) -> Dict[str, Any]:
        """Get blockchain cross-reference data for detected projects"""
        cross_reference = {
            'contracts_to_monitor': {},
            'tokens_to_track': {},
            'networks_involved': set()
        }
        
        for project_id in project_ids:
            project_info = self.project_registry.get_project_info(project_id)
            if project_info:
                # Add contract addresses
                for network, address in project_info.contract_addresses.items():
                    if network not in cross_reference['contracts_to_monitor']:
                        cross_reference['contracts_to_monitor'][network] = []
                    cross_reference['contracts_to_monitor'][network].append({
                        'project': project_info.name,
                        'address': address,
                        'type': project_info.project_type
                    })
                
                # Add tokens
                for token in project_info.token_symbols:
                    cross_reference['tokens_to_track'][token] = {
                        'project': project_info.name,
                        'networks': project_info.blockchain_networks
                    }
                
                # Add networks
                cross_reference['networks_involved'].update(project_info.blockchain_networks)
        
        # Convert set to list for JSON serialization
        cross_reference['networks_involved'] = list(cross_reference['networks_involved'])
        
        return cross_reference


# Global entity recognition engine
entity_engine = EntityRecognitionEngine()
