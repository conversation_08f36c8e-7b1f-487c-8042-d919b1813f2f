"""
Real-time Blockchain Monitor
WebSocket-based real-time monitoring of gaming events across multiple chains
"""
import asyncio
import logging
import json
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass
from datetime import datetime
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from blockchain.multi_chain_client import multi_chain_manager
from .event_scraper import blockchain_event_scraper, GameEvent, GameEventType
from .contract_detector import gaming_contract_detector, ContractConfidence
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class WebSocketConnection:
    """WebSocket connection info"""
    url: str
    chain: str
    connection: Optional[websockets.WebSocketServerProtocol] = None
    is_connected: bool = False
    reconnect_attempts: int = 0
    max_reconnect_attempts: int = 5


class RealTimeBlockchainMonitor:
    """Real-time monitoring of blockchain events via WebSocket"""
    
    def __init__(self):
        self.connections: Dict[str, WebSocketConnection] = {}
        self.event_callbacks: List[Callable[[GameEvent], None]] = []
        self.monitored_contracts: Set[str] = set()
        self.is_running = False
        
        # WebSocket URLs for different chains
        self.websocket_urls = {
            'ethereum': 'wss://mainnet.infura.io/ws/v3/YOUR_PROJECT_ID',
            'polygon': 'wss://polygon-mainnet.infura.io/ws/v3/YOUR_PROJECT_ID',
            'bsc': 'wss://bsc-ws-node.nariox.org:443',
            'solana': 'wss://api.mainnet-beta.solana.com',
            'arbitrum': 'wss://arb1.arbitrum.io/ws',
            'optimism': 'wss://ws-mainnet.optimism.io',
        }
        
        # Initialize connections
        for chain, url in self.websocket_urls.items():
            if url and 'YOUR_PROJECT_ID' not in url:  # Only if properly configured
                self.connections[chain] = WebSocketConnection(url=url, chain=chain)
    
    async def start_monitoring(self, chains: List[str] = None):
        """Start real-time monitoring for specified chains"""
        if chains is None:
            chains = list(self.connections.keys())
        
        logger.info(f"Starting real-time monitoring for chains: {chains}")
        self.is_running = True
        
        # Start monitoring tasks for each chain
        tasks = []
        for chain in chains:
            if chain in self.connections:
                task = asyncio.create_task(self._monitor_chain_websocket(chain))
                tasks.append(task)
        
        # Also start the contract scanner
        scanner_task = asyncio.create_task(self._periodic_contract_scan())
        tasks.append(scanner_task)
        
        try:
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Error in real-time monitoring: {e}")
        finally:
            self.is_running = False
    
    async def stop_monitoring(self):
        """Stop real-time monitoring"""
        logger.info("Stopping real-time monitoring")
        self.is_running = False
        
        # Close all WebSocket connections
        for connection in self.connections.values():
            if connection.connection and connection.is_connected:
                await connection.connection.close()
                connection.is_connected = False
    
    async def _monitor_chain_websocket(self, chain: str):
        """Monitor a specific chain via WebSocket"""
        connection = self.connections[chain]
        
        while self.is_running:
            try:
                await self._connect_websocket(connection)
                
                if connection.is_connected:
                    await self._subscribe_to_events(connection)
                    await self._listen_for_events(connection)
                
            except (ConnectionClosed, WebSocketException) as e:
                logger.warning(f"WebSocket connection lost for {chain}: {e}")
                connection.is_connected = False
                connection.reconnect_attempts += 1
                
                if connection.reconnect_attempts < connection.max_reconnect_attempts:
                    wait_time = min(60, 2 ** connection.reconnect_attempts)
                    logger.info(f"Reconnecting to {chain} in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"Max reconnection attempts reached for {chain}")
                    break
                    
            except Exception as e:
                logger.error(f"Error monitoring {chain}: {e}")
                await asyncio.sleep(30)
    
    async def _connect_websocket(self, connection: WebSocketConnection):
        """Connect to WebSocket"""
        if connection.is_connected:
            return
        
        try:
            logger.info(f"Connecting to {connection.chain} WebSocket: {connection.url}")
            connection.connection = await websockets.connect(
                connection.url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )
            connection.is_connected = True
            connection.reconnect_attempts = 0
            logger.info(f"Connected to {connection.chain} WebSocket")
            
        except Exception as e:
            logger.error(f"Failed to connect to {connection.chain} WebSocket: {e}")
            connection.is_connected = False
    
    async def _subscribe_to_events(self, connection: WebSocketConnection):
        """Subscribe to relevant events"""
        if not connection.connection or not connection.is_connected:
            return
        
        try:
            if connection.chain in ['ethereum', 'polygon', 'bsc', 'arbitrum', 'optimism']:
                # Subscribe to new blocks and logs
                await self._subscribe_evm_events(connection)
            elif connection.chain == 'solana':
                # Subscribe to Solana events
                await self._subscribe_solana_events(connection)
                
        except Exception as e:
            logger.error(f"Error subscribing to events for {connection.chain}: {e}")
    
    async def _subscribe_evm_events(self, connection: WebSocketConnection):
        """Subscribe to EVM chain events"""
        # Subscribe to new blocks
        new_blocks_subscription = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "eth_subscribe",
            "params": ["newHeads"]
        }
        
        await connection.connection.send(json.dumps(new_blocks_subscription))
        
        # Subscribe to logs for monitored contracts
        if self.monitored_contracts:
            logs_subscription = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "eth_subscribe",
                "params": [
                    "logs",
                    {
                        "address": list(self.monitored_contracts),
                        "topics": []
                    }
                ]
            }
            await connection.connection.send(json.dumps(logs_subscription))
    
    async def _subscribe_solana_events(self, connection: WebSocketConnection):
        """Subscribe to Solana events"""
        # Subscribe to account changes for monitored programs
        for contract in self.monitored_contracts:
            subscription = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "accountSubscribe",
                "params": [
                    contract,
                    {
                        "encoding": "jsonParsed",
                        "commitment": "finalized"
                    }
                ]
            }
            await connection.connection.send(json.dumps(subscription))
    
    async def _listen_for_events(self, connection: WebSocketConnection):
        """Listen for incoming events"""
        while connection.is_connected and self.is_running:
            try:
                message = await asyncio.wait_for(
                    connection.connection.recv(), 
                    timeout=30
                )
                
                await self._process_websocket_message(connection.chain, message)
                
            except asyncio.TimeoutError:
                # Send ping to keep connection alive
                if connection.connection:
                    await connection.connection.ping()
            except (ConnectionClosed, WebSocketException):
                logger.warning(f"WebSocket connection closed for {connection.chain}")
                connection.is_connected = False
                break
            except Exception as e:
                logger.error(f"Error listening for events on {connection.chain}: {e}")
                break
    
    async def _process_websocket_message(self, chain: str, message: str):
        """Process incoming WebSocket message"""
        try:
            data = json.loads(message)
            
            if 'method' in data and data['method'] == 'eth_subscription':
                # EVM subscription notification
                await self._process_evm_notification(chain, data)
            elif 'method' in data and 'Subscribe' in data['method']:
                # Solana subscription notification
                await self._process_solana_notification(chain, data)
                
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")
    
    async def _process_evm_notification(self, chain: str, data: Dict[str, Any]):
        """Process EVM subscription notification"""
        try:
            params = data.get('params', {})
            result = params.get('result', {})
            
            if 'blockHash' in result and 'number' in result:
                # New block notification
                await self._handle_new_block(chain, result)
            elif 'topics' in result and 'data' in result:
                # Log notification
                await self._handle_new_log(chain, result)
                
        except Exception as e:
            logger.error(f"Error processing EVM notification: {e}")
    
    async def _process_solana_notification(self, chain: str, data: Dict[str, Any]):
        """Process Solana subscription notification"""
        try:
            # Process Solana account changes
            params = data.get('params', {})
            result = params.get('result', {})
            
            if 'value' in result:
                await self._handle_solana_account_change(chain, result)
                
        except Exception as e:
            logger.error(f"Error processing Solana notification: {e}")
    
    async def _handle_new_block(self, chain: str, block_data: Dict[str, Any]):
        """Handle new block notification"""
        block_number = int(block_data.get('number', '0x0'), 16)
        logger.debug(f"New block on {chain}: {block_number}")
        
        # Trigger contract scanning for new blocks
        asyncio.create_task(self._scan_block_for_contracts(chain, block_number))
    
    async def _handle_new_log(self, chain: str, log_data: Dict[str, Any]):
        """Handle new log notification"""
        try:
            # Convert log to event format
            event_data = {
                'address': log_data.get('address'),
                'topics': log_data.get('topics', []),
                'data': log_data.get('data'),
                'blockNumber': int(log_data.get('blockNumber', '0x0'), 16),
                'transactionHash': log_data.get('transactionHash'),
                'logIndex': int(log_data.get('logIndex', '0x0'), 16),
                'timestamp': datetime.utcnow().timestamp()
            }
            
            # Process through event scraper
            await blockchain_event_scraper._process_event(chain, event_data)
            
        except Exception as e:
            logger.error(f"Error handling new log: {e}")
    
    async def _handle_solana_account_change(self, chain: str, change_data: Dict[str, Any]):
        """Handle Solana account change"""
        try:
            # Process Solana account changes as events
            account_data = change_data.get('value', {})
            
            # Create event from account change
            event_data = {
                'address': change_data.get('subscription'),
                'data': account_data,
                'timestamp': datetime.utcnow().timestamp(),
                'event': 'AccountChanged'
            }
            
            await blockchain_event_scraper._process_event(chain, event_data)
            
        except Exception as e:
            logger.error(f"Error handling Solana account change: {e}")
    
    async def _scan_block_for_contracts(self, chain: str, block_number: int):
        """Scan a specific block for new gaming contracts"""
        try:
            await blockchain_event_scraper.scan_new_contracts(chain, block_number)
        except Exception as e:
            logger.error(f"Error scanning block {block_number} on {chain}: {e}")
    
    async def _periodic_contract_scan(self):
        """Periodically scan for new gaming contracts"""
        while self.is_running:
            try:
                for chain in self.connections.keys():
                    await blockchain_event_scraper.scan_new_contracts(chain)
                
                # Wait 10 minutes before next scan
                await asyncio.sleep(600)
                
            except Exception as e:
                logger.error(f"Error in periodic contract scan: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    def add_event_callback(self, callback: Callable[[GameEvent], None]):
        """Add callback for real-time events"""
        self.event_callbacks.append(callback)
    
    def add_monitored_contract(self, contract_address: str):
        """Add contract to real-time monitoring"""
        self.monitored_contracts.add(contract_address.lower())
        blockchain_event_scraper.add_monitored_contract(contract_address)
    
    def remove_monitored_contract(self, contract_address: str):
        """Remove contract from real-time monitoring"""
        self.monitored_contracts.discard(contract_address.lower())
        blockchain_event_scraper.remove_monitored_contract(contract_address)


# Global instance
real_time_monitor = RealTimeBlockchainMonitor()
