"""
Blockchain synchronization module
"""
import asyncio
import logging
from blockchain.rpc import rpc_manager

logger = logging.getLogger(__name__)


async def sync_all_chains():
    """Sync data from all blockchain networks"""
    logger.info("Starting blockchain sync for all chains")
    
    for chain in rpc_manager.connections.keys():
        try:
            await sync_chain(chain)
        except Exception as e:
            logger.error(f"Error syncing {chain}: {e}")
    
    logger.info("Blockchain sync completed")


async def sync_chain(chain: str):
    """Sync data from a specific blockchain"""
    logger.info(f"Syncing {chain} blockchain data")
    
    # Get latest block
    latest_block = await rpc_manager.get_latest_block(chain)
    if latest_block:
        logger.info(f"{chain} latest block: {latest_block}")
    
    # Sync gaming contract data using blockchain data manager
    try:
        from blockchain.data_clients.manager import blockchain_data_manager

        # Get gaming tokens data for this chain
        gaming_data = await blockchain_data_manager.get_gaming_tokens_data([])
        if gaming_data:
            logger.info(f"Synced gaming data for {chain}: {len(gaming_data.get('tokens', []))} tokens")

        # Get comprehensive gaming data if this is the primary chain
        if chain.lower() in ['ethereum', 'polygon', 'bsc']:
            comprehensive_data = await blockchain_data_manager.get_comprehensive_gaming_data()
            if comprehensive_data:
                logger.info(f"Synced comprehensive gaming data for {chain}")

    except Exception as e:
        logger.error(f"Error syncing gaming contracts for {chain}: {e}")
