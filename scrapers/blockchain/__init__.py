"""
Blockchain scrapers module
Advanced blockchain scraping and monitoring for gaming contracts
"""

from .contract_detector import (
    GamingContractDetector,
    ContractAnalysisResult,
    ContractConfidence,
    gaming_contract_detector
)

from .event_scraper import (
    BlockchainEventScraper,
    GameEvent,
    GameEventType,
    blockchain_event_scraper
)

from .real_time_monitor import (
    RealTimeBlockchainMonitor,
    WebSocketConnection,
    real_time_monitor
)

from .ml_classifier import (
    GamingContractMLClassifier,
    ContractFeatures,
    ClassificationResult,
    gaming_ml_classifier
)

from .scraper_manager import (
    BlockchainScraperManager,
    ScrapingConfig,
    ScrapingMode,
    ScrapingStats,
    blockchain_scraper_manager
)

from .sync import sync_all_chains, sync_chain

__all__ = [
    'GamingContractDetector',
    'ContractAnalysisResult',
    'ContractConfidence',
    'gaming_contract_detector',
    'BlockchainEventScraper',
    'GameEvent',
    'GameEventType',
    'blockchain_event_scraper',
    'RealTimeBlockchainMonitor',
    'WebSocketConnection',
    'real_time_monitor',
    'GamingContractMLClassifier',
    'ContractFeatures',
    'ClassificationResult',
    'gaming_ml_classifier',
    'BlockchainScraperManager',
    'ScrapingConfig',
    'ScrapingMode',
    'ScrapingStats',
    'blockchain_scraper_manager',
    'sync_all_chains',
    'sync_chain'
]
