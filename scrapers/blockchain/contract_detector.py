"""
Gaming Contract Detection Engine
Implements heuristics for identifying gaming-related smart contracts
"""
import asyncio
import logging
import re
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime

from blockchain.multi_chain_client import multi_chain_manager
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class ContractConfidence(Enum):
    """Confidence levels for gaming contract classification"""
    HIGH = "high"      # 80%+ confidence
    MEDIUM = "medium"  # 50-80% confidence
    LOW = "low"        # 20-50% confidence
    NONE = "none"      # <20% confidence


@dataclass
class ContractAnalysisResult:
    """Result of contract analysis"""
    contract_address: str
    blockchain: str
    is_gaming: bool
    confidence: ContractConfidence
    confidence_score: float
    detected_patterns: List[str]
    function_signatures: List[str]
    event_signatures: List[str]
    metadata: Dict[str, Any]
    analysis_timestamp: datetime


class GamingContractDetector:
    """Detects gaming contracts using multiple heuristics"""
    
    def __init__(self):
        self.gaming_function_patterns = {
            # Core gaming functions
            'mint', 'burn', 'craft', 'upgrade', 'battle', 'fight',
            'level_up', 'levelup', 'level', 'experience', 'exp',
            'breed', 'hatch', 'evolve', 'transform',
            
            # Player management
            'register_player', 'create_player', 'player_join',
            'join_game', 'start_game', 'end_game',
            
            # Item/Asset management
            'equip', 'unequip', 'use_item', 'consume',
            'repair', 'enhance', 'forge', 'combine',
            
            # Economy/Trading
            'stake', 'unstake', 'claim_rewards', 'harvest',
            'trade', 'marketplace', 'auction',
            
            # Guild/Social
            'create_guild', 'join_guild', 'guild_battle',
            'invite', 'accept_invite',
            
            # Game mechanics
            'roll_dice', 'spin', 'lottery', 'gacha',
            'quest', 'mission', 'achievement'
        }
        
        self.gaming_event_patterns = {
            # Player events
            'PlayerJoined', 'PlayerLeft', 'PlayerRegistered',
            'LevelUp', 'ExperienceGained', 'PlayerCreated',
            
            # Item/NFT events
            'ItemCrafted', 'ItemUpgraded', 'ItemEquipped',
            'ItemUsed', 'ItemBurned', 'ItemMinted',
            
            # Battle/Combat events
            'BattleStarted', 'BattleEnded', 'BattleWon',
            'DamageDealt', 'PlayerDefeated',
            
            # Economy events
            'RewardClaimed', 'TokensEarned', 'Staked',
            'Unstaked', 'Harvested', 'TradeMade',
            
            # Social events
            'GuildCreated', 'GuildJoined', 'GuildBattle',
            'InviteSent', 'InviteAccepted',
            
            # Game mechanics
            'QuestCompleted', 'AchievementUnlocked',
            'LootDropped', 'ChestOpened'
        }
        
        self.gaming_keywords = {
            'game', 'gaming', 'play', 'player', 'nft', 'collectible',
            'metaverse', 'virtual', 'avatar', 'character', 'hero',
            'monster', 'creature', 'pet', 'weapon', 'armor', 'item',
            'quest', 'adventure', 'dungeon', 'raid', 'battle',
            'guild', 'clan', 'team', 'tournament', 'competition',
            'reward', 'prize', 'loot', 'treasure', 'earn', 'p2e',
            'play2earn', 'playtoearn', 'gamefi', 'defi', 'yield'
        }
        
        # Known gaming token standards
        self.gaming_token_standards = {
            'ERC721', 'ERC1155', 'ERC20',  # Ethereum
            'SPL',  # Solana
            'BEP721', 'BEP1155', 'BEP20'  # BSC
        }
        
        # Blacklisted patterns (DeFi/utility contracts)
        self.blacklist_patterns = {
            'swap', 'pool', 'liquidity', 'vault', 'farm',
            'lending', 'borrowing', 'flash_loan', 'oracle',
            'governance', 'voting', 'proposal', 'timelock'
        }
    
    async def analyze_contract(self, contract_address: str, blockchain: str) -> ContractAnalysisResult:
        """Analyze a contract to determine if it's gaming-related"""
        try:
            client = multi_chain_manager.get_client(blockchain)
            if not client:
                raise ValueError(f"No client available for blockchain: {blockchain}")
            
            # Get contract bytecode and ABI if available
            contract_code = await client.get_contract_code(contract_address)
            if not contract_code or contract_code == '0x':
                logger.warning(f"No contract code found for {contract_address}")
                return self._create_negative_result(contract_address, blockchain)
            
            # Analyze function signatures
            function_signatures = await self._extract_function_signatures(client, contract_address)
            
            # Analyze event signatures
            event_signatures = await self._extract_event_signatures(client, contract_address)
            
            # Get contract metadata
            metadata = await self._get_contract_metadata(client, contract_address)
            
            # Perform analysis
            analysis_result = self._analyze_patterns(
                contract_address, blockchain, function_signatures, 
                event_signatures, metadata, contract_code
            )
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error analyzing contract {contract_address}: {e}")
            return self._create_negative_result(contract_address, blockchain)
    
    async def _extract_function_signatures(self, client, contract_address: str) -> List[str]:
        """Extract function signatures from contract"""
        try:
            # This would typically require ABI or bytecode analysis
            # For now, we'll use a simplified approach
            contract_info = await client.get_contract_info(contract_address)
            if contract_info and 'functions' in contract_info:
                return contract_info['functions']
            return []
        except Exception as e:
            logger.debug(f"Could not extract function signatures: {e}")
            return []
    
    async def _extract_event_signatures(self, client, contract_address: str) -> List[str]:
        """Extract event signatures from contract"""
        try:
            # Get recent events from the contract
            recent_events = await client.get_contract_events(contract_address, limit=100)
            if recent_events:
                return [event.get('event', '') for event in recent_events]
            return []
        except Exception as e:
            logger.debug(f"Could not extract event signatures: {e}")
            return []
    
    async def _get_contract_metadata(self, client, contract_address: str) -> Dict[str, Any]:
        """Get contract metadata including name, symbol, etc."""
        try:
            metadata = {}
            
            # Try to get standard ERC20/ERC721 metadata
            try:
                name = await client.call_contract_function(contract_address, 'name', [])
                if name:
                    metadata['name'] = name
            except:
                pass
            
            try:
                symbol = await client.call_contract_function(contract_address, 'symbol', [])
                if symbol:
                    metadata['symbol'] = symbol
            except:
                pass
            
            return metadata
        except Exception as e:
            logger.debug(f"Could not get contract metadata: {e}")
            return {}
    
    def _analyze_patterns(
        self, 
        contract_address: str, 
        blockchain: str,
        function_signatures: List[str], 
        event_signatures: List[str],
        metadata: Dict[str, Any],
        contract_code: str
    ) -> ContractAnalysisResult:
        """Analyze all patterns to determine if contract is gaming-related"""
        
        detected_patterns = []
        confidence_factors = []
        
        # Analyze function signatures
        gaming_functions = self._analyze_function_patterns(function_signatures)
        if gaming_functions:
            detected_patterns.extend([f"gaming_function:{func}" for func in gaming_functions])
            confidence_factors.append(len(gaming_functions) * 0.15)  # Up to 15% per function
        
        # Analyze event signatures
        gaming_events = self._analyze_event_patterns(event_signatures)
        if gaming_events:
            detected_patterns.extend([f"gaming_event:{event}" for event in gaming_events])
            confidence_factors.append(len(gaming_events) * 0.12)  # Up to 12% per event
        
        # Analyze metadata
        metadata_score = self._analyze_metadata_patterns(metadata)
        if metadata_score > 0:
            detected_patterns.append("gaming_metadata")
            confidence_factors.append(metadata_score)
        
        # Check for blacklisted patterns
        blacklist_score = self._check_blacklist_patterns(function_signatures, event_signatures, metadata)
        if blacklist_score > 0:
            detected_patterns.append("blacklisted_patterns")
            confidence_factors.append(-blacklist_score)  # Negative score
        
        # Calculate final confidence score
        confidence_score = min(1.0, max(0.0, sum(confidence_factors)))
        
        # Determine confidence level and gaming classification
        if confidence_score >= 0.8:
            confidence = ContractConfidence.HIGH
            is_gaming = True
        elif confidence_score >= 0.5:
            confidence = ContractConfidence.MEDIUM
            is_gaming = True
        elif confidence_score >= 0.2:
            confidence = ContractConfidence.LOW
            is_gaming = True
        else:
            confidence = ContractConfidence.NONE
            is_gaming = False
        
        return ContractAnalysisResult(
            contract_address=contract_address,
            blockchain=blockchain,
            is_gaming=is_gaming,
            confidence=confidence,
            confidence_score=confidence_score,
            detected_patterns=detected_patterns,
            function_signatures=function_signatures,
            event_signatures=event_signatures,
            metadata=metadata,
            analysis_timestamp=datetime.utcnow()
        )
    
    def _analyze_function_patterns(self, function_signatures: List[str]) -> List[str]:
        """Analyze function signatures for gaming patterns"""
        gaming_functions = []
        
        for func in function_signatures:
            func_lower = func.lower()
            for pattern in self.gaming_function_patterns:
                if pattern in func_lower:
                    gaming_functions.append(func)
                    break
        
        return gaming_functions
    
    def _analyze_event_patterns(self, event_signatures: List[str]) -> List[str]:
        """Analyze event signatures for gaming patterns"""
        gaming_events = []
        
        for event in event_signatures:
            for pattern in self.gaming_event_patterns:
                if pattern.lower() in event.lower():
                    gaming_events.append(event)
                    break
        
        return gaming_events
    
    def _analyze_metadata_patterns(self, metadata: Dict[str, Any]) -> float:
        """Analyze metadata for gaming keywords"""
        score = 0.0
        
        for key, value in metadata.items():
            if isinstance(value, str):
                value_lower = value.lower()
                for keyword in self.gaming_keywords:
                    if keyword in value_lower:
                        score += 0.1  # 10% per keyword match
        
        return min(0.4, score)  # Cap at 40%
    
    def _check_blacklist_patterns(
        self, 
        function_signatures: List[str], 
        event_signatures: List[str],
        metadata: Dict[str, Any]
    ) -> float:
        """Check for blacklisted DeFi/utility patterns"""
        blacklist_score = 0.0
        
        all_text = ' '.join(function_signatures + event_signatures + 
                           [str(v) for v in metadata.values()])
        all_text_lower = all_text.lower()
        
        for pattern in self.blacklist_patterns:
            if pattern in all_text_lower:
                blacklist_score += 0.2  # 20% penalty per blacklisted pattern
        
        return min(0.6, blacklist_score)  # Cap at 60% penalty
    
    def _create_negative_result(self, contract_address: str, blockchain: str) -> ContractAnalysisResult:
        """Create a negative analysis result"""
        return ContractAnalysisResult(
            contract_address=contract_address,
            blockchain=blockchain,
            is_gaming=False,
            confidence=ContractConfidence.NONE,
            confidence_score=0.0,
            detected_patterns=[],
            function_signatures=[],
            event_signatures=[],
            metadata={},
            analysis_timestamp=datetime.utcnow()
        )


# Global instance
gaming_contract_detector = GamingContractDetector()
