"""
Blockchain Event Scraper
Monitors and collects gaming-related events from multiple blockchains
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import json

from blockchain.multi_chain_client import multi_chain_manager
from .contract_detector import gaming_contract_detector, ContractConfidence
from models.gaming import BlockchainData
from models.crud import create_blockchain_data
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class GameEventType(Enum):
    """Types of gaming events to monitor"""
    PLAYER_ACTION = "player_action"
    ITEM_TRANSACTION = "item_transaction"
    BATTLE_EVENT = "battle_event"
    ECONOMY_EVENT = "economy_event"
    SOCIAL_EVENT = "social_event"
    CONTRACT_DEPLOYMENT = "contract_deployment"
    TOKEN_MINT = "token_mint"
    NFT_TRANSFER = "nft_transfer"


@dataclass
class GameEvent:
    """Represents a gaming-related blockchain event"""
    event_id: str
    blockchain: str
    block_number: int
    transaction_hash: str
    contract_address: str
    event_type: GameEventType
    event_name: str
    event_data: Dict[str, Any]
    timestamp: datetime
    gas_used: Optional[int] = None
    gas_price: Optional[float] = None
    from_address: Optional[str] = None
    to_address: Optional[str] = None


class BlockchainEventScraper:
    """Scrapes gaming events from multiple blockchains"""
    
    def __init__(self):
        self.monitored_contracts: Set[str] = set()
        self.event_filters = {
            GameEventType.PLAYER_ACTION: [
                'PlayerJoined', 'PlayerLeft', 'PlayerRegistered',
                'LevelUp', 'ExperienceGained', 'PlayerCreated'
            ],
            GameEventType.ITEM_TRANSACTION: [
                'ItemCrafted', 'ItemUpgraded', 'ItemEquipped',
                'ItemUsed', 'ItemBurned', 'ItemMinted', 'Transfer'
            ],
            GameEventType.BATTLE_EVENT: [
                'BattleStarted', 'BattleEnded', 'BattleWon',
                'DamageDealt', 'PlayerDefeated'
            ],
            GameEventType.ECONOMY_EVENT: [
                'RewardClaimed', 'TokensEarned', 'Staked',
                'Unstaked', 'Harvested', 'TradeMade'
            ],
            GameEventType.SOCIAL_EVENT: [
                'GuildCreated', 'GuildJoined', 'GuildBattle',
                'InviteSent', 'InviteAccepted'
            ]
        }
        self.last_processed_blocks = {}
    
    async def start_monitoring(self, chains: List[str] = None):
        """Start monitoring gaming events across specified chains"""
        if chains is None:
            chains = multi_chain_manager.get_gaming_chains()
        
        logger.info(f"Starting event monitoring for chains: {chains}")
        
        # Initialize last processed blocks
        for chain in chains:
            client = multi_chain_manager.get_client(chain)
            if client:
                latest_block = await client.get_latest_block_number()
                self.last_processed_blocks[chain] = latest_block
        
        # Start monitoring tasks
        tasks = []
        for chain in chains:
            task = asyncio.create_task(self._monitor_chain_events(chain))
            tasks.append(task)
        
        # Wait for all monitoring tasks
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _monitor_chain_events(self, chain: str):
        """Monitor events for a specific blockchain"""
        logger.info(f"Starting event monitoring for {chain}")
        
        while True:
            try:
                client = multi_chain_manager.get_client(chain)
                if not client:
                    logger.error(f"No client available for {chain}")
                    await asyncio.sleep(60)
                    continue
                
                # Get latest block
                latest_block = await client.get_latest_block_number()
                last_processed = self.last_processed_blocks.get(chain, latest_block - 10)
                
                if latest_block > last_processed:
                    # Process new blocks
                    await self._process_blocks(chain, last_processed + 1, latest_block)
                    self.last_processed_blocks[chain] = latest_block
                
                # Wait before next check
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error monitoring {chain} events: {e}")
                await asyncio.sleep(60)
    
    async def _process_blocks(self, chain: str, from_block: int, to_block: int):
        """Process blocks for gaming events"""
        logger.debug(f"Processing {chain} blocks {from_block} to {to_block}")
        
        client = multi_chain_manager.get_client(chain)
        if not client:
            return
        
        try:
            # Get events from blocks
            events = await client.get_events_in_range(from_block, to_block)
            
            for event_data in events:
                await self._process_event(chain, event_data)
                
        except Exception as e:
            logger.error(f"Error processing blocks {from_block}-{to_block} on {chain}: {e}")
    
    async def _process_event(self, chain: str, event_data: Dict[str, Any]):
        """Process a single blockchain event"""
        try:
            contract_address = event_data.get('address', '').lower()
            event_name = event_data.get('event', '')
            
            # Check if this is a gaming-related event
            event_type = self._classify_event(event_name)
            if not event_type:
                return
            
            # Check if contract is gaming-related (with caching)
            if contract_address not in self.monitored_contracts:
                analysis = await gaming_contract_detector.analyze_contract(contract_address, chain)
                if analysis.confidence in [ContractConfidence.HIGH, ContractConfidence.MEDIUM]:
                    self.monitored_contracts.add(contract_address)
                else:
                    return  # Skip non-gaming contracts
            
            # Create game event
            game_event = GameEvent(
                event_id=f"{chain}_{event_data.get('transactionHash', '')}_{event_data.get('logIndex', 0)}",
                blockchain=chain,
                block_number=event_data.get('blockNumber', 0),
                transaction_hash=event_data.get('transactionHash', ''),
                contract_address=contract_address,
                event_type=event_type,
                event_name=event_name,
                event_data=event_data.get('args', {}),
                timestamp=datetime.fromtimestamp(event_data.get('timestamp', 0)),
                gas_used=event_data.get('gasUsed'),
                gas_price=event_data.get('gasPrice'),
                from_address=event_data.get('from'),
                to_address=event_data.get('to')
            )
            
            # Save to database
            await self._save_game_event(game_event)
            
            logger.debug(f"Processed gaming event: {event_name} on {chain}")
            
        except Exception as e:
            logger.error(f"Error processing event: {e}")
    
    def _classify_event(self, event_name: str) -> Optional[GameEventType]:
        """Classify event by name to determine if it's gaming-related"""
        event_name_lower = event_name.lower()
        
        for event_type, patterns in self.event_filters.items():
            for pattern in patterns:
                if pattern.lower() in event_name_lower:
                    return event_type
        
        # Special cases for common events
        if event_name in ['Transfer', 'Approval']:
            return GameEventType.NFT_TRANSFER
        
        return None
    
    async def _save_game_event(self, game_event: GameEvent):
        """Save game event to database"""
        try:
            # Create blockchain data entry
            blockchain_data = {
                'blockchain': game_event.blockchain,
                'block_number': game_event.block_number,
                'transaction_hash': game_event.transaction_hash,
                'contract_address': game_event.contract_address,
                'event_type': game_event.event_name,
                'event_data': game_event.event_data,
                'block_timestamp': game_event.timestamp,
                'gas_used': game_event.gas_used,
                'gas_price': game_event.gas_price,
                'from_address': game_event.from_address,
                'to_address': game_event.to_address
            }
            
            # Save to database (this would need a database session)
            # For now, we'll log the event
            logger.info(f"Gaming event saved: {game_event.event_name} on {game_event.blockchain}")
            
        except Exception as e:
            logger.error(f"Error saving game event: {e}")
    
    async def scan_new_contracts(self, chain: str, from_block: int = None):
        """Scan for new contract deployments that might be gaming-related"""
        try:
            client = multi_chain_manager.get_client(chain)
            if not client:
                return
            
            if from_block is None:
                latest_block = await client.get_latest_block_number()
                from_block = latest_block - 1000  # Scan last 1000 blocks
            
            # Get contract creation transactions
            contract_creations = await client.get_contract_creations(from_block)
            
            for creation in contract_creations:
                contract_address = creation.get('contractAddress')
                if contract_address:
                    # Analyze if it's a gaming contract
                    analysis = await gaming_contract_detector.analyze_contract(contract_address, chain)
                    
                    if analysis.is_gaming:
                        logger.info(f"New gaming contract detected: {contract_address} on {chain}")
                        self.monitored_contracts.add(contract_address.lower())
                        
                        # Create contract deployment event
                        deployment_event = GameEvent(
                            event_id=f"{chain}_{creation.get('transactionHash', '')}_deployment",
                            blockchain=chain,
                            block_number=creation.get('blockNumber', 0),
                            transaction_hash=creation.get('transactionHash', ''),
                            contract_address=contract_address.lower(),
                            event_type=GameEventType.CONTRACT_DEPLOYMENT,
                            event_name='ContractDeployed',
                            event_data={
                                'confidence': analysis.confidence.value,
                                'confidence_score': analysis.confidence_score,
                                'detected_patterns': analysis.detected_patterns
                            },
                            timestamp=datetime.fromtimestamp(creation.get('timestamp', 0)),
                            from_address=creation.get('from')
                        )
                        
                        await self._save_game_event(deployment_event)
                        
        except Exception as e:
            logger.error(f"Error scanning new contracts on {chain}: {e}")
    
    async def get_gaming_events(
        self, 
        chain: str = None, 
        event_type: GameEventType = None,
        from_timestamp: datetime = None,
        to_timestamp: datetime = None,
        limit: int = 100
    ) -> List[GameEvent]:
        """Retrieve gaming events with filters"""
        # This would query the database for stored events
        # For now, return empty list as placeholder
        return []
    
    def add_monitored_contract(self, contract_address: str):
        """Add a contract to the monitoring list"""
        self.monitored_contracts.add(contract_address.lower())
    
    def remove_monitored_contract(self, contract_address: str):
        """Remove a contract from the monitoring list"""
        self.monitored_contracts.discard(contract_address.lower())


# Global instance
blockchain_event_scraper = BlockchainEventScraper()
