"""
Machine Learning Gaming Contract Classifier
Uses ML to classify contracts as gaming vs non-gaming with confidence scoring
"""
import asyncio
import logging
import pickle
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import os

logger = logging.getLogger(__name__)

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, accuracy_score
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn not available. ML classification will be disabled.")

from .contract_detector import gaming_contract_detector, ContractAnalysisResult
from blockchain.multi_chain_client import multi_chain_manager
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class ContractFeatures:
    """Features extracted from a contract for ML classification"""
    contract_address: str
    blockchain: str
    
    # Code features
    bytecode_size: int
    function_count: int
    event_count: int
    
    # Function signature features
    gaming_function_ratio: float
    defi_function_ratio: float
    standard_function_ratio: float
    
    # Event signature features
    gaming_event_ratio: float
    transfer_event_count: int
    
    # Metadata features
    name_gaming_score: float
    symbol_gaming_score: float
    
    # Behavioral features
    transaction_count_24h: int
    unique_users_24h: int
    avg_gas_usage: float
    
    # Token standard features
    is_erc721: bool
    is_erc1155: bool
    is_erc20: bool
    
    # Network features
    deployment_age_days: int
    creator_contract_count: int


@dataclass
class ClassificationResult:
    """Result of ML classification"""
    contract_address: str
    blockchain: str
    is_gaming_probability: float
    confidence_score: float
    feature_importance: Dict[str, float]
    classification_timestamp: datetime


class GamingContractMLClassifier:
    """Machine Learning classifier for gaming contracts"""
    
    def __init__(self):
        self.model: Optional[RandomForestClassifier] = None
        self.vectorizer: Optional[TfidfVectorizer] = None
        self.scaler: Optional[StandardScaler] = None
        self.is_trained = False
        self.model_path = "models/gaming_contract_classifier.pkl"
        self.vectorizer_path = "models/gaming_contract_vectorizer.pkl"
        self.scaler_path = "models/gaming_contract_scaler.pkl"
        
        # Gaming keywords for text features
        self.gaming_keywords = {
            'game', 'gaming', 'play', 'player', 'nft', 'collectible',
            'metaverse', 'virtual', 'avatar', 'character', 'hero',
            'monster', 'creature', 'pet', 'weapon', 'armor', 'item',
            'quest', 'adventure', 'dungeon', 'raid', 'battle',
            'guild', 'clan', 'team', 'tournament', 'competition',
            'reward', 'prize', 'loot', 'treasure', 'earn', 'p2e'
        }
        
        # DeFi keywords for contrast
        self.defi_keywords = {
            'swap', 'pool', 'liquidity', 'vault', 'farm', 'yield',
            'lending', 'borrowing', 'flash', 'oracle', 'governance',
            'voting', 'proposal', 'timelock', 'multisig'
        }
        
        # Standard function signatures
        self.standard_functions = {
            'transfer', 'approve', 'balanceof', 'allowance',
            'totalsupply', 'name', 'symbol', 'decimals'
        }
        
        if SKLEARN_AVAILABLE:
            self._load_model()
    
    def _load_model(self):
        """Load trained model from disk"""
        try:
            if (os.path.exists(self.model_path) and 
                os.path.exists(self.vectorizer_path) and 
                os.path.exists(self.scaler_path)):
                
                with open(self.model_path, 'rb') as f:
                    self.model = pickle.load(f)
                
                with open(self.vectorizer_path, 'rb') as f:
                    self.vectorizer = pickle.load(f)
                
                with open(self.scaler_path, 'rb') as f:
                    self.scaler = pickle.load(f)
                
                self.is_trained = True
                logger.info("Loaded trained gaming contract classifier")
            else:
                logger.info("No trained model found. Will need to train first.")
                
        except Exception as e:
            logger.error(f"Error loading model: {e}")
    
    def _save_model(self):
        """Save trained model to disk"""
        try:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            
            with open(self.model_path, 'wb') as f:
                pickle.dump(self.model, f)
            
            with open(self.vectorizer_path, 'wb') as f:
                pickle.dump(self.vectorizer, f)
            
            with open(self.scaler_path, 'wb') as f:
                pickle.dump(self.scaler, f)
            
            logger.info("Saved trained gaming contract classifier")
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
    
    async def extract_features(self, contract_address: str, blockchain: str) -> Optional[ContractFeatures]:
        """Extract features from a contract for ML classification"""
        try:
            client = multi_chain_manager.get_client(blockchain)
            if not client:
                return None
            
            # Get basic contract info
            contract_code = await client.get_contract_code(contract_address)
            if not contract_code or contract_code == '0x':
                return None
            
            # Get contract metadata
            metadata = {}
            try:
                name = await client.call_contract_function(contract_address, 'name', [])
                symbol = await client.call_contract_function(contract_address, 'symbol', [])
                metadata = {'name': name or '', 'symbol': symbol or ''}
            except:
                pass
            
            # Get function and event signatures
            function_signatures = await self._get_function_signatures(client, contract_address)
            event_signatures = await self._get_event_signatures(client, contract_address)
            
            # Get behavioral data
            behavioral_data = await self._get_behavioral_data(client, contract_address)
            
            # Calculate features
            features = ContractFeatures(
                contract_address=contract_address,
                blockchain=blockchain,
                
                # Code features
                bytecode_size=len(contract_code) // 2,  # Convert hex to bytes
                function_count=len(function_signatures),
                event_count=len(event_signatures),
                
                # Function signature features
                gaming_function_ratio=self._calculate_gaming_function_ratio(function_signatures),
                defi_function_ratio=self._calculate_defi_function_ratio(function_signatures),
                standard_function_ratio=self._calculate_standard_function_ratio(function_signatures),
                
                # Event signature features
                gaming_event_ratio=self._calculate_gaming_event_ratio(event_signatures),
                transfer_event_count=self._count_transfer_events(event_signatures),
                
                # Metadata features
                name_gaming_score=self._calculate_text_gaming_score(metadata.get('name', '')),
                symbol_gaming_score=self._calculate_text_gaming_score(metadata.get('symbol', '')),
                
                # Behavioral features
                transaction_count_24h=behavioral_data.get('transaction_count_24h', 0),
                unique_users_24h=behavioral_data.get('unique_users_24h', 0),
                avg_gas_usage=behavioral_data.get('avg_gas_usage', 0),
                
                # Token standard features
                is_erc721=self._check_erc721(function_signatures),
                is_erc1155=self._check_erc1155(function_signatures),
                is_erc20=self._check_erc20(function_signatures),
                
                # Network features
                deployment_age_days=behavioral_data.get('deployment_age_days', 0),
                creator_contract_count=behavioral_data.get('creator_contract_count', 0)
            )
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features for {contract_address}: {e}")
            return None
    
    async def _get_function_signatures(self, client, contract_address: str) -> List[str]:
        """Get function signatures from contract"""
        try:
            # This would require ABI analysis or bytecode disassembly
            # For now, return empty list as placeholder
            return []
        except:
            return []
    
    async def _get_event_signatures(self, client, contract_address: str) -> List[str]:
        """Get event signatures from contract"""
        try:
            # Get recent events to infer signatures
            events = await client.get_contract_events(contract_address, limit=100)
            return [event.get('event', '') for event in events if event.get('event')]
        except:
            return []
    
    async def _get_behavioral_data(self, client, contract_address: str) -> Dict[str, Any]:
        """Get behavioral data for the contract"""
        try:
            # This would require historical transaction analysis
            # For now, return default values
            return {
                'transaction_count_24h': 0,
                'unique_users_24h': 0,
                'avg_gas_usage': 0,
                'deployment_age_days': 0,
                'creator_contract_count': 0
            }
        except:
            return {}
    
    def _calculate_gaming_function_ratio(self, functions: List[str]) -> float:
        """Calculate ratio of gaming-related functions"""
        if not functions:
            return 0.0
        
        gaming_count = 0
        for func in functions:
            func_lower = func.lower()
            for keyword in self.gaming_keywords:
                if keyword in func_lower:
                    gaming_count += 1
                    break
        
        return gaming_count / len(functions)
    
    def _calculate_defi_function_ratio(self, functions: List[str]) -> float:
        """Calculate ratio of DeFi-related functions"""
        if not functions:
            return 0.0
        
        defi_count = 0
        for func in functions:
            func_lower = func.lower()
            for keyword in self.defi_keywords:
                if keyword in func_lower:
                    defi_count += 1
                    break
        
        return defi_count / len(functions)
    
    def _calculate_standard_function_ratio(self, functions: List[str]) -> float:
        """Calculate ratio of standard ERC functions"""
        if not functions:
            return 0.0
        
        standard_count = 0
        for func in functions:
            func_lower = func.lower()
            if func_lower in self.standard_functions:
                standard_count += 1
        
        return standard_count / len(functions)
    
    def _calculate_gaming_event_ratio(self, events: List[str]) -> float:
        """Calculate ratio of gaming-related events"""
        if not events:
            return 0.0
        
        gaming_count = 0
        for event in events:
            event_lower = event.lower()
            for keyword in self.gaming_keywords:
                if keyword in event_lower:
                    gaming_count += 1
                    break
        
        return gaming_count / len(events)
    
    def _count_transfer_events(self, events: List[str]) -> int:
        """Count transfer events"""
        return sum(1 for event in events if 'transfer' in event.lower())
    
    def _calculate_text_gaming_score(self, text: str) -> float:
        """Calculate gaming score for text (name/symbol)"""
        if not text:
            return 0.0
        
        text_lower = text.lower()
        score = 0.0
        
        for keyword in self.gaming_keywords:
            if keyword in text_lower:
                score += 1.0
        
        return min(1.0, score / 3)  # Normalize to 0-1
    
    def _check_erc721(self, functions: List[str]) -> bool:
        """Check if contract implements ERC721"""
        erc721_functions = {'ownerof', 'approve', 'transferfrom', 'safetransferfrom'}
        functions_lower = {f.lower() for f in functions}
        return len(erc721_functions.intersection(functions_lower)) >= 3
    
    def _check_erc1155(self, functions: List[str]) -> bool:
        """Check if contract implements ERC1155"""
        erc1155_functions = {'balanceofbatch', 'safebatchtransferfrom', 'setapprovalforall'}
        functions_lower = {f.lower() for f in functions}
        return len(erc1155_functions.intersection(functions_lower)) >= 2
    
    def _check_erc20(self, functions: List[str]) -> bool:
        """Check if contract implements ERC20"""
        erc20_functions = {'transfer', 'approve', 'balanceof', 'allowance'}
        functions_lower = {f.lower() for f in functions}
        return len(erc20_functions.intersection(functions_lower)) >= 3
    
    def _features_to_array(self, features: ContractFeatures) -> np.ndarray:
        """Convert features to numpy array for ML model"""
        return np.array([
            features.bytecode_size,
            features.function_count,
            features.event_count,
            features.gaming_function_ratio,
            features.defi_function_ratio,
            features.standard_function_ratio,
            features.gaming_event_ratio,
            features.transfer_event_count,
            features.name_gaming_score,
            features.symbol_gaming_score,
            features.transaction_count_24h,
            features.unique_users_24h,
            features.avg_gas_usage,
            int(features.is_erc721),
            int(features.is_erc1155),
            int(features.is_erc20),
            features.deployment_age_days,
            features.creator_contract_count
        ]).reshape(1, -1)
    
    async def classify_contract(self, contract_address: str, blockchain: str) -> Optional[ClassificationResult]:
        """Classify a contract using ML model"""
        if not SKLEARN_AVAILABLE or not self.is_trained:
            logger.warning("ML classifier not available or not trained")
            return None
        
        try:
            # Extract features
            features = await self.extract_features(contract_address, blockchain)
            if not features:
                return None
            
            # Convert to array and scale
            feature_array = self._features_to_array(features)
            scaled_features = self.scaler.transform(feature_array)
            
            # Make prediction
            probability = self.model.predict_proba(scaled_features)[0]
            is_gaming_prob = probability[1] if len(probability) > 1 else probability[0]
            
            # Calculate confidence score
            confidence_score = max(probability) - min(probability) if len(probability) > 1 else probability[0]
            
            # Get feature importance
            feature_names = [
                'bytecode_size', 'function_count', 'event_count',
                'gaming_function_ratio', 'defi_function_ratio', 'standard_function_ratio',
                'gaming_event_ratio', 'transfer_event_count',
                'name_gaming_score', 'symbol_gaming_score',
                'transaction_count_24h', 'unique_users_24h', 'avg_gas_usage',
                'is_erc721', 'is_erc1155', 'is_erc20',
                'deployment_age_days', 'creator_contract_count'
            ]
            
            feature_importance = dict(zip(feature_names, self.model.feature_importances_))
            
            return ClassificationResult(
                contract_address=contract_address,
                blockchain=blockchain,
                is_gaming_probability=float(is_gaming_prob),
                confidence_score=float(confidence_score),
                feature_importance=feature_importance,
                classification_timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Error classifying contract {contract_address}: {e}")
            return None
    
    async def train_model(self, training_data: List[Tuple[ContractFeatures, bool]]):
        """Train the ML model with labeled data"""
        if not SKLEARN_AVAILABLE:
            logger.error("scikit-learn not available for training")
            return
        
        try:
            # Prepare training data
            X = []
            y = []
            
            for features, is_gaming in training_data:
                feature_array = self._features_to_array(features).flatten()
                X.append(feature_array)
                y.append(int(is_gaming))
            
            X = np.array(X)
            y = np.array(y)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Scale features
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train model
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                class_weight='balanced'
            )
            
            self.model.fit(X_train_scaled, y_train)
            
            # Evaluate model
            y_pred = self.model.predict(X_test_scaled)
            accuracy = accuracy_score(y_test, y_pred)
            
            logger.info(f"Model trained with accuracy: {accuracy:.3f}")
            logger.info(f"Classification report:\n{classification_report(y_test, y_pred)}")
            
            self.is_trained = True
            self._save_model()
            
        except Exception as e:
            logger.error(f"Error training model: {e}")


# Global instance
gaming_ml_classifier = GamingContractMLClassifier()
