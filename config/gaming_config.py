"""
Gaming Project Configuration System
Parses CSV data and provides dynamic gaming project configuration
"""
import csv
import logging
import os
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from pathlib import Path
from datetime import datetime
import json

logger = logging.getLogger(__name__)


@dataclass
class TokenInfo:
    """Token information for gaming projects"""
    symbol: str
    contract_address: str
    token_type: str  # Utility, Governance, Utility and Governance, etc.
    coingecko_link: str = ""
    blockchain_scanner_link: str = ""
    total_supply: Optional[str] = None


@dataclass
class NFTInfo:
    """NFT information for gaming projects"""
    contract_address: str
    blockchain_scanner_link: str = ""
    marketplace_link: str = ""
    function: str = ""  # What the NFTs are used for
    name: str = ""
    holders_count: Optional[int] = None


@dataclass
class GamingProjectConfig:
    """Complete gaming project configuration"""
    # Basic project info
    project_name: str
    website: str
    blockchain: str
    status: str  # Live, Beta, Coming Soon, etc.
    
    # Documentation
    whitepaper_link: str = ""
    token_schedule_link: str = ""
    
    # Tokens (can have multiple)
    tokens: List[TokenInfo] = field(default_factory=list)
    
    # NFTs
    nfts: List[NFTInfo] = field(default_factory=list)
    
    # Game classification
    genre: str = ""
    sub_genre: str = ""
    game_style: List[str] = field(default_factory=list)
    
    # Social media and community
    twitter_link: str = ""
    discord_link: str = ""
    telegram_link: str = ""
    medium_link: str = ""
    youtube_link: str = ""
    linkedin_link: str = ""
    facebook_link: str = ""
    reddit_link: str = ""
    
    # Metrics
    daily_active_users: Optional[int] = None
    daily_unique_wallets: Optional[int] = None
    dau_source: str = ""
    uaw_source: str = ""
    
    # Auto-clicker specific (for Telegram games)
    auto_clicker_bot: str = ""
    auto_clicker_community: str = ""
    auto_clicker_membership: Optional[int] = None
    
    # Additional metadata
    developer: str = ""
    platform: str = ""
    notes: str = ""
    
    # Internal tracking
    enabled: bool = True
    last_updated: datetime = field(default_factory=datetime.now)
    
    def get_primary_token(self) -> Optional[TokenInfo]:
        """Get the primary token for this project"""
        return self.tokens[0] if self.tokens else None
    
    def get_primary_nft(self) -> Optional[NFTInfo]:
        """Get the primary NFT for this project"""
        return self.nfts[0] if self.nfts else None
    
    def get_blockchain_list(self) -> List[str]:
        """Get list of blockchains this project operates on"""
        blockchains = set()
        if self.blockchain:
            # Handle comma-separated blockchains
            for chain in self.blockchain.split(','):
                blockchains.add(chain.strip().lower())
        return list(blockchains)
    
    def is_live(self) -> bool:
        """Check if the project is live/active"""
        return self.status.lower() in ['live', 'beta - closer to its final form']
    
    def has_tokens(self) -> bool:
        """Check if project has any tokens"""
        return len(self.tokens) > 0
    
    def has_nfts(self) -> bool:
        """Check if project has any NFTs"""
        return len(self.nfts) > 0


class GamingProjectManager:
    """Manages gaming project configurations from CSV data"""
    
    def __init__(self, csv_path: str = "GamingDBDraftResponses.csv"):
        self.csv_path = csv_path
        self.projects: Dict[str, GamingProjectConfig] = {}
        self.projects_by_blockchain: Dict[str, List[str]] = {}
        self.projects_by_status: Dict[str, List[str]] = {}
        self.projects_by_genre: Dict[str, List[str]] = {}
        self._load_projects()
    
    def _load_projects(self):
        """Load gaming projects from CSV file"""
        if not os.path.exists(self.csv_path):
            logger.warning(f"Gaming projects CSV file not found: {self.csv_path}")
            return
        
        try:
            with open(self.csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row in reader:
                    project = self._parse_project_row(row)
                    if project:
                        self._register_project(project)
            
            logger.info(f"✅ Loaded {len(self.projects)} gaming projects from CSV")
            
        except Exception as e:
            logger.error(f"❌ Failed to load gaming projects from CSV: {e}")
    
    def _parse_project_row(self, row: Dict[str, str]) -> Optional[GamingProjectConfig]:
        """Parse a single CSV row into a GamingProjectConfig"""
        try:
            project_name = row.get('Project Name', '').strip()
            if not project_name or project_name.lower() in ['testgame', 'redandgreen']:
                # Skip test entries
                return None
            
            # Create project config
            project = GamingProjectConfig(
                project_name=project_name,
                website=row.get('Project Website Link', '').strip(),
                blockchain=row.get('Blockchain', '').strip(),
                status=row.get('Validated Game Status', '').strip(),
                whitepaper_link=row.get('Whitepaper Link', '').strip(),
                token_schedule_link=row.get('Token Schedule Link', '').strip(),
                genre=row.get('Which genre best fits this title? Choose only ONE', '').strip(),
                twitter_link=row.get('Twitter (X) link', '').strip(),
                discord_link=row.get('Discord Server Link', '').strip(),
                telegram_link=row.get('Telegram Channel Link', '').strip(),
                medium_link=row.get('Medium Link', '').strip(),
                youtube_link=row.get('Youtube Channel', '').strip(),
                linkedin_link=row.get('LinkedIn', '').strip(),
                facebook_link=row.get('Facebook Page', '').strip(),
                reddit_link=row.get('Reddit - Subreddit or User', '').strip(),
                developer=row.get('Developer', '').strip(),
                platform=row.get('Platform', '').strip(),
                notes=row.get('Notes and Comments', '').strip()
            )
            
            # Parse game style
            game_style_str = row.get('Game Style (check all that apply)', '').strip()
            if game_style_str:
                project.game_style = [style.strip() for style in game_style_str.split(',')]
            
            # Parse sub-genre (find the first non-empty sub-genre field)
            sub_genre_fields = [
                'Choose ONE Action sub-genre that best fits this game',
                'Choose ONE Action Adventure sub-genre that best fits this game',
                'Choose ONE Action RPG sub-genre that best fits this game',
                'Choose ONE Adventure sub-genre that best fits this game',
                'Choose ONE Battle Royale sub-genre that best fits this game',
                'Choose ONE Casual sub-genre that best fits this game',
                'Choose ONE Fighting sub-genre that best fits this game',
                'Choose ONE FPS sub-genre that best fits this game',
                'Choose ONE MMORPG sub-genre that best fits this game',
                'Choose ONE Party sub-genre that best fits this game',
                'Choose ONE Platformer sub-genre that best fits this game',
                'Choose ONE Puzzle sub-genre that best fits this game',
                'Choose ONE Racing sub-genre that best fits this game',
                'Choose ONE RTS sub-genre that best fits this game',
                'Choose ONE RPG sub-genre that best fits this game',
                'Choose ONE Shooter sub-genre that best fits this game',
                'Choose ONE Simulation sub-genre that best fits this game',
                'Choose ONE Sports sub-genre that best fits this game',
                'Choose ONE stealth sub-genre that best fits this game',
                'Choose ONE Strategy sub-genre that best fits this game',
                'Choose ONE Survival sub-genre that best fits this game',
                'Choose ONE Tactical RPG sub-genre that best fits this game'
            ]
            
            for field in sub_genre_fields:
                sub_genre = row.get(field, '').strip()
                if sub_genre:
                    project.sub_genre = sub_genre
                    break
            
            # Parse tokens
            self._parse_tokens(row, project)
            
            # Parse NFTs
            self._parse_nfts(row, project)
            
            # Parse metrics
            self._parse_metrics(row, project)
            
            # Parse auto-clicker info
            self._parse_auto_clicker_info(row, project)
            
            return project
            
        except Exception as e:
            logger.error(f"Failed to parse project row: {e}")
            return None

    def _parse_tokens(self, row: Dict[str, str], project: GamingProjectConfig):
        """Parse token information from CSV row"""
        # Token 1 - using correct CSV column names
        token1_symbol = row.get('Token 1 - Symbol', '').strip()
        token1_address = row.get('Token 1 - Token Contract Address', '').strip()
        token1_type = row.get('Token 1 - Type', '').strip()
        token1_coingecko = row.get('Token 1 Link to CoinGecko Listing', '').strip()
        token1_scanner = row.get('Token 1 Blockchain Scanner link', '').strip()
        token1_supply = row.get('Token 1 Total Supply', '').strip()

        if token1_symbol and token1_address:
            project.tokens.append(TokenInfo(
                symbol=token1_symbol,
                contract_address=token1_address,
                token_type=token1_type,
                coingecko_link=token1_coingecko,
                blockchain_scanner_link=token1_scanner,
                total_supply=token1_supply if token1_supply else None
            ))

        # Token 2 - using correct CSV column names
        token2_symbol = row.get('Token 2 - Symbol', '').strip()
        token2_address = row.get('Token 2 - Token Contract Address', '').strip()
        token2_type = row.get('Token 2 - Type', '').strip()
        token2_coingecko = row.get('Token 2 Link to CoinGecko Listing', '').strip()
        token2_scanner = row.get('Token 2 - Blockchain Scanner Link', '').strip()
        token2_supply = row.get('Token 2 Total Supply', '').strip()

        if token2_symbol and token2_address:
            project.tokens.append(TokenInfo(
                symbol=token2_symbol,
                contract_address=token2_address,
                token_type=token2_type,
                coingecko_link=token2_coingecko,
                blockchain_scanner_link=token2_scanner,
                total_supply=token2_supply if token2_supply else None
            ))

    def _parse_nfts(self, row: Dict[str, str], project: GamingProjectConfig):
        """Parse NFT information from CSV row"""
        # Use correct CSV column names
        nft_address = row.get('What is the NFT Contract Address?', '').strip()
        nft_scanner = row.get('Blockchain scanner link of NFT Contract Address', '').strip()
        nft_marketplace = row.get('Marketplace link for NFTs for this game', '').strip()
        nft_function = row.get('What is the function of the NFTs?', '').strip()
        nft_name = row.get('NFT name', '').strip()
        nft_holders = row.get('How many wallets are holding this NFT?', '').strip()

        if nft_address:
            holders_count = None
            if nft_holders:
                try:
                    holders_count = int(nft_holders.replace(',', ''))
                except ValueError:
                    pass

            project.nfts.append(NFTInfo(
                contract_address=nft_address,
                blockchain_scanner_link=nft_scanner,
                marketplace_link=nft_marketplace,
                function=nft_function,
                name=nft_name,
                holders_count=holders_count
            ))

    def _parse_metrics(self, row: Dict[str, str], project: GamingProjectConfig):
        """Parse metrics information from CSV row"""
        # Use correct CSV column names
        dau_str = row.get('On the average, how many daily active users (DAU) in the past month?', '').strip()
        uaw_str = row.get('On the average, how many daily unique active wallets (UAW) in the past month?', '').strip()
        dau_source = row.get('Link to Source of DAU count', '').strip()
        uaw_source = row.get('Link to Source of UAW count', '').strip()

        # Parse DAU
        if dau_str:
            try:
                # Remove commas and convert to int
                project.daily_active_users = int(dau_str.replace(',', ''))
            except ValueError:
                pass

        # Parse UAW
        if uaw_str:
            try:
                # Remove commas and convert to int
                project.daily_unique_wallets = int(uaw_str.replace(',', ''))
            except ValueError:
                pass

        project.dau_source = dau_source
        project.uaw_source = uaw_source

    def _parse_auto_clicker_info(self, row: Dict[str, str], project: GamingProjectConfig):
        """Parse auto-clicker information from CSV row"""
        project.auto_clicker_bot = row.get('Auto-Clicker Bot', '').strip()
        project.auto_clicker_community = row.get('Auto-Clicker Community', '').strip()

        membership_str = row.get('Auto-Clicker Membership', '').strip()
        if membership_str:
            try:
                project.auto_clicker_membership = int(membership_str.replace(',', ''))
            except ValueError:
                pass

    def _register_project(self, project: GamingProjectConfig):
        """Register a project and update indices"""
        project_key = project.project_name.lower().replace(' ', '-')
        self.projects[project_key] = project

        # Update blockchain index
        for blockchain in project.get_blockchain_list():
            if blockchain not in self.projects_by_blockchain:
                self.projects_by_blockchain[blockchain] = []
            self.projects_by_blockchain[blockchain].append(project_key)

        # Update status index
        status = project.status.lower()
        if status not in self.projects_by_status:
            self.projects_by_status[status] = []
        self.projects_by_status[status].append(project_key)

        # Update genre index
        genre = project.genre.lower()
        if genre and genre not in self.projects_by_genre:
            self.projects_by_genre[genre] = []
        if genre:
            self.projects_by_genre[genre].append(project_key)

    def get_project(self, project_name: str) -> Optional[GamingProjectConfig]:
        """Get a specific project by name"""
        project_key = project_name.lower().replace(' ', '-')
        return self.projects.get(project_key)

    def get_all_projects(self) -> Dict[str, GamingProjectConfig]:
        """Get all projects"""
        return self.projects.copy()

    def get_enabled_projects(self) -> Dict[str, GamingProjectConfig]:
        """Get only enabled projects"""
        return {k: v for k, v in self.projects.items() if v.enabled}

    def get_live_projects(self) -> Dict[str, GamingProjectConfig]:
        """Get only live/active projects"""
        return {k: v for k, v in self.projects.items() if v.is_live() and v.enabled}

    def get_projects_by_blockchain(self, blockchain: str) -> List[GamingProjectConfig]:
        """Get projects by blockchain"""
        blockchain = blockchain.lower()
        project_keys = self.projects_by_blockchain.get(blockchain, [])
        return [self.projects[key] for key in project_keys if key in self.projects]

    def get_projects_with_tokens(self) -> Dict[str, GamingProjectConfig]:
        """Get projects that have tokens"""
        return {k: v for k, v in self.projects.items() if v.has_tokens() and v.enabled}

    def get_projects_with_nfts(self) -> Dict[str, GamingProjectConfig]:
        """Get projects that have NFTs"""
        return {k: v for k, v in self.projects.items() if v.has_nfts() and v.enabled}

    def get_all_token_symbols(self) -> List[str]:
        """Get all unique token symbols"""
        symbols = set()
        for project in self.projects.values():
            if project.enabled:
                for token in project.tokens:
                    symbols.add(token.symbol)
        return list(symbols)

    def get_all_contract_addresses(self) -> List[str]:
        """Get all unique contract addresses"""
        addresses = set()
        for project in self.projects.values():
            if project.enabled:
                for token in project.tokens:
                    if token.contract_address:
                        addresses.add(token.contract_address)
                for nft in project.nfts:
                    if nft.contract_address:
                        addresses.add(nft.contract_address)
        return list(addresses)

    def get_gaming_keywords(self) -> List[str]:
        """Generate gaming keywords from project data"""
        keywords = set()
        for project in self.projects.values():
            if project.enabled:
                # Add project name
                keywords.add(project.project_name.lower())
                # Add token symbols
                for token in project.tokens:
                    keywords.add(token.symbol.lower())
                # Add genre and sub-genre
                if project.genre:
                    keywords.add(project.genre.lower())
                if project.sub_genre:
                    keywords.add(project.sub_genre.lower())

        # Add common gaming keywords
        keywords.update([
            'play-to-earn', 'p2e', 'nft gaming', 'gamefi', 'metaverse',
            'blockchain gaming', 'crypto gaming', 'gaming tokens', 'gaming nfts',
            'virtual worlds', 'web3 gaming', 'defi gaming'
        ])

        return list(keywords)

    def enable_project(self, project_name: str):
        """Enable a project"""
        project = self.get_project(project_name)
        if project:
            project.enabled = True
            logger.info(f"✅ Enabled project: {project_name}")

    def disable_project(self, project_name: str):
        """Disable a project"""
        project = self.get_project(project_name)
        if project:
            project.enabled = False
            logger.info(f"❌ Disabled project: {project_name}")

    def get_project_summary(self) -> Dict[str, Any]:
        """Get a summary of all projects"""
        total_projects = len(self.projects)
        enabled_projects = len(self.get_enabled_projects())
        live_projects = len(self.get_live_projects())
        projects_with_tokens = len(self.get_projects_with_tokens())
        projects_with_nfts = len(self.get_projects_with_nfts())

        return {
            'total_projects': total_projects,
            'enabled_projects': enabled_projects,
            'live_projects': live_projects,
            'projects_with_tokens': projects_with_tokens,
            'projects_with_nfts': projects_with_nfts,
            'blockchains': list(self.projects_by_blockchain.keys()),
            'genres': list(self.projects_by_genre.keys()),
            'total_tokens': len(self.get_all_token_symbols()),
            'total_contracts': len(self.get_all_contract_addresses())
        }


# Global instance
gaming_project_manager = GamingProjectManager()
