import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import get_db_session
from models.gaming import GamingProtocolMetricsHistory
from sqlalchemy import select, func, desc
from services.historical_data_collector import historical_data_collector

async def test_historical_collection():
    print('🔍 Testing historical data collection...')

    # First, manually trigger collection
    print('📊 Triggering manual collection...')
    try:
        await historical_data_collector.collect_current_metrics()
        print('✅ Manual collection completed')
    except Exception as e:
        print(f'❌ Manual collection failed: {e}')
        import traceback
        traceback.print_exc()
        return

    # Now check what's in the database
    print('🔍 Checking historical data in database...')
    try:
        async with get_db_session() as session:
            # Count total records
            result = await session.execute(
                select(func.count(GamingProtocolMetricsHistory.id))
            )
            total_records = result.scalar()
            print(f'📊 Total historical records: {total_records}')

            if total_records > 0:
                # Get latest 5 records
                result = await session.execute(
                    select(GamingProtocolMetricsHistory)
                    .order_by(desc(GamingProtocolMetricsHistory.collection_timestamp))
                    .limit(5)
                )

                latest_records = result.scalars().all()
                print(f'📈 Latest 5 records:')
                for record in latest_records:
                    price_str = f"${record.token_price}" if record.token_price else "N/A"
                    print(f'   - {record.protocol_name}: {price_str} @ {record.collection_timestamp}')

                # Check unique protocols
                result = await session.execute(
                    select(func.count(func.distinct(GamingProtocolMetricsHistory.protocol_name)))
                )
                unique_protocols = result.scalar()
                print(f'📊 Unique protocols in history: {unique_protocols}')

            else:
                print('📭 No historical records found')

    except Exception as e:
        print(f'❌ Error checking database: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_historical_collection())
