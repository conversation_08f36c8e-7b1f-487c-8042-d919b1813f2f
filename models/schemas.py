"""
Pydantic schemas for data validation and API serialization
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator, ConfigDict
from enum import Enum


class GamingCategory(str, Enum):
    """Gaming categories enum"""
    P2E = "P2E"  # Play-to-Earn
    NFT = "NFT"  # NFT Gaming
    DEFI = "DeFi"  # DeFi Gaming
    METAVERSE = "Metaverse"
    INFRASTRUCTURE = "Infrastructure"
    ESPORTS = "Esports"


class SourceType(str, Enum):
    """Source types enum"""
    RSS = "RSS"
    SCRAPER = "Scraper"
    API = "API"
    SOCIAL = "Social"


class BlockchainNetwork(str, Enum):
    """Supported blockchain networks"""
    ETHEREUM = "ethereum"
    POLYGON = "polygon"
    BSC = "bsc"
    ARBITRUM = "arbitrum"
    OPTIMISM = "optimism"
    AVALANCHE = "avalanche"
    FANTOM = "fantom"
    RONIN = "ronin"
    IMMUTABLE_X = "immutable_x"


# Base schemas
class BaseSchema(BaseModel):
    """Base schema with common configuration"""
    model_config = ConfigDict(from_attributes=True)


class TimestampSchema(BaseSchema):
    """Schema with timestamp fields"""
    created_at: datetime
    updated_at: datetime


# Source schemas
class SourceBase(BaseSchema):
    """Base source schema"""
    name: str = Field(..., min_length=1, max_length=200)
    slug: str = Field(..., min_length=1, max_length=200)
    url: str = Field(..., min_length=1, max_length=500)
    source_type: SourceType
    category: str = Field(..., max_length=50)
    is_active: bool = True
    scrape_frequency: int = Field(default=3600, ge=300)  # Minimum 5 minutes
    reliability_score: float = Field(default=0.5, ge=0.0, le=1.0)
    config: Optional[Dict[str, Any]] = None


class SourceCreate(SourceBase):
    """Schema for creating a source"""
    pass


class SourceUpdate(BaseSchema):
    """Schema for updating a source"""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    url: Optional[str] = Field(None, min_length=1, max_length=500)
    is_active: Optional[bool] = None
    scrape_frequency: Optional[int] = Field(None, ge=300)
    reliability_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    config: Optional[Dict[str, Any]] = None


class Source(SourceBase, TimestampSchema):
    """Complete source schema"""
    id: int
    article_count: int = 0
    last_scraped_at: Optional[datetime] = None


# Article schemas
class ArticleBase(BaseSchema):
    """Base article schema"""
    title: str = Field(..., min_length=1, max_length=500)
    content: Optional[str] = None
    summary: Optional[str] = Field(None, max_length=1000)
    url: str = Field(..., min_length=1, max_length=500)
    author: Optional[str] = Field(None, max_length=200)
    published_at: Optional[datetime] = None
    source_url: Optional[str] = Field(None, max_length=500)
    
    # Gaming categorization
    gaming_category: Optional[GamingCategory] = None
    gaming_subcategory: Optional[str] = Field(None, max_length=100)
    gaming_projects: Optional[List[str]] = None
    gaming_tokens: Optional[List[str]] = None
    
    # Analysis scores
    sentiment_score: Optional[float] = Field(None, ge=-1.0, le=1.0)
    relevance_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    
    # Metadata
    keywords: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    extra_metadata: Optional[Dict[str, Any]] = None


class ArticleCreate(ArticleBase):
    """Schema for creating an article"""
    source_id: int


class ArticleUpdate(BaseSchema):
    """Schema for updating an article"""
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    content: Optional[str] = None
    summary: Optional[str] = Field(None, max_length=1000)
    gaming_category: Optional[GamingCategory] = None
    gaming_subcategory: Optional[str] = Field(None, max_length=100)
    sentiment_score: Optional[float] = Field(None, ge=-1.0, le=1.0)
    relevance_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    is_processed: Optional[bool] = None


class Article(ArticleBase, TimestampSchema):
    """Complete article schema"""
    id: int
    source_id: int
    views: int = 0
    likes: int = 0
    shares: int = 0
    comments: int = 0
    is_processed: bool = False
    is_duplicate: bool = False
    duplicate_of_id: Optional[int] = None


# Gaming Project schemas
class GamingProjectBase(BaseSchema):
    """Base gaming project schema"""
    name: str = Field(..., min_length=1, max_length=200)
    slug: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    website: Optional[str] = Field(None, max_length=500)
    
    # Project categorization
    category: GamingCategory
    subcategory: Optional[str] = Field(None, max_length=100)
    blockchain: Optional[BlockchainNetwork] = None
    
    # Contract information
    contract_addresses: Optional[Dict[str, str]] = None
    token_symbol: Optional[str] = Field(None, max_length=20)
    token_address: Optional[str] = Field(None, max_length=100)
    
    # Status
    is_active: bool = True
    launch_date: Optional[datetime] = None
    extra_metadata: Optional[Dict[str, Any]] = None


class GamingProjectCreate(GamingProjectBase):
    """Schema for creating a gaming project"""
    pass


class GamingProjectUpdate(BaseSchema):
    """Schema for updating a gaming project"""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    website: Optional[str] = Field(None, max_length=500)
    category: Optional[GamingCategory] = None
    subcategory: Optional[str] = Field(None, max_length=100)
    blockchain: Optional[BlockchainNetwork] = None
    is_active: Optional[bool] = None
    market_cap: Optional[float] = Field(None, ge=0)
    token_price: Optional[float] = Field(None, ge=0)
    daily_active_users: Optional[int] = Field(None, ge=0)
    total_value_locked: Optional[float] = Field(None, ge=0)


class GamingProject(GamingProjectBase, TimestampSchema):
    """Complete gaming project schema"""
    id: int
    
    # Project metrics
    market_cap: Optional[float] = None
    token_price: Optional[float] = None
    daily_active_users: Optional[int] = None
    total_value_locked: Optional[float] = None
    
    # Social metrics
    twitter_followers: Optional[int] = None
    discord_members: Optional[int] = None
    telegram_members: Optional[int] = None


# Blockchain Data schemas
class BlockchainDataBase(BaseSchema):
    """Base blockchain data schema"""
    blockchain: BlockchainNetwork
    block_number: Optional[int] = None
    transaction_hash: Optional[str] = Field(None, max_length=100)
    contract_address: Optional[str] = Field(None, max_length=100)
    
    # Event data
    event_type: Optional[str] = Field(None, max_length=100)
    event_data: Optional[Dict[str, Any]] = None
    
    # Token information
    token_symbol: Optional[str] = Field(None, max_length=20)
    token_address: Optional[str] = Field(None, max_length=100)
    token_amount: Optional[float] = None
    token_price_usd: Optional[float] = None
    
    # NFT information
    nft_collection: Optional[str] = Field(None, max_length=200)
    nft_token_id: Optional[str] = Field(None, max_length=100)
    nft_metadata: Optional[Dict[str, Any]] = None
    
    # Transaction details
    from_address: Optional[str] = Field(None, max_length=100)
    to_address: Optional[str] = Field(None, max_length=100)
    gas_used: Optional[int] = None
    gas_price: Optional[float] = None
    
    # Timestamps
    block_timestamp: Optional[datetime] = None


class BlockchainDataCreate(BlockchainDataBase):
    """Schema for creating blockchain data"""
    article_id: Optional[int] = None
    gaming_project_id: Optional[int] = None


class BlockchainData(BlockchainDataBase, TimestampSchema):
    """Complete blockchain data schema"""
    id: int
    article_id: Optional[int] = None
    gaming_project_id: Optional[int] = None


# NFT Collection schemas
class NFTCollectionBase(BaseSchema):
    """Base NFT collection schema"""
    name: str = Field(..., min_length=1, max_length=200)
    slug: str = Field(..., min_length=1, max_length=200)
    contract_address: str = Field(..., min_length=1, max_length=100)
    blockchain: BlockchainNetwork
    
    # Collection info
    description: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=500)
    website: Optional[str] = Field(None, max_length=500)
    
    # Gaming categorization
    gaming_category: Optional[str] = Field(None, max_length=50)
    gaming_project_id: Optional[int] = None
    
    # Status
    is_verified: bool = False
    is_active: bool = True
    extra_metadata: Optional[Dict[str, Any]] = None


class NFTCollectionCreate(NFTCollectionBase):
    """Schema for creating an NFT collection"""
    pass


class NFTCollectionUpdate(BaseSchema):
    """Schema for updating an NFT collection"""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    gaming_category: Optional[str] = Field(None, max_length=50)
    is_verified: Optional[bool] = None
    is_active: Optional[bool] = None
    floor_price: Optional[float] = Field(None, ge=0)
    total_supply: Optional[int] = Field(None, ge=0)


class NFTCollection(NFTCollectionBase, TimestampSchema):
    """Complete NFT collection schema"""
    id: int
    
    # Collection metrics
    total_supply: Optional[int] = None
    floor_price: Optional[float] = None
    floor_price_usd: Optional[float] = None
    volume_24h: Optional[float] = None
    volume_total: Optional[float] = None
    owners_count: Optional[int] = None
    
    # Social metrics
    discord_members: Optional[int] = None
    twitter_followers: Optional[int] = None


# API Response schemas
class PaginatedResponse(BaseSchema):
    """Paginated response schema"""
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int


class APIResponse(BaseSchema):
    """Standard API response schema"""
    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None
