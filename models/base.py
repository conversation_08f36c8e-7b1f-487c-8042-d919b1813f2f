"""
Base database models and configuration with performance optimizations
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import <PERSON>umn, Integer, DateTime, String, Text, Boolean, Float, JSON, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool
import logging
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

# Enhanced database engine configuration for production performance
def create_optimized_engine():
    """Create optimized database engine with performance tuning"""

    if settings.database.url.startswith('sqlite'):
        # SQLite configuration with performance optimizations
        engine = create_engine(
            settings.database.url,
            echo=settings.api.debug,
            connect_args={
                "check_same_thread": False,
                "timeout": 30,
                "isolation_level": None  # Enable autocommit mode for better performance
            }
        )

        # SQLite-specific optimizations
        @event.listens_for(engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            cursor = dbapi_connection.cursor()
            # Performance optimizations for SQLite
            cursor.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
            cursor.execute("PRAGMA synchronous=NORMAL")  # Faster writes
            cursor.execute("PRAGMA cache_size=10000")  # Larger cache
            cursor.execute("PRAGMA temp_store=MEMORY")  # Store temp tables in memory
            cursor.close()

    else:
        # PostgreSQL configuration with production-grade connection pooling
        engine = create_engine(
            settings.database.url,
            echo=settings.api.debug,
            # Connection pool settings optimized for gaming data workloads
            poolclass=QueuePool,
            pool_size=20,  # Increased for concurrent dashboard requests
            max_overflow=30,  # Higher overflow for peak loads
            pool_pre_ping=True,  # Verify connections before use
            pool_recycle=1800,  # Recycle connections every 30 minutes
            pool_timeout=30,  # Connection timeout
            # Performance optimizations
            connect_args={
                "connect_timeout": 10,
                "application_name": "web3_gaming_tracker",
                "options": "-c default_transaction_isolation=read_committed"
            }
        )

        # PostgreSQL-specific optimizations
        @event.listens_for(engine, "connect")
        def set_postgresql_search_path(dbapi_connection, connection_record):
            with dbapi_connection.cursor() as cursor:
                # Set search path and performance optimizations
                cursor.execute("SET search_path TO public")
                cursor.execute("SET statement_timeout = '300s'")  # 5 minute query timeout
                cursor.execute("SET lock_timeout = '30s'")  # 30 second lock timeout
                cursor.execute("SET idle_in_transaction_session_timeout = '600s'")  # 10 minute idle timeout

    return engine

# Create optimized engine
engine = create_optimized_engine()

# Create optimized session factory
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    # Performance optimizations
    expire_on_commit=False  # Keep objects accessible after commit
)

# Create base class for models
Base = declarative_base()


class TimestampMixin:
    """Mixin for timestamp fields with optimized defaults"""
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, index=True)


class BaseModel(Base, TimestampMixin):
    """Base model with common fields and performance optimizations"""
    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True)


def get_db():
    """Get database session with connection management"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def get_db_sync():
    """Get synchronous database session for non-async operations"""
    return SessionLocal()


class DatabaseManager:
    """Database management utilities for performance monitoring and optimization"""

    def __init__(self):
        self.engine = engine

    def get_connection_pool_status(self) -> dict:
        """Get current connection pool status"""
        pool = self.engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }

    def get_slow_queries(self, threshold_ms: int = 1000) -> list:
        """Get slow queries from PostgreSQL (if available)"""
        if not settings.database.url.startswith('postgresql'):
            return []

        try:
            with self.engine.connect() as conn:
                result = conn.execute("""
                    SELECT query, mean_time, calls, total_time
                    FROM pg_stat_statements
                    WHERE mean_time > %s
                    ORDER BY mean_time DESC
                    LIMIT 10
                """, (threshold_ms,))
                return [dict(row) for row in result]
        except Exception as e:
            logger.warning(f"Could not fetch slow queries: {e}")
            return []

    def analyze_table_stats(self, table_name: str) -> dict:
        """Get table statistics for performance analysis"""
        if not settings.database.url.startswith('postgresql'):
            return {}

        try:
            with self.engine.connect() as conn:
                result = conn.execute("""
                    SELECT
                        schemaname,
                        tablename,
                        attname,
                        n_distinct,
                        correlation
                    FROM pg_stats
                    WHERE tablename = %s
                """, (table_name,))
                return [dict(row) for row in result]
        except Exception as e:
            logger.warning(f"Could not fetch table stats for {table_name}: {e}")
            return {}

# Global database manager instance
db_manager = DatabaseManager()


def create_tables():
    """Create all tables"""
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """Drop all tables"""
    Base.metadata.drop_all(bind=engine)
