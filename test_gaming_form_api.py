#!/usr/bin/env python3
"""
Test script for Gaming Form API endpoints
Tests the backend integration functionality
"""
import requests
import json
import sys
from datetime import datetime

# API base URL - adjust if running on different host/port
BASE_URL = "http://localhost:8000/api/v1/gaming-form"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_csv_stats():
    """Test the CSV stats endpoint"""
    print("\n🔍 Testing CSV stats endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/csv-stats")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ CSV stats test failed: {e}")
        return False

def test_csv_headers():
    """Test the CSV headers endpoint"""
    print("\n🔍 Testing CSV headers endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/csv-headers")
        print(f"Status Code: {response.status_code}")
        data = response.json()
        print(f"Total columns: {data.get('total_columns', 'N/A')}")
        print(f"First 5 headers: {data.get('headers', [])[:5]}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ CSV headers test failed: {e}")
        return False

def test_project_validation():
    """Test project name validation"""
    print("\n🔍 Testing project name validation...")
    try:
        # Test with existing project (should exist)
        response = requests.get(f"{BASE_URL}/validate-project/testgame")
        print(f"Validation for 'testgame' - Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        # Test with new project name (should not exist)
        test_name = f"TestProject_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        response = requests.get(f"{BASE_URL}/validate-project/{test_name}")
        print(f"\nValidation for '{test_name}' - Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Project validation test failed: {e}")
        return False

def test_form_submission():
    """Test form submission with sample data"""
    print("\n🔍 Testing form submission...")
    
    # Create test data
    test_data = {
        "project_name": f"TestProject_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "project_website_link": "https://testproject.example.com",
        "blockchain": "Ethereum",
        "validated_game_status": "Live",
        "genre": "Strategy",
        "game_style": "Free to Play, Play to Earn",
        "twitter_link": "https://twitter.com/testproject",
        "discord_link": "https://discord.gg/testproject",
        "involves_nfts": "Yes",
        "daily_active_users": "1000",
        "developer": "Test Studio",
        "platform": "PC, Mobile",
        "notes_and_comments": "This is a test submission from the API test script"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/submit",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Form submission successful!")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            return True
        else:
            print(f"❌ Form submission failed")
            try:
                print(f"Error response: {json.dumps(response.json(), indent=2)}")
            except:
                print(f"Raw response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Form submission test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Gaming Form API Tests")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_check),
        ("CSV Stats", test_csv_stats),
        ("CSV Headers", test_csv_headers),
        ("Project Validation", test_project_validation),
        ("Form Submission", test_form_submission)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 TEST SUMMARY")
    print(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Backend integration is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
