#!/usr/bin/env python3
"""
Test database connection and verify our fixes
"""
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database_connection():
    """Test basic database connectivity"""
    try:
        from sqlalchemy import create_engine, text
        from config.settings import get_settings
        
        settings = get_settings()
        engine = create_engine(settings.database.url)
        
        print("🔌 Testing database connection...")
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            if row and row[0] == 1:
                print("✅ Database connection successful")
                return True
            else:
                print("❌ Database connection failed")
                return False
                
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def test_model_imports():
    """Test that our models can be imported"""
    try:
        print("📦 Testing model imports...")
        
        # Test BlockchainData model
        from models.gaming import BlockchainData
        print("✅ BlockchainData model imported")
        
        # Test gaming project models
        from models.gaming import GamingProject
        print("✅ GamingProject model imported")
        
        # Test analytics models
        from services.user_activity_tracker import UserActivityMetrics
        print("✅ UserActivityMetrics imported")
        
        from services.p2e_economics_tracker import EarningMechanics
        print("✅ EarningMechanics imported")
        
        from services.nft_floor_tracker import NFTFloorData
        print("✅ NFTFloorData imported")
        
        from services.tvl_tracker import TVLData
        print("✅ TVLData imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Model import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gaming_config():
    """Test gaming configuration"""
    try:
        print("🎮 Testing gaming configuration...")
        
        from config.gaming_config import gaming_project_manager
        
        projects = gaming_project_manager.get_enabled_projects()
        print(f"✅ Gaming projects loaded: {len(projects)} projects")
        
        for project_name, project_config in list(projects.items())[:3]:  # Show first 3
            print(f"   - {project_config.project_name} ({project_config.blockchain})")
        
        return True
        
    except Exception as e:
        print(f"❌ Gaming config error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_schema():
    """Test database schema"""
    try:
        print("🗄️ Testing database schema...")
        
        from sqlalchemy import create_engine, text
        from config.settings import get_settings
        
        settings = get_settings()
        engine = create_engine(settings.database.url)
        
        with engine.connect() as connection:
            # Check if blockchain_data table exists
            result = connection.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'blockchain_data'
                ORDER BY ordinal_position
            """))
            
            columns = result.fetchall()
            if columns:
                print("✅ blockchain_data table exists with columns:")
                for col_name, col_type in columns:
                    print(f"   - {col_name}: {col_type}")
                return True
            else:
                print("❌ blockchain_data table not found")
                return False
                
    except Exception as e:
        print(f"❌ Database schema error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Running Database and Fix Tests...\n")
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Model Imports", test_model_imports),
        ("Gaming Configuration", test_gaming_config),
        ("Database Schema", test_database_schema),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        result = test_func()
        results.append((test_name, result))
    
    print(f"\n🎯 Test Results Summary:")
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
    
    all_passed = all(result for _, result in results)
    print(f"\n{'🎉 All tests passed!' if all_passed else '⚠️ Some tests failed'}")
