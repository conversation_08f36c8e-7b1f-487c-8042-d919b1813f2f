"""
Blockchain Scraper API Endpoints
Provides REST API access to blockchain scraping functionality
"""
from fastapi import APIRouter, HTTPException, Query, BackgroundTasks, Depends
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from pydantic import BaseModel

from scrapers.blockchain import (
    blockchain_scraper_manager,
    ScrapingConfig,
    ScrapingMode,
    gaming_contract_detector,
    gaming_ml_classifier,
    blockchain_event_scraper,
    real_time_monitor
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/blockchain-scraper", tags=["blockchain-scraper"])


# Pydantic models for API
class ScrapingConfigModel(BaseModel):
    chains: List[str]
    mode: str  # "historical", "real_time", "hybrid"
    enable_ml_classification: bool = True
    enable_real_time_monitoring: bool = True
    historical_blocks_back: int = 1000
    contract_scan_interval: int = 600


class ContractAnalysisRequest(BaseModel):
    contract_address: str
    blockchain: str


class ContractAnalysisResponse(BaseModel):
    contract_address: str
    blockchain: str
    heuristic_analysis: Dict[str, Any]
    ml_analysis: Optional[Dict[str, Any]]
    combined_result: bool
    analysis_timestamp: str


class ScrapingStatsResponse(BaseModel):
    contracts_analyzed: int
    gaming_contracts_found: int
    events_processed: int
    errors_encountered: int
    last_update: Optional[str]
    chains_active: List[str]
    is_running: bool


class GameEventResponse(BaseModel):
    event_id: str
    blockchain: str
    block_number: int
    transaction_hash: str
    contract_address: str
    event_type: str
    event_name: str
    event_data: Dict[str, Any]
    timestamp: str


@router.post("/start")
async def start_scraping(
    background_tasks: BackgroundTasks,
    config: Optional[ScrapingConfigModel] = None
):
    """Start blockchain scraping with optional configuration"""
    try:
        if blockchain_scraper_manager.is_running:
            raise HTTPException(status_code=400, detail="Scraping is already running")
        
        # Update configuration if provided
        if config:
            scraping_mode = ScrapingMode(config.mode)
            new_config = ScrapingConfig(
                chains=config.chains,
                mode=scraping_mode,
                enable_ml_classification=config.enable_ml_classification,
                enable_real_time_monitoring=config.enable_real_time_monitoring,
                historical_blocks_back=config.historical_blocks_back,
                contract_scan_interval=config.contract_scan_interval
            )
            blockchain_scraper_manager.config = new_config
        
        # Start scraping in background
        background_tasks.add_task(blockchain_scraper_manager.start_scraping)
        
        return {
            "status": "started",
            "message": "Blockchain scraping started in background",
            "config": {
                "chains": blockchain_scraper_manager.config.chains,
                "mode": blockchain_scraper_manager.config.mode.value,
                "enable_ml_classification": blockchain_scraper_manager.config.enable_ml_classification,
                "enable_real_time_monitoring": blockchain_scraper_manager.config.enable_real_time_monitoring
            }
        }
        
    except Exception as e:
        logger.error(f"Error starting blockchain scraping: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop")
async def stop_scraping():
    """Stop blockchain scraping"""
    try:
        if not blockchain_scraper_manager.is_running:
            raise HTTPException(status_code=400, detail="Scraping is not running")
        
        await blockchain_scraper_manager.stop_scraping()
        
        return {
            "status": "stopped",
            "message": "Blockchain scraping stopped successfully"
        }
        
    except Exception as e:
        logger.error(f"Error stopping blockchain scraping: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=ScrapingStatsResponse)
async def get_scraping_status():
    """Get current scraping status and statistics"""
    try:
        stats = await blockchain_scraper_manager.get_stats()
        
        return ScrapingStatsResponse(
            contracts_analyzed=stats.contracts_analyzed,
            gaming_contracts_found=stats.gaming_contracts_found,
            events_processed=stats.events_processed,
            errors_encountered=stats.errors_encountered,
            last_update=stats.last_update.isoformat() if stats.last_update else None,
            chains_active=stats.chains_active or [],
            is_running=blockchain_scraper_manager.is_running
        )
        
    except Exception as e:
        logger.error(f"Error getting scraping status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze-contract", response_model=ContractAnalysisResponse)
async def analyze_contract(request: ContractAnalysisRequest):
    """Analyze a specific contract to determine if it's gaming-related"""
    try:
        result = await blockchain_scraper_manager.analyze_specific_contract(
            request.contract_address, 
            request.blockchain
        )
        
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return ContractAnalysisResponse(
            contract_address=result['contract_address'],
            blockchain=result['blockchain'],
            heuristic_analysis=result['heuristic_analysis'],
            ml_analysis=result['ml_analysis'],
            combined_result=result['combined_result'],
            analysis_timestamp=result['analysis_timestamp']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing contract: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/gaming-contracts")
async def get_gaming_contracts(
    blockchain: Optional[str] = Query(None, description="Filter by blockchain"),
    limit: int = Query(100, description="Maximum number of contracts to return"),
    offset: int = Query(0, description="Number of contracts to skip")
):
    """Get list of discovered gaming contracts"""
    try:
        # This would query the database for stored gaming contracts
        # For now, return the contracts currently being monitored
        monitored_contracts = list(blockchain_scraper_manager.discovered_contracts)
        
        # Apply filters
        if blockchain:
            # Filter would be applied here if we had blockchain info stored
            pass
        
        # Apply pagination
        start = offset
        end = offset + limit
        contracts = monitored_contracts[start:end]
        
        return {
            "contracts": [
                {
                    "contract_address": addr,
                    "blockchain": "unknown",  # Would be stored in database
                    "discovery_timestamp": "unknown",  # Would be stored in database
                    "confidence": "unknown"  # Would be stored in database
                }
                for addr in contracts
            ],
            "total": len(monitored_contracts),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Error getting gaming contracts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/events")
async def get_gaming_events(
    blockchain: Optional[str] = Query(None, description="Filter by blockchain"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    contract_address: Optional[str] = Query(None, description="Filter by contract address"),
    from_timestamp: Optional[datetime] = Query(None, description="Start timestamp"),
    to_timestamp: Optional[datetime] = Query(None, description="End timestamp"),
    limit: int = Query(100, description="Maximum number of events to return")
):
    """Get gaming events with optional filters"""
    try:
        # This would query the database for stored events
        # For now, return empty list as placeholder
        events = []
        
        return {
            "events": events,
            "total": len(events),
            "filters": {
                "blockchain": blockchain,
                "event_type": event_type,
                "contract_address": contract_address,
                "from_timestamp": from_timestamp.isoformat() if from_timestamp else None,
                "to_timestamp": to_timestamp.isoformat() if to_timestamp else None
            },
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"Error getting gaming events: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/add-monitored-contract")
async def add_monitored_contract(
    contract_address: str = Query(..., description="Contract address to monitor"),
    blockchain: str = Query(..., description="Blockchain network")
):
    """Add a contract to the monitoring list"""
    try:
        # Add to real-time monitoring
        real_time_monitor.add_monitored_contract(contract_address)
        blockchain_event_scraper.add_monitored_contract(contract_address)
        blockchain_scraper_manager.discovered_contracts.add(contract_address)
        
        return {
            "status": "success",
            "message": f"Contract {contract_address} added to monitoring on {blockchain}",
            "contract_address": contract_address,
            "blockchain": blockchain
        }
        
    except Exception as e:
        logger.error(f"Error adding monitored contract: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/remove-monitored-contract")
async def remove_monitored_contract(
    contract_address: str = Query(..., description="Contract address to remove"),
    blockchain: str = Query(..., description="Blockchain network")
):
    """Remove a contract from the monitoring list"""
    try:
        # Remove from monitoring
        real_time_monitor.remove_monitored_contract(contract_address)
        blockchain_event_scraper.remove_monitored_contract(contract_address)
        blockchain_scraper_manager.discovered_contracts.discard(contract_address)
        
        return {
            "status": "success",
            "message": f"Contract {contract_address} removed from monitoring on {blockchain}",
            "contract_address": contract_address,
            "blockchain": blockchain
        }
        
    except Exception as e:
        logger.error(f"Error removing monitored contract: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_scraping_config():
    """Get current scraping configuration"""
    try:
        config = blockchain_scraper_manager.config
        
        return {
            "chains": config.chains,
            "mode": config.mode.value,
            "enable_ml_classification": config.enable_ml_classification,
            "enable_real_time_monitoring": config.enable_real_time_monitoring,
            "historical_blocks_back": config.historical_blocks_back,
            "contract_scan_interval": config.contract_scan_interval,
            "event_batch_size": config.event_batch_size,
            "max_concurrent_requests": config.max_concurrent_requests
        }
        
    except Exception as e:
        logger.error(f"Error getting scraping config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/config")
async def update_scraping_config(config: ScrapingConfigModel):
    """Update scraping configuration"""
    try:
        if blockchain_scraper_manager.is_running:
            raise HTTPException(
                status_code=400, 
                detail="Cannot update configuration while scraping is running. Stop scraping first."
            )
        
        scraping_mode = ScrapingMode(config.mode)
        new_config = ScrapingConfig(
            chains=config.chains,
            mode=scraping_mode,
            enable_ml_classification=config.enable_ml_classification,
            enable_real_time_monitoring=config.enable_real_time_monitoring,
            historical_blocks_back=config.historical_blocks_back,
            contract_scan_interval=config.contract_scan_interval
        )
        
        blockchain_scraper_manager.config = new_config
        
        return {
            "status": "success",
            "message": "Scraping configuration updated successfully",
            "config": {
                "chains": new_config.chains,
                "mode": new_config.mode.value,
                "enable_ml_classification": new_config.enable_ml_classification,
                "enable_real_time_monitoring": new_config.enable_real_time_monitoring,
                "historical_blocks_back": new_config.historical_blocks_back,
                "contract_scan_interval": new_config.contract_scan_interval
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid configuration: {e}")
    except Exception as e:
        logger.error(f"Error updating scraping config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/scan-chain")
async def scan_chain_for_contracts(
    background_tasks: BackgroundTasks,
    blockchain: str = Query(..., description="Blockchain to scan"),
    from_block: Optional[int] = Query(None, description="Starting block number")
):
    """Manually trigger contract scanning for a specific chain"""
    try:
        # Run scanning in background
        background_tasks.add_task(
            blockchain_event_scraper.scan_new_contracts,
            blockchain,
            from_block
        )
        
        return {
            "status": "started",
            "message": f"Contract scanning started for {blockchain}",
            "blockchain": blockchain,
            "from_block": from_block
        }
        
    except Exception as e:
        logger.error(f"Error starting chain scan: {e}")
        raise HTTPException(status_code=500, detail=str(e))
