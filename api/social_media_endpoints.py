"""
Social Media API endpoints for Web3 Gaming News Tracker
Endpoints for Twitter and Reddit gaming content and filtering
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc, or_
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel

from models.base import get_db
from models.gaming import TwitterPost, RedditPost, SocialMediaFilter
from scrapers.social import twitter_scraper, reddit_scraper

router = APIRouter()


class SocialMediaSummary(BaseModel):
    """Social media summary data"""
    total_twitter_posts: int
    total_reddit_posts: int
    recent_twitter_posts: int
    recent_reddit_posts: int
    top_gaming_projects: List[str]
    top_gaming_influencers: List[str]
    engagement_metrics: Dict[str, Any]


class FilterRequest(BaseModel):
    """Request model for creating/updating filters"""
    filter_name: str
    filter_type: str
    keywords: Optional[List[str]] = []
    excluded_keywords: Optional[List[str]] = []
    gaming_projects: Optional[List[str]] = []
    gaming_influencers: Optional[List[str]] = []
    gaming_tokens: Optional[List[str]] = []
    min_engagement_score: Optional[int] = 0
    min_relevance_score: Optional[float] = 0.0
    min_sentiment_score: Optional[float] = -1.0
    max_age_hours: Optional[int] = 24
    twitter_settings: Optional[Dict[str, Any]] = {}
    reddit_settings: Optional[Dict[str, Any]] = {}
    description: Optional[str] = ""


@router.get("/social-media/summary", response_model=SocialMediaSummary)
async def get_social_media_summary(
    db: Session = Depends(get_db),
    hours: int = Query(24, description="Hours to look back for recent activity")
):
    """Get social media summary for dashboard"""
    try:
        # Get total counts
        total_twitter = db.query(TwitterPost).filter(TwitterPost.is_gaming_related == True).count()
        total_reddit = db.query(RedditPost).filter(RedditPost.is_gaming_related == True).count()
        
        # Get recent activity
        since = datetime.utcnow() - timedelta(hours=hours)
        recent_twitter = db.query(TwitterPost).filter(
            and_(
                TwitterPost.is_gaming_related == True,
                TwitterPost.created_at >= since
            )
        ).count()
        
        recent_reddit = db.query(RedditPost).filter(
            and_(
                RedditPost.is_gaming_related == True,
                RedditPost.created_utc >= since
            )
        ).count()
        
        # Get top gaming projects mentioned
        twitter_projects = db.query(TwitterPost.gaming_projects).filter(
            and_(
                TwitterPost.is_gaming_related == True,
                TwitterPost.created_at >= since,
                TwitterPost.gaming_projects.isnot(None)
            )
        ).all()
        
        reddit_projects = db.query(RedditPost.gaming_projects).filter(
            and_(
                RedditPost.is_gaming_related == True,
                RedditPost.created_utc >= since,
                RedditPost.gaming_projects.isnot(None)
            )
        ).all()
        
        # Flatten and count project mentions
        project_mentions = {}
        for row in twitter_projects + reddit_projects:
            if row[0]:  # gaming_projects is not None
                for project in row[0]:
                    project_mentions[project] = project_mentions.get(project, 0) + 1
        
        top_projects = sorted(project_mentions.items(), key=lambda x: x[1], reverse=True)[:10]
        top_projects = [project[0] for project in top_projects]
        
        # Get top gaming influencers
        twitter_influencers = db.query(TwitterPost.gaming_influencers).filter(
            and_(
                TwitterPost.is_gaming_related == True,
                TwitterPost.created_at >= since,
                TwitterPost.gaming_influencers.isnot(None)
            )
        ).all()
        
        influencer_mentions = {}
        for row in twitter_influencers:
            if row[0]:  # gaming_influencers is not None
                for influencer in row[0]:
                    influencer_mentions[influencer] = influencer_mentions.get(influencer, 0) + 1
        
        top_influencers = sorted(influencer_mentions.items(), key=lambda x: x[1], reverse=True)[:10]
        top_influencers = [influencer[0] for influencer in top_influencers]
        
        # Calculate engagement metrics
        twitter_engagement = db.query(
            func.avg(TwitterPost.like_count + TwitterPost.retweet_count).label('avg_engagement'),
            func.sum(TwitterPost.like_count + TwitterPost.retweet_count).label('total_engagement')
        ).filter(
            and_(
                TwitterPost.is_gaming_related == True,
                TwitterPost.created_at >= since
            )
        ).first()
        
        reddit_engagement = db.query(
            func.avg(RedditPost.score).label('avg_score'),
            func.sum(RedditPost.score).label('total_score')
        ).filter(
            and_(
                RedditPost.is_gaming_related == True,
                RedditPost.created_utc >= since
            )
        ).first()
        
        engagement_metrics = {
            "twitter": {
                "avg_engagement": float(twitter_engagement.avg_engagement or 0),
                "total_engagement": int(twitter_engagement.total_engagement or 0)
            },
            "reddit": {
                "avg_score": float(reddit_engagement.avg_score or 0),
                "total_score": int(reddit_engagement.total_score or 0)
            }
        }
        
        return SocialMediaSummary(
            total_twitter_posts=total_twitter,
            total_reddit_posts=total_reddit,
            recent_twitter_posts=recent_twitter,
            recent_reddit_posts=recent_reddit,
            top_gaming_projects=top_projects,
            top_gaming_influencers=top_influencers,
            engagement_metrics=engagement_metrics
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting social media summary: {str(e)}")


@router.get("/social-media/twitter/posts")
async def get_twitter_posts(
    db: Session = Depends(get_db),
    limit: int = Query(50, description="Number of posts to return"),
    hours: int = Query(24, description="Hours to look back"),
    min_engagement: int = Query(0, description="Minimum engagement score"),
    gaming_only: bool = Query(True, description="Only gaming-related posts")
):
    """Get recent Twitter posts"""
    try:
        since = datetime.utcnow() - timedelta(hours=hours)
        
        query = db.query(TwitterPost).filter(TwitterPost.created_at >= since)
        
        if gaming_only:
            query = query.filter(TwitterPost.is_gaming_related == True)
        
        if min_engagement > 0:
            query = query.filter(
                (TwitterPost.like_count + TwitterPost.retweet_count) >= min_engagement
            )
        
        posts = query.order_by(desc(TwitterPost.created_at)).limit(limit).all()
        
        return {
            "posts": [
                {
                    "id": post.twitter_id,
                    "text": post.text,
                    "author_username": post.author_username,
                    "author_name": post.author_name,
                    "created_at": post.created_at.isoformat(),
                    "url": post.url,
                    "engagement": {
                        "likes": post.like_count,
                        "retweets": post.retweet_count,
                        "replies": post.reply_count
                    },
                    "gaming_projects": post.gaming_projects or [],
                    "gaming_influencers": post.gaming_influencers or [],
                    "hashtags": post.hashtags or [],
                    "sentiment_score": post.sentiment_score,
                    "relevance_score": post.relevance_score
                }
                for post in posts
            ],
            "total_count": len(posts),
            "filters_applied": {
                "hours": hours,
                "min_engagement": min_engagement,
                "gaming_only": gaming_only
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting Twitter posts: {str(e)}")


@router.get("/social-media/reddit/posts")
async def get_reddit_posts(
    db: Session = Depends(get_db),
    limit: int = Query(50, description="Number of posts to return"),
    hours: int = Query(24, description="Hours to look back"),
    min_score: int = Query(5, description="Minimum upvote score"),
    gaming_only: bool = Query(True, description="Only gaming-related posts")
):
    """Get recent Reddit posts"""
    try:
        since = datetime.utcnow() - timedelta(hours=hours)
        
        query = db.query(RedditPost).filter(RedditPost.created_utc >= since)
        
        if gaming_only:
            query = query.filter(RedditPost.is_gaming_related == True)
        
        if min_score > 0:
            query = query.filter(RedditPost.score >= min_score)
        
        posts = query.order_by(desc(RedditPost.created_utc)).limit(limit).all()
        
        return {
            "posts": [
                {
                    "id": post.reddit_id,
                    "title": post.title,
                    "selftext": post.selftext,
                    "author": post.author,
                    "subreddit": post.subreddit,
                    "created_utc": post.created_utc.isoformat(),
                    "url": post.url,
                    "permalink": f"https://reddit.com{post.permalink}" if post.permalink else None,
                    "engagement": {
                        "score": post.score,
                        "upvote_ratio": post.upvote_ratio,
                        "num_comments": post.num_comments
                    },
                    "gaming_projects": post.gaming_projects or [],
                    "gaming_tokens": post.gaming_tokens or [],
                    "sentiment_score": post.sentiment_score,
                    "relevance_score": post.relevance_score,
                    "quality_score": post.quality_score
                }
                for post in posts
            ],
            "total_count": len(posts),
            "filters_applied": {
                "hours": hours,
                "min_score": min_score,
                "gaming_only": gaming_only
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting Reddit posts: {str(e)}")


@router.get("/social-media/filters")
async def get_social_media_filters(
    db: Session = Depends(get_db),
    filter_type: Optional[str] = Query(None, description="Filter by type: twitter, reddit, both")
):
    """Get all social media filters"""
    try:
        query = db.query(SocialMediaFilter)
        
        if filter_type:
            query = query.filter(SocialMediaFilter.filter_type == filter_type)
        
        filters = query.order_by(desc(SocialMediaFilter.created_at)).all()
        
        return {
            "filters": [
                {
                    "id": f.id,
                    "filter_name": f.filter_name,
                    "filter_type": f.filter_type,
                    "is_active": f.is_active,
                    "is_locked": f.is_locked,
                    "keywords": f.keywords or [],
                    "excluded_keywords": f.excluded_keywords or [],
                    "gaming_projects": f.gaming_projects or [],
                    "gaming_influencers": f.gaming_influencers or [],
                    "gaming_tokens": f.gaming_tokens or [],
                    "min_engagement_score": f.min_engagement_score,
                    "min_relevance_score": f.min_relevance_score,
                    "min_sentiment_score": f.min_sentiment_score,
                    "max_age_hours": f.max_age_hours,
                    "description": f.description,
                    "last_used_at": f.last_used_at.isoformat() if f.last_used_at else None,
                    "usage_count": f.usage_count,
                    "created_at": f.created_at.isoformat()
                }
                for f in filters
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting filters: {str(e)}")


@router.post("/social-media/filters")
async def create_social_media_filter(
    filter_data: FilterRequest,
    db: Session = Depends(get_db)
):
    """Create a new social media filter"""
    try:
        # Check if filter name already exists
        existing = db.query(SocialMediaFilter).filter(
            SocialMediaFilter.filter_name == filter_data.filter_name
        ).first()

        if existing:
            raise HTTPException(status_code=400, detail="Filter name already exists")

        new_filter = SocialMediaFilter(
            filter_name=filter_data.filter_name,
            filter_type=filter_data.filter_type,
            keywords=filter_data.keywords,
            excluded_keywords=filter_data.excluded_keywords,
            gaming_projects=filter_data.gaming_projects,
            gaming_influencers=filter_data.gaming_influencers,
            gaming_tokens=filter_data.gaming_tokens,
            min_engagement_score=filter_data.min_engagement_score,
            min_relevance_score=filter_data.min_relevance_score,
            min_sentiment_score=filter_data.min_sentiment_score,
            max_age_hours=filter_data.max_age_hours,
            twitter_settings=filter_data.twitter_settings,
            reddit_settings=filter_data.reddit_settings,
            description=filter_data.description
        )

        db.add(new_filter)
        db.commit()
        db.refresh(new_filter)

        return {
            "id": new_filter.id,
            "filter_name": new_filter.filter_name,
            "message": "Filter created successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating filter: {str(e)}")


@router.put("/social-media/filters/{filter_id}")
async def update_social_media_filter(
    filter_id: int,
    filter_data: FilterRequest,
    db: Session = Depends(get_db)
):
    """Update an existing social media filter"""
    try:
        filter_obj = db.query(SocialMediaFilter).filter(SocialMediaFilter.id == filter_id).first()

        if not filter_obj:
            raise HTTPException(status_code=404, detail="Filter not found")

        if filter_obj.is_locked:
            raise HTTPException(status_code=400, detail="Cannot modify locked filter")

        # Update fields
        filter_obj.filter_name = filter_data.filter_name
        filter_obj.filter_type = filter_data.filter_type
        filter_obj.keywords = filter_data.keywords
        filter_obj.excluded_keywords = filter_data.excluded_keywords
        filter_obj.gaming_projects = filter_data.gaming_projects
        filter_obj.gaming_influencers = filter_data.gaming_influencers
        filter_obj.gaming_tokens = filter_data.gaming_tokens
        filter_obj.min_engagement_score = filter_data.min_engagement_score
        filter_obj.min_relevance_score = filter_data.min_relevance_score
        filter_obj.min_sentiment_score = filter_data.min_sentiment_score
        filter_obj.max_age_hours = filter_data.max_age_hours
        filter_obj.twitter_settings = filter_data.twitter_settings
        filter_obj.reddit_settings = filter_data.reddit_settings
        filter_obj.description = filter_data.description
        filter_obj.updated_at = datetime.utcnow()

        db.commit()

        return {
            "id": filter_obj.id,
            "filter_name": filter_obj.filter_name,
            "message": "Filter updated successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating filter: {str(e)}")


@router.delete("/social-media/filters/{filter_id}")
async def delete_social_media_filter(
    filter_id: int,
    db: Session = Depends(get_db)
):
    """Delete a social media filter"""
    try:
        filter_obj = db.query(SocialMediaFilter).filter(SocialMediaFilter.id == filter_id).first()

        if not filter_obj:
            raise HTTPException(status_code=404, detail="Filter not found")

        if filter_obj.is_locked:
            raise HTTPException(status_code=400, detail="Cannot delete locked filter")

        db.delete(filter_obj)
        db.commit()

        return {"message": "Filter deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting filter: {str(e)}")


@router.post("/social-media/filters/{filter_id}/toggle")
async def toggle_filter_status(
    filter_id: int,
    db: Session = Depends(get_db)
):
    """Toggle filter active status"""
    try:
        filter_obj = db.query(SocialMediaFilter).filter(SocialMediaFilter.id == filter_id).first()

        if not filter_obj:
            raise HTTPException(status_code=404, detail="Filter not found")

        filter_obj.is_active = not filter_obj.is_active
        filter_obj.updated_at = datetime.utcnow()

        db.commit()

        return {
            "id": filter_obj.id,
            "is_active": filter_obj.is_active,
            "message": f"Filter {'activated' if filter_obj.is_active else 'deactivated'}"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error toggling filter: {str(e)}")


@router.post("/social-media/filters/{filter_id}/lock")
async def toggle_filter_lock(
    filter_id: int,
    db: Session = Depends(get_db)
):
    """Toggle filter lock status"""
    try:
        filter_obj = db.query(SocialMediaFilter).filter(SocialMediaFilter.id == filter_id).first()

        if not filter_obj:
            raise HTTPException(status_code=404, detail="Filter not found")

        filter_obj.is_locked = not filter_obj.is_locked
        filter_obj.updated_at = datetime.utcnow()

        db.commit()

        return {
            "id": filter_obj.id,
            "is_locked": filter_obj.is_locked,
            "message": f"Filter {'locked' if filter_obj.is_locked else 'unlocked'}"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error toggling filter lock: {str(e)}")


@router.post("/social-media/test-data")
async def create_test_social_media_data(
    db: Session = Depends(get_db),
    count: int = Query(10, description="Number of test posts to create per platform")
):
    """Create test social media data for testing data flow"""
    try:
        import random
        from datetime import datetime, timedelta

        # Sample gaming data
        gaming_projects = ["Axie Infinity", "The Sandbox", "Decentraland", "Gala Games", "Splinterlands"]
        gaming_influencers = ["jihoz_axie", "Filbertsteiner", "Amanda Zhu", "sinjinMAYG"]
        gaming_hashtags = ["#p2e", "#playtoearn", "#web3gaming", "#cryptogaming", "#blockchain"]
        gaming_tokens = ["AXS", "SAND", "MANA", "GALA", "SPS"]

        twitter_posts_created = 0
        reddit_posts_created = 0

        # Create test Twitter posts
        for i in range(count):
            twitter_id = f"test_twitter_{random.randint(100000, 999999)}_{i}"

            # Check if already exists
            existing = db.query(TwitterPost).filter(TwitterPost.twitter_id == twitter_id).first()
            if existing:
                continue

            project = random.choice(gaming_projects)
            influencer = random.choice(gaming_influencers)
            hashtag = random.choice(gaming_hashtags)
            token = random.choice(gaming_tokens)

            twitter_post = TwitterPost(
                twitter_id=twitter_id,
                text=f"Just played {project} and earned some {token}! The future of gaming is here {hashtag} #blockchain",
                author_username=f"gamer_{random.randint(1000, 9999)}",
                author_name=f"Gaming Enthusiast {i+1}",
                created_at=datetime.utcnow() - timedelta(hours=random.randint(1, 24)),
                url=f"https://twitter.com/test/status/{twitter_id}",
                like_count=random.randint(5, 500),
                retweet_count=random.randint(1, 100),
                reply_count=random.randint(0, 50),
                quote_count=random.randint(0, 20),
                hashtags=[hashtag, "#blockchain", "#gaming"],
                mentions=[f"@{influencer}"],
                gaming_projects=[project],
                gaming_influencers=[influencer],
                is_gaming_related=True,
                sentiment_score=random.uniform(0.3, 0.9),
                relevance_score=random.uniform(0.7, 1.0),
                collection_source='test_data'
            )
            db.add(twitter_post)
            twitter_posts_created += 1

        # Create test Reddit posts
        subreddits = ["CryptoCurrency", "ethereum", "NFTGames", "playtoearn", "GameFi"]

        for i in range(count):
            reddit_id = f"test_reddit_{random.randint(100000, 999999)}_{i}"

            # Check if already exists
            existing = db.query(RedditPost).filter(RedditPost.reddit_id == reddit_id).first()
            if existing:
                continue

            project = random.choice(gaming_projects)
            token = random.choice(gaming_tokens)
            subreddit = random.choice(subreddits)

            reddit_post = RedditPost(
                reddit_id=reddit_id,
                title=f"My experience with {project} - earning {token} while gaming",
                selftext=f"I've been playing {project} for a few weeks now and the play-to-earn mechanics are incredible. The {token} rewards make gaming actually profitable!",
                author=f"reddit_gamer_{random.randint(1000, 9999)}",
                subreddit=subreddit,
                created_utc=datetime.utcnow() - timedelta(hours=random.randint(1, 48)),
                url=f"https://reddit.com/r/{subreddit}/comments/{reddit_id}/",
                permalink=f"/r/{subreddit}/comments/{reddit_id}/",
                score=random.randint(10, 200),
                upvote_ratio=random.uniform(0.7, 0.95),
                num_comments=random.randint(5, 50),
                gaming_projects=[project],
                gaming_tokens=[token],
                is_gaming_related=True,
                meets_quality_threshold=True,
                sentiment_score=random.uniform(0.4, 0.9),
                relevance_score=random.uniform(0.8, 1.0),
                quality_score=random.uniform(0.6, 0.9)
            )
            db.add(reddit_post)
            reddit_posts_created += 1

        db.commit()

        return {
            "message": "Test social media data created successfully",
            "twitter_posts_created": twitter_posts_created,
            "reddit_posts_created": reddit_posts_created,
            "total_posts": twitter_posts_created + reddit_posts_created,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating test data: {str(e)}")


@router.post("/social-media/collect")
async def trigger_social_media_collection(
    platform: str = Query(..., description="Platform to collect from: twitter, reddit, or both"),
    force: bool = Query(False, description="Force collection bypassing time interval"),
    db: Session = Depends(get_db)
):
    """Manually trigger social media data collection"""
    try:
        results = {}

        if platform in ["twitter", "both"]:
            twitter_posts = await twitter_scraper.collect_gaming_posts(force=force)

            # Store Twitter posts in database
            for post_data in twitter_posts:
                existing = db.query(TwitterPost).filter(
                    TwitterPost.twitter_id == post_data.id
                ).first()

                if not existing:
                    twitter_post = TwitterPost(
                        twitter_id=post_data.id,
                        text=post_data.text,
                        author_username=post_data.author_username,
                        author_name=post_data.author_name,
                        created_at=post_data.created_at,
                        url=post_data.url,
                        like_count=post_data.public_metrics.get('like_count', 0),
                        retweet_count=post_data.public_metrics.get('retweet_count', 0),
                        reply_count=post_data.public_metrics.get('reply_count', 0),
                        quote_count=post_data.public_metrics.get('quote_count', 0),
                        hashtags=post_data.hashtags,
                        mentions=post_data.mentions,
                        is_gaming_related=post_data.is_gaming_related,
                        sentiment_score=post_data.sentiment_score,
                        collection_source='manual_trigger'
                    )
                    db.add(twitter_post)

            results['twitter'] = {
                'collected': len(twitter_posts),
                'message': f'Collected {len(twitter_posts)} Twitter posts'
            }

        if platform in ["reddit", "both"]:
            reddit_posts = await reddit_scraper.collect_gaming_posts(force=force)

            # Store Reddit posts in database
            for post_data in reddit_posts:
                existing = db.query(RedditPost).filter(
                    RedditPost.reddit_id == post_data.id
                ).first()

                if not existing:
                    reddit_post = RedditPost(
                        reddit_id=post_data.id,
                        title=post_data.title,
                        selftext=post_data.content,
                        author=post_data.author,
                        subreddit=post_data.subreddit,
                        created_utc=post_data.created_at,
                        url=post_data.url,
                        score=post_data.score,
                        upvote_ratio=post_data.upvote_ratio,
                        num_comments=post_data.num_comments,
                        is_gaming_related=post_data.is_gaming_related,
                        meets_quality_threshold=post_data.score >= 5
                    )
                    db.add(reddit_post)

            results['reddit'] = {
                'collected': len(reddit_posts),
                'message': f'Collected {len(reddit_posts)} Reddit posts'
            }

        db.commit()

        return {
            'platform': platform,
            'results': results,
            'timestamp': datetime.utcnow().isoformat()
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error collecting social media data: {str(e)}")
