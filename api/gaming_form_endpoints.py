"""
Gaming Form API Endpoints
Handles form submissions for manual gaming project additions
"""
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
import csv
import os
import logging
import asyncio
from pathlib import Path
import json

from models.base import get_db
from config.gaming_config import gaming_project_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/gaming-form", tags=["Gaming Form"])

# CSV file path
CSV_FILE_PATH = "GamingDBDraftResponses.csv"


class GamingProjectFormData(BaseModel):
    """Pydantic model for gaming project form data"""
    # Basic project information
    project_name: str = Field(..., description="Project Name")
    project_website_link: Optional[str] = Field(None, description="Project Website Link")
    whitepaper_link: Optional[str] = Field(None, description="Whitepaper Link")
    blockchain: Optional[str] = Field(None, description="Blockchain")
    validated_game_status: Optional[str] = Field(None, description="Validated Game Status")
    game_status_notes: Optional[str] = Field(None, description="Game Status Notes")
    token_schedule_link: Optional[str] = Field(None, description="Token Schedule Link")
    
    # Token information
    token_1_symbol: Optional[str] = Field(None, description="Token 1 - Symbol")
    token_1_contract_address: Optional[str] = Field(None, description="Token 1 - Token Contract Address")
    token_1_type: Optional[str] = Field(None, description="Token 1 - Type")
    token_1_coingecko_link: Optional[str] = Field(None, description="Token 1 Link to CoinGecko Listing")
    token_2_symbol: Optional[str] = Field(None, description="Token 2 - Symbol")
    token_2_contract_address: Optional[str] = Field(None, description="Token 2 - Token Contract Address")
    token_2_type: Optional[str] = Field(None, description="Token 2 - Type")
    token_2_coingecko_link: Optional[str] = Field(None, description="Token 2 Link to CoinGecko Listing")
    
    # Genre and sub-genre information
    genre: Optional[str] = Field(None, description="Which genre best fits this title? Choose only ONE")
    action_subgenre: Optional[str] = Field(None, description="Choose ONE Action sub-genre that best fits this game")
    action_adventure_subgenre: Optional[str] = Field(None, description="Choose ONE Action Adventure sub-genre that best fits this game")
    action_rpg_subgenre: Optional[str] = Field(None, description="Choose ONE Action RPG sub-genre that best fits this game")
    adventure_subgenre: Optional[str] = Field(None, description="Choose ONE Adventure sub-genre that best fits this game")
    battle_royale_subgenre: Optional[str] = Field(None, description="Choose ONE Battle Royale sub-genre that best fits this game")
    casual_subgenre: Optional[str] = Field(None, description="Choose ONE Casual sub-genre that best fits this game")
    fighting_subgenre: Optional[str] = Field(None, description="Choose ONE Fighting sub-genre that best fits this game")
    fps_subgenre: Optional[str] = Field(None, description="Choose ONE FPS sub-genre that best fits this game")
    mmorpg_subgenre: Optional[str] = Field(None, description="Choose ONE MMORPG sub-genre that best fits this game")
    party_subgenre: Optional[str] = Field(None, description="Choose ONE Party sub-genre that best fits this game")
    platformer_subgenre: Optional[str] = Field(None, description="Choose ONE Platformer sub-genre that best fits this game")
    puzzle_subgenre: Optional[str] = Field(None, description="Choose ONE Puzzle sub-genre that best fits this game")
    racing_subgenre: Optional[str] = Field(None, description="Choose ONE Racing sub-genre that best fits this game")
    rts_subgenre: Optional[str] = Field(None, description="Choose ONE RTS sub-genre that best fits this game")
    rpg_subgenre: Optional[str] = Field(None, description="Choose ONE RPG sub-genre that best fits this game")
    shooter_subgenre: Optional[str] = Field(None, description="Choose ONE Shooter sub-genre that best fits this game")
    simulation_subgenre: Optional[str] = Field(None, description="Choose ONE Simulation sub-genre that best fits this game")
    sports_subgenre: Optional[str] = Field(None, description="Choose ONE Sports sub-genre that best fits this game")
    stealth_subgenre: Optional[str] = Field(None, description="Choose ONE stealth sub-genre that best fits this game")
    strategy_subgenre: Optional[str] = Field(None, description="Choose ONE Strategy sub-genre that best fits this game")
    survival_subgenre: Optional[str] = Field(None, description="Choose ONE Survival sub-genre that best fits this game")
    tactical_rpg_subgenre: Optional[str] = Field(None, description="Choose ONE Tactical RPG sub-genre that best fits this game")
    
    # NFT information
    involves_nfts: Optional[str] = Field(None, description="Does this project involve NFTs?")
    nft_marketplace_link: Optional[str] = Field(None, description="Marketplace link for NFTs for this game")
    game_style: Optional[str] = Field(None, description="Game Style (check all that apply)")
    
    # Social media links
    twitter_link: Optional[str] = Field(None, description="Twitter (X) link")
    discord_link: Optional[str] = Field(None, description="Discord Server Link")
    telegram_link: Optional[str] = Field(None, description="Telegram Channel Link")
    medium_link: Optional[str] = Field(None, description="Medium Link")
    youtube_link: Optional[str] = Field(None, description="Youtube Channel")
    linkedin_link: Optional[str] = Field(None, description="LinkedIn")
    facebook_page: Optional[str] = Field(None, description="Facebook Page")
    reddit_link: Optional[str] = Field(None, description="Reddit - Subreddit or User")
    
    # Blockchain scanner links
    token_1_scanner_link: Optional[str] = Field(None, description="Token 1 Blockchain Scanner link")
    token_2_scanner_link: Optional[str] = Field(None, description="Token 2 - Blockchain Scanner Link")
    nft_scanner_link: Optional[str] = Field(None, description="Blockchain scanner link of NFT Contract Address")
    
    # NFT details
    nft_function: Optional[str] = Field(None, description="What are the NFTs used for?")
    nft_marketplace_links: Optional[str] = Field(None, description="NFT Marketplace Links")
    nft_contract_address: Optional[str] = Field(None, description="What is the NFT Contract Address?")
    includes_nfts: Optional[str] = Field(None, description="Does this game include NFTs?")
    nft_name: Optional[str] = Field(None, description="NFT name")
    nft_holders_count: Optional[str] = Field(None, description="How many wallets are holding this NFT?")
    nft_holders_source: Optional[str] = Field(None, description="Source for number of NFT-holding wallets")
    
    # Metrics
    daily_active_users: Optional[str] = Field(None, description="On the average, how many daily active users (DAU) in the past month?")
    dau_source: Optional[str] = Field(None, description="Link to Source of DAU count")
    daily_unique_wallets: Optional[str] = Field(None, description="On the average, how many daily unique active wallets (UAW) in the past month?")
    uaw_source: Optional[str] = Field(None, description="Link to Source of UAW count")
    
    # Auto-Clicker information
    autoclicker_telegram_invite: Optional[str] = Field(None, description="Auto-Clicker Telegram Invite Link")
    autoclicker_telegram_channel: Optional[str] = Field(None, description="Auto-Clicker Telegram Community Channel Link")
    autoclicker_telegram_bot: Optional[str] = Field(None, description="Auto-Clicker Telegram Bot Address")
    autoclicker_membership_population: Optional[str] = Field(None, description="Auto-Clicker TG Membership Population")
    autoclicker_twitter: Optional[str] = Field(None, description="Auto-Clicker Twitter (X) Page")
    autoclicker_discord: Optional[str] = Field(None, description="Auto-Clicker Discord invite link")
    autoclicker_medium: Optional[str] = Field(None, description="Auto-Clicker Medium Link")
    autoclicker_youtube: Optional[str] = Field(None, description="Auto-Clicker YouTube Link")
    autoclicker_has_token: Optional[str] = Field(None, description="Does this Auto-Clicker have an official Token?")
    
    # Additional information
    notes_and_comments: Optional[str] = Field(None, description="Notes and Comments")
    additional_notes: Optional[str] = Field(None, description="Additional Notes and Comments")
    additional_tokens: Optional[str] = Field(None, description="Additional Tokens")
    token_1_total_supply: Optional[str] = Field(None, description="Token 1 Total Supply")
    token_2_total_supply: Optional[str] = Field(None, description="Token 2 Total Supply")
    developer: Optional[str] = Field(None, description="Developer")
    platform: Optional[str] = Field(None, description="Platform")
    notes_additional_tokens: Optional[str] = Field(None, description="Notes, Additional Tokens")
    
    @validator('project_name')
    def validate_project_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Project name is required')
        return v.strip()
    
    @validator('*', pre=True)
    def empty_str_to_none(cls, v):
        if v == '':
            return None
        return v


class FormSubmissionResponse(BaseModel):
    """Response model for form submission"""
    success: bool
    message: str
    project_name: str
    csv_row_added: bool
    config_reloaded: bool
    timestamp: str


def get_csv_headers() -> List[str]:
    """Get the CSV headers from the existing file"""
    try:
        with open(CSV_FILE_PATH, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            headers = next(reader)
            return headers
    except FileNotFoundError:
        logger.error(f"CSV file not found: {CSV_FILE_PATH}")
        raise HTTPException(status_code=500, detail="CSV file not found")
    except Exception as e:
        logger.error(f"Error reading CSV headers: {e}")
        raise HTTPException(status_code=500, detail="Error reading CSV file")


def form_data_to_csv_row(form_data: GamingProjectFormData, headers: List[str]) -> List[str]:
    """Convert form data to CSV row matching the headers"""
    # Create a mapping from form field names to CSV column names
    field_mapping = {
        'project_name': 'Project Name',
        'project_website_link': 'Project Website Link',
        'whitepaper_link': 'Whitepaper Link',
        'blockchain': 'Blockchain',
        'validated_game_status': 'Validated Game Status',
        'game_status_notes': 'Game Status Notes',
        'token_schedule_link': 'Token Schedule Link',
        'token_1_symbol': 'Token 1 - Symbol',
        'token_1_contract_address': 'Token 1 - Token Contract Address',
        'token_1_type': 'Token 1 - Type',
        'token_1_coingecko_link': 'Token 1 Link to CoinGecko Listing',
        'token_2_symbol': 'Token 2 - Symbol',
        'token_2_contract_address': 'Token 2 - Token Contract Address',
        'token_2_type': 'Token 2 - Type',
        'token_2_coingecko_link': 'Token 2 Link to CoinGecko Listing',
        'genre': 'Which genre best fits this title? Choose only ONE',
        'action_subgenre': 'Choose ONE Action sub-genre that best fits this game',
        'action_adventure_subgenre': 'Choose ONE Action Adventure sub-genre that best fits this game',
        'action_rpg_subgenre': 'Choose ONE Action RPG sub-genre that best fits this game',
        'adventure_subgenre': 'Choose ONE Adventure sub-genre that best fits this game',
        'battle_royale_subgenre': 'Choose ONE Battle Royale sub-genre that best fits this game',
        'casual_subgenre': 'Choose ONE Casual sub-genre that best fits this game',
        'fighting_subgenre': 'Choose ONE Fighting sub-genre that best fits this game',
        'fps_subgenre': 'Choose ONE FPS sub-genre that best fits this game',
        'mmorpg_subgenre': 'Choose ONE MMORPG sub-genre that best fits this game',
        'party_subgenre': 'Choose ONE Party sub-genre that best fits this game',
        'platformer_subgenre': 'Choose ONE Platformer sub-genre that best fits this game',
        'puzzle_subgenre': 'Choose ONE Puzzle sub-genre that best fits this game',
        'racing_subgenre': 'Choose ONE Racing sub-genre that best fits this game',
        'rts_subgenre': 'Choose ONE RTS sub-genre that best fits this game',
        'rpg_subgenre': 'Choose ONE RPG sub-genre that best fits this game',
        'shooter_subgenre': 'Choose ONE Shooter sub-genre that best fits this game',
        'simulation_subgenre': 'Choose ONE Simulation sub-genre that best fits this game',
        'sports_subgenre': 'Choose ONE Sports sub-genre that best fits this game',
        'stealth_subgenre': 'Choose ONE stealth sub-genre that best fits this game',
        'strategy_subgenre': 'Choose ONE Strategy sub-genre that best fits this game',
        'survival_subgenre': 'Choose ONE Survival sub-genre that best fits this game',
        'tactical_rpg_subgenre': 'Choose ONE Tactical RPG sub-genre that best fits this game',
        'involves_nfts': 'Does this project involve NFTs?',
        'nft_marketplace_link': 'Marketplace link for NFTs for this game',
        'game_style': 'Game Style (check all that apply)',
        'twitter_link': 'Twitter (X) link',
        'discord_link': 'Discord Server Link',
        'telegram_link': 'Telegram Channel Link',
        'medium_link': 'Medium Link',
        'youtube_link': 'Youtube Channel',
        'linkedin_link': 'LinkedIn',
        'facebook_page': 'Facebook Page',
        'reddit_link': 'Reddit - Subreddit or User',
        'token_1_scanner_link': 'Token 1 Blockchain Scanner link',
        'token_2_scanner_link': 'Token 2 - Blockchain Scanner Link',
        'nft_scanner_link': 'Blockchain scanner link of NFT Contract Address',
        'nft_function': 'What are the NFTs used for?',
        'nft_marketplace_links': 'NFT Marketplace Links',
        'nft_contract_address': 'What is the NFT Contract Address?',
        'includes_nfts': 'Does this game include NFTs?',
        'nft_name': 'NFT name',
        'nft_holders_count': 'How many wallets are holding this NFT?',
        'nft_holders_source': 'Source for number of NFT-holding wallets',
        'daily_active_users': 'On the average, how many daily active users (DAU) in the past month?',
        'dau_source': 'Link to Source of DAU count',
        'daily_unique_wallets': 'On the average, how many daily unique active wallets (UAW) in the past month?',
        'uaw_source': 'Link to Source of UAW count',
        'autoclicker_telegram_invite': 'Auto-Clicker Telegram Invite Link',
        'autoclicker_telegram_channel': 'Auto-Clicker Telegram Community Channel Link',
        'autoclicker_telegram_bot': 'Auto-Clicker Telegram Bot Address',
        'autoclicker_membership_population': 'Auto-Clicker TG Membership Population',
        'autoclicker_twitter': 'Auto-Clicker Twitter (X) Page',
        'autoclicker_discord': 'Auto-Clicker Discord invite link',
        'autoclicker_medium': 'Auto-Clicker Medium Link',
        'autoclicker_youtube': 'Auto-Clicker YouTube Link',
        'autoclicker_has_token': 'Does this Auto-Clicker have an official Token?',
        'notes_and_comments': 'Notes and Comments',
        'additional_notes': 'Additional Notes and Comments',
        'additional_tokens': 'Additional Tokens',
        'token_1_total_supply': 'Token 1 Total Supply',
        'token_2_total_supply': 'Token 2 Total Supply',
        'developer': 'Developer',
        'platform': 'Platform',
        'notes_additional_tokens': 'Notes, Additional Tokens'
    }
    
    # Create row data
    row_data = []
    form_dict = form_data.dict()
    
    for header in headers:
        if header == 'Timestamp':
            # Add current timestamp
            row_data.append(datetime.now().strftime('%m/%d/%Y %H:%M:%S'))
        elif header == 'Email Address':
            # Add placeholder email or empty
            row_data.append('')
        else:
            # Find matching form field
            form_field = None
            for field_name, csv_column in field_mapping.items():
                if csv_column == header:
                    form_field = field_name
                    break
            
            if form_field and form_dict.get(form_field):
                row_data.append(str(form_dict[form_field]))
            else:
                row_data.append('')
    
    return row_data


async def append_to_csv(form_data: GamingProjectFormData) -> bool:
    """Append form data to CSV file"""
    try:
        # Get CSV headers
        headers = get_csv_headers()
        
        # Convert form data to CSV row
        row_data = form_data_to_csv_row(form_data, headers)
        
        # Append to CSV file
        with open(CSV_FILE_PATH, 'a', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(row_data)
        
        logger.info(f"✅ Added new gaming project '{form_data.project_name}' to CSV")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to append to CSV: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save to CSV: {str(e)}")


async def reload_gaming_configuration():
    """Reload gaming configuration after CSV update"""
    try:
        # Reload the gaming project manager
        gaming_project_manager._load_projects()
        
        # Also reload gaming contracts and analytics if available
        try:
            from blockchain.gaming_contracts import gaming_contract_manager
            from blockchain.gaming_analytics import gaming_analytics
            
            gaming_contract_manager.reload_contracts_from_csv()
            gaming_analytics.reload_protocols_from_csv()
        except ImportError:
            logger.warning("Gaming contracts/analytics modules not available for reload")
        
        logger.info("✅ Gaming configuration reloaded successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to reload gaming configuration: {e}")
        return False


@router.post("/submit", response_model=FormSubmissionResponse)
async def submit_gaming_project_form(
    form_data: GamingProjectFormData,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Submit gaming project form data and append to CSV"""
    try:
        logger.info(f"📝 Received form submission for project: {form_data.project_name}")
        
        # Validate required fields
        if not form_data.project_name:
            raise HTTPException(status_code=400, detail="Project name is required")
        
        # Check if project already exists
        existing_projects = gaming_project_manager.get_all_projects()
        for project_key, project in existing_projects.items():
            if project.project_name.lower() == form_data.project_name.lower():
                logger.warning(f"⚠️ Project '{form_data.project_name}' already exists")
                raise HTTPException(
                    status_code=409, 
                    detail=f"Project '{form_data.project_name}' already exists in the database"
                )
        
        # Append to CSV file
        csv_success = await append_to_csv(form_data)
        
        # Reload configuration in background
        background_tasks.add_task(reload_gaming_configuration)
        
        response = FormSubmissionResponse(
            success=True,
            message=f"Gaming project '{form_data.project_name}' submitted successfully",
            project_name=form_data.project_name,
            csv_row_added=csv_success,
            config_reloaded=True,  # Will be done in background
            timestamp=datetime.now().isoformat()
        )
        
        logger.info(f"✅ Successfully processed form submission for '{form_data.project_name}'")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error processing form submission: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/validate-project/{project_name}")
async def validate_project_name(project_name: str):
    """Check if a project name already exists"""
    try:
        existing_projects = gaming_project_manager.get_all_projects()
        
        for project_key, project in existing_projects.items():
            if project.project_name.lower() == project_name.lower():
                return {
                    "exists": True,
                    "message": f"Project '{project_name}' already exists",
                    "existing_project": {
                        "name": project.project_name,
                        "website": project.website,
                        "blockchain": project.blockchain,
                        "status": project.status
                    }
                }
        
        return {
            "exists": False,
            "message": f"Project name '{project_name}' is available"
        }
        
    except Exception as e:
        logger.error(f"Error validating project name: {e}")
        raise HTTPException(status_code=500, detail="Error validating project name")


@router.get("/csv-headers")
async def get_csv_headers_endpoint():
    """Get the current CSV headers for reference"""
    try:
        headers = get_csv_headers()
        return {
            "headers": headers,
            "total_columns": len(headers)
        }
    except Exception as e:
        logger.error(f"Error getting CSV headers: {e}")
        raise HTTPException(status_code=500, detail="Error reading CSV headers")


@router.get("/csv-stats")
async def get_csv_stats():
    """Get statistics about the CSV file"""
    try:
        if not os.path.exists(CSV_FILE_PATH):
            return {
                "file_exists": False,
                "total_rows": 0,
                "total_projects": 0
            }

        with open(CSV_FILE_PATH, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            rows = list(reader)

        total_rows = len(rows)
        total_projects = max(0, total_rows - 1)  # Subtract header row

        return {
            "file_exists": True,
            "total_rows": total_rows,
            "total_projects": total_projects,
            "file_size_bytes": os.path.getsize(CSV_FILE_PATH),
            "last_modified": datetime.fromtimestamp(os.path.getmtime(CSV_FILE_PATH)).isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting CSV stats: {e}")
        raise HTTPException(status_code=500, detail="Error reading CSV file")


@router.get("/health")
async def health_check():
    """Health check endpoint for gaming form API"""
    try:
        # Check CSV file accessibility
        csv_accessible = os.path.exists(CSV_FILE_PATH) and os.access(CSV_FILE_PATH, os.R_OK | os.W_OK)

        # Check gaming project manager
        project_count = len(gaming_project_manager.get_all_projects())

        return {
            "status": "healthy",
            "csv_file_accessible": csv_accessible,
            "csv_file_path": CSV_FILE_PATH,
            "gaming_projects_loaded": project_count,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
