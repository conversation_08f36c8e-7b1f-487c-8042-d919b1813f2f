"""
Notification API Endpoints
Handles real-time notifications for gaming project detection and system events
"""
from fastapi import APIRouter, HTTPException, Query, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel
import json
import logging

from services.notification_service import notification_service, NotificationType

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/notifications", tags=["Notifications"])


class NotificationResponse(BaseModel):
    """Response model for notifications"""
    id: str
    type: str
    title: str
    message: str
    data: Dict[str, Any]
    timestamp: str
    priority: str
    read: bool


class NotificationStats(BaseModel):
    """Statistics for notifications"""
    total_notifications: int
    unread_count: int
    high_priority_count: int
    urgent_count: int


class MarkReadRequest(BaseModel):
    """Request model for marking notifications as read"""
    notification_ids: List[str]


# WebSocket connection manager for real-time notifications
class NotificationConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"New notification WebSocket connection. Total: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            logger.info(f"Notification WebSocket disconnected. Total: {len(self.active_connections)}")

    async def broadcast_notification(self, notification: Dict[str, Any]):
        """Broadcast notification to all connected clients"""
        if not self.active_connections:
            return

        message = json.dumps({
            "type": "notification",
            "data": notification
        })

        # Send to all connections, remove failed ones
        failed_connections = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error sending notification to WebSocket: {e}")
                failed_connections.append(connection)

        # Remove failed connections
        for connection in failed_connections:
            self.disconnect(connection)


# Global connection manager
notification_manager = NotificationConnectionManager()


# Subscribe to notification service
async def websocket_notification_callback(notification: Dict[str, Any]):
    """Callback to broadcast notifications via WebSocket"""
    await notification_manager.broadcast_notification(notification)


# Register the callback
notification_service.subscribe(websocket_notification_callback)


@router.get("/", response_model=List[NotificationResponse])
async def get_notifications(
    limit: int = Query(50, ge=1, le=100),
    unread_only: bool = Query(False),
    notification_type: Optional[str] = Query(None)
):
    """Get notifications with optional filtering"""
    try:
        # Convert string type to enum if provided
        type_filter = None
        if notification_type:
            try:
                type_filter = NotificationType(notification_type)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid notification type: {notification_type}")

        notifications = notification_service.get_notifications(
            limit=limit,
            unread_only=unread_only,
            notification_type=type_filter
        )

        return [NotificationResponse(**notification) for notification in notifications]

    except Exception as e:
        logger.error(f"Error fetching notifications: {e}")
        raise HTTPException(status_code=500, detail="Error fetching notifications")


@router.get("/stats", response_model=NotificationStats)
async def get_notification_stats():
    """Get notification statistics"""
    try:
        all_notifications = notification_service.get_notifications(limit=1000)
        unread_count = notification_service.get_unread_count()
        
        high_priority_count = sum(1 for n in all_notifications if n.get('priority') == 'high')
        urgent_count = sum(1 for n in all_notifications if n.get('priority') == 'urgent')

        return NotificationStats(
            total_notifications=len(all_notifications),
            unread_count=unread_count,
            high_priority_count=high_priority_count,
            urgent_count=urgent_count
        )

    except Exception as e:
        logger.error(f"Error fetching notification stats: {e}")
        raise HTTPException(status_code=500, detail="Error fetching notification statistics")


@router.post("/mark-read")
async def mark_notifications_read(request: MarkReadRequest):
    """Mark specific notifications as read"""
    try:
        marked_count = 0
        for notification_id in request.notification_ids:
            if notification_service.mark_as_read(notification_id):
                marked_count += 1

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Marked {marked_count} notifications as read",
                "marked_count": marked_count
            }
        )

    except Exception as e:
        logger.error(f"Error marking notifications as read: {e}")
        raise HTTPException(status_code=500, detail="Error marking notifications as read")


@router.post("/mark-all-read")
async def mark_all_notifications_read():
    """Mark all notifications as read"""
    try:
        marked_count = notification_service.mark_all_as_read()

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Marked {marked_count} notifications as read",
                "marked_count": marked_count
            }
        )

    except Exception as e:
        logger.error(f"Error marking all notifications as read: {e}")
        raise HTTPException(status_code=500, detail="Error marking all notifications as read")


@router.delete("/clear-old")
async def clear_old_notifications(days: int = Query(7, ge=1, le=30)):
    """Clear notifications older than specified days"""
    try:
        cleared_count = notification_service.clear_old_notifications(days)

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Cleared {cleared_count} old notifications",
                "cleared_count": cleared_count
            }
        )

    except Exception as e:
        logger.error(f"Error clearing old notifications: {e}")
        raise HTTPException(status_code=500, detail="Error clearing old notifications")


@router.get("/types")
async def get_notification_types():
    """Get available notification types"""
    return {
        "notification_types": [
            {
                "value": nt.value,
                "name": nt.name.replace('_', ' ').title()
            }
            for nt in NotificationType
        ]
    }


@router.websocket("/ws")
async def notification_websocket(websocket: WebSocket):
    """WebSocket endpoint for real-time notifications"""
    await notification_manager.connect(websocket)
    
    try:
        # Send initial notification stats
        stats = await get_notification_stats()
        await websocket.send_text(json.dumps({
            "type": "stats",
            "data": stats.dict()
        }))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (like ping/pong)
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
                elif message.get("type") == "get_stats":
                    stats = await get_notification_stats()
                    await websocket.send_text(json.dumps({
                        "type": "stats",
                        "data": stats.dict()
                    }))
                    
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format"
                }))
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                break
                
    except WebSocketDisconnect:
        notification_manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        notification_manager.disconnect(websocket)


# Test endpoint for sending sample notifications
@router.post("/test")
async def send_test_notification(
    title: str = "Test Notification",
    message: str = "This is a test notification",
    priority: str = "normal"
):
    """Send a test notification (for development/testing)"""
    try:
        notification_id = await notification_service.send_notification(
            NotificationType.SYSTEM_ALERT,
            title,
            message,
            {"test": True},
            priority
        )

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "Test notification sent",
                "notification_id": notification_id
            }
        )

    except Exception as e:
        logger.error(f"Error sending test notification: {e}")
        raise HTTPException(status_code=500, detail="Error sending test notification")


# Test endpoint for simulating gaming project detection
@router.post("/test-gaming-project")
async def test_gaming_project_detection():
    """Simulate a gaming project detection for testing the complete pipeline"""
    try:
        # Simulate a new gaming project detection
        await notification_service.notify_new_gaming_project(
            project_name="Test Gaming Project",
            contract_address="******************************************",
            blockchain="ethereum",
            confidence="HIGH",
            project_id=999
        )

        # Also simulate a project needing review
        await notification_service.notify_project_needs_review(
            project_name="Test Gaming Project",
            project_id=999,
            reason="Incomplete metadata - missing token information"
        )

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "Test gaming project notifications sent",
                "notifications_sent": 2
            }
        )

    except Exception as e:
        logger.error(f"Error sending test gaming project notifications: {e}")
        raise HTTPException(status_code=500, detail="Error sending test gaming project notifications")
