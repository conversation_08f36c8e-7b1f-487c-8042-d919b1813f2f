"""
Blockchain API Endpoints
Exposes blockchain integration functionality via REST API
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from blockchain import (
    multi_chain_manager,
    gaming_contract_manager,
    gaming_event_monitor,
    nft_tracker,
    gaming_market_data,
    blockchain_sync_manager,
    get_blockchain_health_status
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/blockchain", tags=["blockchain"])


@router.get("/health")
async def get_blockchain_health():
    """Get blockchain service health status"""
    try:
        health_status = await get_blockchain_health_status()
        return health_status
    except Exception as e:
        logger.error(f"Error getting blockchain health: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/chains")
async def get_supported_chains():
    """Get list of supported blockchain networks"""
    try:
        chains = multi_chain_manager.get_gaming_chains()
        chain_info = {}
        
        for chain in chains:
            client = multi_chain_manager.get_client(chain)
            if client:
                latest_block = await client.get_latest_block_number()
                chain_info[chain] = {
                    'name': chain,
                    'type': client.__class__.__name__,
                    'latest_block': latest_block,
                    'status': 'connected' if latest_block else 'disconnected'
                }
        
        return {
            'supported_chains': chains,
            'chain_details': chain_info
        }
    except Exception as e:
        logger.error(f"Error getting supported chains: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/contracts")
async def get_gaming_contracts(
    chain: Optional[str] = Query(None, description="Filter by blockchain"),
    contract_type: Optional[str] = Query(None, description="Filter by contract type")
):
    """Get gaming contracts"""
    try:
        if chain and contract_type:
            contracts = gaming_contract_manager.get_contracts_by_type(chain, contract_type)
        elif chain:
            contracts = gaming_contract_manager.get_contracts_by_chain(chain)
        else:
            contracts = gaming_contract_manager.get_all_contracts()
        
        return {
            'contracts': [
                {
                    'name': contract.name,
                    'address': contract.address,
                    'chain': contract.chain,
                    'contract_type': contract.contract_type.value,
                    'token_symbol': contract.token_symbol,
                    'description': contract.description
                }
                for contract in contracts
            ],
            'total_count': len(contracts)
        }
    except Exception as e:
        logger.error(f"Error getting gaming contracts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/events")
async def get_gaming_events(
    chain: Optional[str] = Query(None, description="Filter by blockchain"),
    contract_address: Optional[str] = Query(None, description="Filter by contract address"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    limit: int = Query(100, description="Maximum number of events to return")
):
    """Get recent gaming events"""
    try:
        # This would typically query the database for recent events
        # For now, return a placeholder response
        return {
            'events': [],
            'filters': {
                'chain': chain,
                'contract_address': contract_address,
                'event_type': event_type,
                'limit': limit
            },
            'message': 'Event monitoring is active. Events will be stored in database.'
        }
    except Exception as e:
        logger.error(f"Error getting gaming events: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/nft/{chain}/{contract_address}/{token_id}")
async def get_nft_details(
    chain: str,
    contract_address: str,
    token_id: str
):
    """Get NFT details and history"""
    try:
        # Get asset history
        history = await nft_tracker.get_asset_history(chain, contract_address, token_id)
        
        # Get current asset info from cache if available
        asset_id = f"{chain}:{contract_address.lower()}:{token_id}"
        current_asset = nft_tracker.asset_cache.get(asset_id)
        
        response = {
            'chain': chain,
            'contract_address': contract_address,
            'token_id': token_id,
            'history': history,
            'current_owner': current_asset.owner_address if current_asset else None,
            'collection_name': current_asset.collection_name if current_asset else None,
            'metadata': current_asset.metadata.__dict__ if current_asset and current_asset.metadata else None
        }
        
        return response
    except Exception as e:
        logger.error(f"Error getting NFT details: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/nft/collections/{chain}/{contract_address}/stats")
async def get_collection_stats(chain: str, contract_address: str):
    """Get NFT collection statistics"""
    try:
        stats = await nft_tracker.get_collection_stats(chain, contract_address)
        return {
            'chain': chain,
            'contract_address': contract_address,
            'stats': stats
        }
    except Exception as e:
        logger.error(f"Error getting collection stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tokens/prices")
async def get_gaming_token_prices():
    """Get current prices for all gaming tokens"""
    try:
        prices = await gaming_market_data.get_all_gaming_token_prices()
        
        price_data = {}
        for symbol, price in prices.items():
            price_data[symbol] = {
                'symbol': price.symbol,
                'contract_address': price.contract_address,
                'chain': price.chain,
                'price_usd': price.price_usd,
                'price_change_24h': price.price_change_24h,
                'market_cap': price.market_cap,
                'volume_24h': price.volume_24h,
                'last_updated': price.last_updated.isoformat()
            }
        
        return {
            'prices': price_data,
            'cache_stats': gaming_market_data.get_price_cache_stats()
        }
    except Exception as e:
        logger.error(f"Error getting gaming token prices: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tokens/{chain}/{contract_address}/price")
async def get_token_price(
    chain: str,
    contract_address: str,
    symbol: Optional[str] = Query(None, description="Token symbol")
):
    """Get price for a specific token"""
    try:
        price = await gaming_market_data.get_token_price(chain, contract_address, symbol or "")
        
        if not price:
            raise HTTPException(status_code=404, detail="Token price not found")
        
        return {
            'symbol': price.symbol,
            'contract_address': price.contract_address,
            'chain': price.chain,
            'price_usd': price.price_usd,
            'price_change_24h': price.price_change_24h,
            'market_cap': price.market_cap,
            'volume_24h': price.volume_24h,
            'circulating_supply': price.circulating_supply,
            'total_supply': price.total_supply,
            'last_updated': price.last_updated.isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting token price: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sync/status")
async def get_sync_status():
    """Get blockchain synchronization status"""
    try:
        status = blockchain_sync_manager.get_sync_status()
        return status
    except Exception as e:
        logger.error(f"Error getting sync status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sync/start")
async def start_synchronization():
    """Start blockchain synchronization"""
    try:
        if blockchain_sync_manager.sync_active:
            return {'message': 'Synchronization is already active'}
        
        # Start sync in background
        import asyncio
        asyncio.create_task(blockchain_sync_manager.start_synchronization())
        
        return {'message': 'Blockchain synchronization started'}
    except Exception as e:
        logger.error(f"Error starting synchronization: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sync/stop")
async def stop_synchronization():
    """Stop blockchain synchronization"""
    try:
        await blockchain_sync_manager.stop_synchronization()
        return {'message': 'Blockchain synchronization stopped'}
    except Exception as e:
        logger.error(f"Error stopping synchronization: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sync/resync/{chain}")
async def force_resync_chain(
    chain: str,
    from_block: int = Query(0, description="Block number to resync from")
):
    """Force resync a specific chain"""
    try:
        await blockchain_sync_manager.force_resync_chain(chain, from_block)
        return {
            'message': f'Forced resync for {chain} from block {from_block}',
            'chain': chain,
            'from_block': from_block
        }
    except Exception as e:
        logger.error(f"Error forcing resync: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/rpc/health")
async def get_rpc_health():
    """Get RPC endpoint health status"""
    try:
        from blockchain.enhanced_rpc import enhanced_rpc_manager
        
        health_data = {}
        for chain in multi_chain_manager.get_gaming_chains():
            endpoints = enhanced_rpc_manager.get_endpoints_for_chain(chain)
            chain_health = []
            
            for endpoint in endpoints:
                chain_health.append({
                    'url': endpoint.url,
                    'priority': endpoint.priority,
                    'health_score': endpoint.health_score,
                    'success_rate': endpoint.success_rate,
                    'avg_response_time': endpoint.avg_response_time,
                    'is_healthy': endpoint.is_healthy(),
                    'last_check': endpoint.last_check.isoformat() if endpoint.last_check else None
                })
            
            health_data[chain] = chain_health
        
        return {
            'rpc_endpoints': health_data,
            'timestamp': datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting RPC health: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/discover/tokens")
async def discover_gaming_tokens():
    """Discover new gaming tokens from various sources"""
    try:
        new_tokens = await gaming_market_data.discover_new_gaming_tokens()
        
        return {
            'discovered_tokens': new_tokens,
            'count': len(new_tokens),
            'timestamp': datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Error discovering gaming tokens: {e}")
        raise HTTPException(status_code=500, detail=str(e))
