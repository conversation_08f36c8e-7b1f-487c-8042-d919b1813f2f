"""
Enhanced Gaming News API Endpoints for Phase 4
Cross-chain gaming content aggregation and analysis
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel

from models.base import get_db
from models.gaming import Article, Source
from scrapers.news.gaming_sources import GAMING_SCRAPERS, blockchain_classifier
from scrapers.news.entity_recognition import entity_engine
from scrapers.news.cross_chain_aggregator import (
    cross_chain_aggregator, 
    AggregationStrategy,
    ContentPriority
)

router = APIRouter(prefix="/gaming", tags=["gaming"])


class GamingArticleResponse(BaseModel):
    """Enhanced gaming article response"""
    id: int
    title: str
    content: Optional[str]
    summary: Optional[str]
    url: str
    author: Optional[str]
    published_at: datetime
    source_name: str
    blockchain_networks: List[str]
    gaming_categories: List[str]
    detected_projects: List[str]
    network_focus: str
    priority_score: Optional[float]
    relevance_score: Optional[float]
    is_solana_focused: bool
    is_multi_chain: bool


class NetworkDistributionResponse(BaseModel):
    """Network distribution statistics"""
    network: str
    article_count: int
    percentage: float


class GamingAnalyticsResponse(BaseModel):
    """Gaming content analytics"""
    total_articles: int
    network_distribution: List[NetworkDistributionResponse]
    top_gaming_projects: List[Dict[str, Any]]
    solana_articles: int
    ethereum_articles: int
    multi_chain_articles: int
    recent_activity: Dict[str, int]


@router.get("/articles", response_model=List[GamingArticleResponse])
async def get_gaming_articles(
    db: Session = Depends(get_db),
    limit: int = Query(50, le=200),
    offset: int = Query(0, ge=0),
    network: Optional[str] = Query(None, description="Filter by blockchain network"),
    project: Optional[str] = Query(None, description="Filter by gaming project"),
    category: Optional[str] = Query(None, description="Filter by gaming category"),
    solana_only: bool = Query(False, description="Show only Solana-focused content"),
    strategy: str = Query("relevance_based", description="Aggregation strategy")
):
    """Get enhanced gaming articles with blockchain-aware classification"""
    
    query = db.query(Article).join(Source)
    
    # Apply filters based on enhanced metadata
    if network:
        query = query.filter(
            Article.extra_metadata.op('->>')('blockchain_classification').op('->>')('blockchain_networks').contains(f'"{network}"')
        )
    
    if project:
        query = query.filter(
            Article.extra_metadata.op('->>')('entity_analysis').op('->>')('detected_projects').contains(f'"{project}"')
        )
    
    if category:
        query = query.filter(
            Article.extra_metadata.op('->>')('blockchain_classification').op('->>')('gaming_categories').contains(f'"{category}"')
        )
    
    if solana_only:
        query = query.filter(
            Article.extra_metadata.op('->>')('blockchain_classification').op('->>')('is_solana_focused').astext == 'true'
        )
    
    # Order by relevance score if available, otherwise by published date
    query = query.order_by(
        Article.extra_metadata.op('->>')('relevance_score').desc().nullslast(),
        Article.published_at.desc()
    )
    
    articles = query.offset(offset).limit(limit).all()
    
    # Convert to response format
    response_articles = []
    for article in articles:
        metadata = article.extra_metadata or {}
        blockchain_classification = metadata.get('blockchain_classification', {})
        entity_analysis = metadata.get('entity_analysis', {})
        
        response_articles.append(GamingArticleResponse(
            id=article.id,
            title=article.title,
            content=article.content,
            summary=article.summary,
            url=article.url,
            author=article.author,
            published_at=article.published_at,
            source_name=article.source.name,
            blockchain_networks=blockchain_classification.get('blockchain_networks', []),
            gaming_categories=blockchain_classification.get('gaming_categories', []),
            detected_projects=entity_analysis.get('detected_projects', []),
            network_focus=entity_analysis.get('primary_network', 'unknown'),
            priority_score=metadata.get('priority_score'),
            relevance_score=metadata.get('relevance_score'),
            is_solana_focused=blockchain_classification.get('is_solana_focused', False),
            is_multi_chain=blockchain_classification.get('is_multi_chain', False)
        ))
    
    return response_articles


@router.get("/analytics", response_model=GamingAnalyticsResponse)
async def get_gaming_analytics(
    db: Session = Depends(get_db),
    days: int = Query(7, description="Number of days to analyze")
):
    """Get gaming content analytics and insights"""
    
    # Get articles from the specified time period
    since_date = datetime.now() - timedelta(days=days)
    articles = db.query(Article).filter(
        Article.published_at >= since_date,
        Article.extra_metadata.has_key('phase_4_enhanced')
    ).all()
    
    total_articles = len(articles)
    
    # Network distribution
    network_counts = {}
    solana_count = 0
    ethereum_count = 0
    multi_chain_count = 0
    
    # Project mentions
    project_mentions = {}
    
    for article in articles:
        metadata = article.extra_metadata or {}
        blockchain_classification = metadata.get('blockchain_classification', {})
        entity_analysis = metadata.get('entity_analysis', {})
        
        # Count networks
        networks = blockchain_classification.get('blockchain_networks', [])
        for network in networks:
            network_counts[network] = network_counts.get(network, 0) + 1
        
        # Count special categories
        if blockchain_classification.get('is_solana_focused', False):
            solana_count += 1
        if 'ethereum' in networks:
            ethereum_count += 1
        if blockchain_classification.get('is_multi_chain', False):
            multi_chain_count += 1
        
        # Count project mentions
        projects = entity_analysis.get('detected_projects', [])
        for project in projects:
            project_mentions[project] = project_mentions.get(project, 0) + 1
    
    # Convert network distribution to response format
    network_distribution = []
    for network, count in network_counts.items():
        percentage = (count / total_articles * 100) if total_articles > 0 else 0
        network_distribution.append(NetworkDistributionResponse(
            network=network,
            article_count=count,
            percentage=round(percentage, 2)
        ))
    
    # Sort by count
    network_distribution.sort(key=lambda x: x.article_count, reverse=True)
    
    # Top gaming projects
    top_projects = [
        {"project": project, "mentions": count}
        for project, count in sorted(project_mentions.items(), key=lambda x: x[1], reverse=True)[:10]
    ]
    
    # Recent activity (daily breakdown)
    recent_activity = {}
    for i in range(days):
        date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
        day_start = datetime.now() - timedelta(days=i+1)
        day_end = datetime.now() - timedelta(days=i)
        
        day_articles = [
            a for a in articles 
            if day_start <= a.published_at < day_end
        ]
        recent_activity[date] = len(day_articles)
    
    return GamingAnalyticsResponse(
        total_articles=total_articles,
        network_distribution=network_distribution,
        top_gaming_projects=top_projects,
        solana_articles=solana_count,
        ethereum_articles=ethereum_count,
        multi_chain_articles=multi_chain_count,
        recent_activity=recent_activity
    )


@router.get("/networks")
async def get_supported_networks():
    """Get list of supported blockchain networks"""
    from scrapers.news.gaming_sources import BlockchainNetwork
    
    networks = [
        {
            "name": network.value,
            "display_name": network.value.replace('_', ' ').title(),
            "is_gaming_focused": network.value in ['immutable_x', 'ronin'],
            "ecosystem": "solana" if network.value == "solana" else "evm" if network.value not in ["ton", "solana"] else "other"
        }
        for network in BlockchainNetwork
        if network != BlockchainNetwork.UNKNOWN
    ]
    
    return {"networks": networks}


@router.get("/projects")
async def get_gaming_projects():
    """Get list of known gaming projects"""
    projects = []
    
    for project_id, project in entity_engine.project_registry.projects.items():
        projects.append({
            "id": project_id,
            "name": project.name,
            "aliases": project.aliases,
            "blockchain_networks": project.blockchain_networks,
            "project_type": project.project_type,
            "token_symbols": project.token_symbols,
            "description": project.description,
            "is_active": project.is_active
        })
    
    return {"projects": projects}


@router.get("/sources")
async def get_gaming_sources():
    """Get list of gaming news sources with their capabilities"""
    sources = []
    
    for source_name, scraper_class in GAMING_SCRAPERS.items():
        # Determine source characteristics
        is_solana_focused = 'solana' in source_name.lower()
        is_mainstream = source_name in ['coindesk-gaming', 'decrypt-gaming', 'theblock-gaming']
        is_gaming_native = source_name in ['gam3s', 'chainplay', 'playtoearn-com']
        
        sources.append({
            "name": source_name,
            "scraper_class": scraper_class.__name__,
            "is_solana_focused": is_solana_focused,
            "is_mainstream_crypto": is_mainstream,
            "is_gaming_native": is_gaming_native,
            "supports_rss": "RSS" in scraper_class.__name__,
            "supports_web_scraping": "Web" in scraper_class.__name__
        })
    
    return {"sources": sources}


@router.post("/classify")
async def classify_content(
    title: str,
    content: str = "",
    summary: str = ""
):
    """Classify gaming content using blockchain-aware classifier"""
    
    # Blockchain classification
    blockchain_classification = blockchain_classifier.classify_content(title, content, summary)
    
    # Entity recognition
    entity_analysis = entity_engine.analyze_content(title, content, summary)
    
    return {
        "blockchain_classification": blockchain_classification,
        "entity_analysis": entity_analysis,
        "classification_timestamp": datetime.now().isoformat()
    }


@router.get("/solana/articles", response_model=List[GamingArticleResponse])
async def get_solana_gaming_articles(
    db: Session = Depends(get_db),
    limit: int = Query(50, le=200),
    offset: int = Query(0, ge=0)
):
    """Get articles specifically focused on Solana gaming ecosystem"""
    return await get_gaming_articles(
        db=db, 
        limit=limit, 
        offset=offset, 
        solana_only=True
    )


@router.get("/cross-chain/comparison")
async def get_cross_chain_comparison(
    db: Session = Depends(get_db),
    days: int = Query(7, description="Number of days to analyze")
):
    """Compare gaming activity across different blockchain networks"""
    
    since_date = datetime.now() - timedelta(days=days)
    articles = db.query(Article).filter(
        Article.published_at >= since_date,
        Article.extra_metadata.has_key('phase_4_enhanced')
    ).all()
    
    network_comparison = {}
    
    for article in articles:
        metadata = article.extra_metadata or {}
        blockchain_classification = metadata.get('blockchain_classification', {})
        entity_analysis = metadata.get('entity_analysis', {})
        
        networks = blockchain_classification.get('blockchain_networks', [])
        for network in networks:
            if network not in network_comparison:
                network_comparison[network] = {
                    'article_count': 0,
                    'unique_projects': set(),
                    'gaming_categories': set(),
                    'avg_relevance_score': 0,
                    'total_relevance': 0
                }
            
            network_comparison[network]['article_count'] += 1
            network_comparison[network]['unique_projects'].update(
                entity_analysis.get('detected_projects', [])
            )
            network_comparison[network]['gaming_categories'].update(
                blockchain_classification.get('gaming_categories', [])
            )
            
            relevance = metadata.get('relevance_score', 0)
            network_comparison[network]['total_relevance'] += relevance
    
    # Calculate averages and convert sets to lists
    for network, data in network_comparison.items():
        if data['article_count'] > 0:
            data['avg_relevance_score'] = data['total_relevance'] / data['article_count']
        data['unique_projects'] = list(data['unique_projects'])
        data['gaming_categories'] = list(data['gaming_categories'])
        del data['total_relevance']  # Remove intermediate calculation
    
    return {
        "comparison_period_days": days,
        "network_comparison": network_comparison,
        "analysis_timestamp": datetime.now().isoformat()
    }
