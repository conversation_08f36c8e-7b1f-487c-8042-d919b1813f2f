"""
Gaming Analytics API Endpoints
Phase 6: Advanced Gaming Protocol Analytics
"""
from fastapi import APIRouter, HTTPException, Query, BackgroundTasks, WebSocket, WebSocketDisconnect
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from pydantic import BaseModel
import asyncio
import json
import logging

from blockchain.gaming_analytics import gaming_analytics, GamingProtocolMetrics, MetricType
from api.dashboard_endpoints import dashboard_metrics
from api.websocket_manager import manager

router = APIRouter(prefix="/gaming-analytics", tags=["Gaming Analytics"])
logger = logging.getLogger(__name__)


class ProtocolMetricsResponse(BaseModel):
    """Response model for protocol metrics"""
    protocol_name: str
    chain: str
    timestamp: datetime
    
    # User metrics
    daily_active_users: Optional[int] = None
    monthly_active_users: Optional[int] = None
    new_users_24h: Optional[int] = None
    user_retention_rate: Optional[float] = None
    
    # Transaction metrics
    transaction_count_24h: Optional[int] = None
    transaction_volume_24h: Optional[float] = None
    average_transaction_value: Optional[float] = None
    gas_fees_24h: Optional[float] = None
    
    # Token metrics
    token_price: Optional[float] = None
    token_price_change_24h: Optional[float] = None
    market_cap: Optional[float] = None
    trading_volume_24h: Optional[float] = None
    circulating_supply: Optional[float] = None
    
    # NFT metrics
    nft_trades_24h: Optional[int] = None
    nft_volume_24h: Optional[float] = None
    floor_price: Optional[float] = None
    unique_holders: Optional[int] = None
    
    # Protocol-specific metrics
    total_value_locked: Optional[float] = None
    protocol_revenue_24h: Optional[float] = None
    staking_rewards_distributed: Optional[float] = None
    
    # P2E economics
    average_earnings_per_user: Optional[float] = None
    reward_token_distribution: Optional[float] = None
    gameplay_sessions_24h: Optional[int] = None
    
    # Health indicators
    protocol_uptime: Optional[float] = None
    smart_contract_interactions: Optional[int] = None
    developer_activity_score: Optional[float] = None


class ProtocolSummaryResponse(BaseModel):
    """Response model for protocol summary"""
    total_protocols: int
    total_market_cap: float
    total_daily_active_users: int
    total_transaction_volume_24h: float
    total_nft_volume_24h: float
    average_protocol_uptime: float
    protocols: Dict[str, Any]
    last_updated: str


@router.get("/protocols", response_model=List[str])
async def get_supported_protocols():
    """Get list of supported gaming protocols"""
    return list(gaming_analytics.supported_protocols.keys())


@router.get("/protocols/{protocol_name}", response_model=ProtocolMetricsResponse)
async def get_protocol_metrics(protocol_name: str):
    """Get comprehensive metrics for a specific gaming protocol"""
    if protocol_name not in gaming_analytics.supported_protocols:
        raise HTTPException(status_code=404, detail=f"Protocol {protocol_name} not supported")
    
    try:
        metrics = await gaming_analytics.collect_protocol_metrics(protocol_name)
        if not metrics:
            raise HTTPException(status_code=500, detail=f"Failed to collect metrics for {protocol_name}")
        
        return ProtocolMetricsResponse(
            protocol_name=metrics.protocol_name,
            chain=metrics.chain,
            timestamp=metrics.timestamp,
            daily_active_users=metrics.daily_active_users,
            monthly_active_users=metrics.monthly_active_users,
            new_users_24h=metrics.new_users_24h,
            user_retention_rate=metrics.user_retention_rate,
            transaction_count_24h=metrics.transaction_count_24h,
            transaction_volume_24h=metrics.transaction_volume_24h,
            average_transaction_value=metrics.average_transaction_value,
            gas_fees_24h=metrics.gas_fees_24h,
            token_price=metrics.token_price,
            token_price_change_24h=metrics.token_price_change_24h,
            market_cap=metrics.market_cap,
            trading_volume_24h=metrics.trading_volume_24h,
            circulating_supply=metrics.circulating_supply,
            nft_trades_24h=metrics.nft_trades_24h,
            nft_volume_24h=metrics.nft_volume_24h,
            floor_price=metrics.floor_price,
            unique_holders=metrics.unique_holders,
            total_value_locked=metrics.total_value_locked,
            protocol_revenue_24h=metrics.protocol_revenue_24h,
            staking_rewards_distributed=metrics.staking_rewards_distributed,
            average_earnings_per_user=metrics.average_earnings_per_user,
            reward_token_distribution=metrics.reward_token_distribution,
            gameplay_sessions_24h=metrics.gameplay_sessions_24h,
            protocol_uptime=metrics.protocol_uptime,
            smart_contract_interactions=metrics.smart_contract_interactions,
            developer_activity_score=metrics.developer_activity_score
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error collecting metrics: {str(e)}")


@router.get("/summary", response_model=ProtocolSummaryResponse)
async def get_gaming_protocols_summary():
    """Get summary of all gaming protocol metrics"""
    try:
        summary = await gaming_analytics.get_protocol_summary()
        return ProtocolSummaryResponse(**summary)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating summary: {str(e)}")


@router.get("/protocols/{protocol_name}/user-activity")
async def get_protocol_user_activity(protocol_name: str, days: int = Query(7, ge=1, le=30)):
    """Get user activity metrics for a protocol over time"""
    if protocol_name not in gaming_analytics.supported_protocols:
        raise HTTPException(status_code=404, detail=f"Protocol {protocol_name} not supported")
    
    try:
        # For now, return current metrics
        # In production, this would query historical data
        metrics = await gaming_analytics.collect_protocol_metrics(protocol_name)
        if not metrics:
            raise HTTPException(status_code=500, detail=f"Failed to collect metrics for {protocol_name}")
        
        return {
            "protocol_name": protocol_name,
            "period_days": days,
            "current_metrics": {
                "daily_active_users": metrics.daily_active_users,
                "monthly_active_users": metrics.monthly_active_users,
                "new_users_24h": metrics.new_users_24h,
                "user_retention_rate": metrics.user_retention_rate,
                "gameplay_sessions_24h": metrics.gameplay_sessions_24h
            },
            "historical_data": {
                "note": "Historical data collection will be implemented in future updates"
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error collecting user activity: {str(e)}")


@router.get("/protocols/{protocol_name}/token-metrics")
async def get_protocol_token_metrics(protocol_name: str):
    """Get token-specific metrics for a protocol"""
    if protocol_name not in gaming_analytics.supported_protocols:
        raise HTTPException(status_code=404, detail=f"Protocol {protocol_name} not supported")
    
    try:
        metrics = await gaming_analytics.collect_protocol_metrics(protocol_name)
        if not metrics:
            raise HTTPException(status_code=500, detail=f"Failed to collect metrics for {protocol_name}")
        
        return {
            "protocol_name": protocol_name,
            "token_metrics": {
                "price": metrics.token_price,
                "price_change_24h": metrics.token_price_change_24h,
                "market_cap": metrics.market_cap,
                "trading_volume_24h": metrics.trading_volume_24h,
                "circulating_supply": metrics.circulating_supply,
                "transaction_count_24h": metrics.transaction_count_24h,
                "transaction_volume_24h": metrics.transaction_volume_24h,
                "unique_holders": metrics.unique_holders
            },
            "timestamp": metrics.timestamp.isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error collecting token metrics: {str(e)}")


@router.get("/protocols/{protocol_name}/nft-metrics")
async def get_protocol_nft_metrics(protocol_name: str):
    """Get NFT-specific metrics for a protocol"""
    if protocol_name not in gaming_analytics.supported_protocols:
        raise HTTPException(status_code=404, detail=f"Protocol {protocol_name} not supported")
    
    try:
        metrics = await gaming_analytics.collect_protocol_metrics(protocol_name)
        if not metrics:
            raise HTTPException(status_code=500, detail=f"Failed to collect metrics for {protocol_name}")
        
        return {
            "protocol_name": protocol_name,
            "nft_metrics": {
                "floor_price": metrics.floor_price,
                "volume_24h": metrics.nft_volume_24h,
                "trades_24h": metrics.nft_trades_24h,
                "unique_holders": metrics.unique_holders
            },
            "timestamp": metrics.timestamp.isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error collecting NFT metrics: {str(e)}")


@router.post("/protocols/{protocol_name}/refresh")
async def refresh_protocol_metrics(protocol_name: str, background_tasks: BackgroundTasks):
    """Refresh metrics for a specific protocol (force cache update)"""
    if protocol_name not in gaming_analytics.supported_protocols:
        raise HTTPException(status_code=404, detail=f"Protocol {protocol_name} not supported")
    
    # Clear cache for this protocol
    cache_key = f"{protocol_name}_{datetime.now().strftime('%Y%m%d_%H')}"
    if cache_key in gaming_analytics.metrics_cache:
        del gaming_analytics.metrics_cache[cache_key]
    
    # Collect fresh metrics in background
    background_tasks.add_task(gaming_analytics.collect_protocol_metrics, protocol_name)
    
    return {
        "message": f"Metrics refresh initiated for {protocol_name}",
        "status": "processing"
    }


@router.post("/refresh-all")
async def refresh_all_metrics(background_tasks: BackgroundTasks):
    """Refresh metrics for all protocols"""
    # Clear all cache
    gaming_analytics.metrics_cache.clear()
    
    # Collect fresh metrics for all protocols in background
    background_tasks.add_task(gaming_analytics.collect_all_protocols_metrics)
    
    return {
        "message": "Metrics refresh initiated for all protocols",
        "status": "processing",
        "protocols": list(gaming_analytics.supported_protocols.keys())
    }


@router.get("/dashboard")
async def get_gaming_analytics_dashboard():
    """Get gaming analytics data formatted for dashboard display"""
    try:
        # Get summary data
        summary = await gaming_analytics.get_protocol_summary()

        # Format for dashboard
        dashboard_data = {
            "overview": {
                "total_protocols": summary["total_protocols"],
                "total_market_cap": summary["total_market_cap"],
                "average_uptime": summary["average_protocol_uptime"],
                "last_updated": summary["last_updated"]
            },
            "protocols": [],
            "market_metrics": {
                "total_market_cap": summary["total_market_cap"],
                "protocols_online": sum(1 for p in summary["protocols"].values() if p.get("protocol_uptime", 0) > 0.5),
                "protocols_total": summary["total_protocols"]
            }
        }

        # Add individual protocol data
        for protocol_name, protocol_data in summary["protocols"].items():
            dashboard_data["protocols"].append({
                "name": protocol_data["name"],
                "protocol_id": protocol_name,
                "token_price": protocol_data.get("token_price", 0),
                "market_cap": protocol_data.get("market_cap", 0),
                "price_change_24h": protocol_data.get("token_price_change_24h", 0),
                "uptime": protocol_data.get("protocol_uptime", 0),
                "status": "online" if protocol_data.get("protocol_uptime", 0) > 0.5 else "degraded"
            })

        return dashboard_data

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Dashboard data error: {str(e)}")


@router.get("/system-status")
async def get_system_status():
    """Get comprehensive system status and performance metrics"""
    try:
        return gaming_analytics.get_system_status()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"System status error: {str(e)}")


@router.get("/health")
async def gaming_analytics_health():
    """Health check for gaming analytics system"""
    try:
        # Basic health check without testing external APIs
        protocols = list(gaming_analytics.supported_protocols.keys())

        return {
            "status": "healthy",
            "supported_protocols": len(protocols),
            "cache_size": len(gaming_analytics.metrics_cache),
            "protocols": protocols,
            "cryptorank_configured": bool(gaming_analytics.gaming_market_data),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


# Dashboard integration endpoint
@router.get("/dashboard-data")
async def get_dashboard_analytics_data():
    """Get gaming analytics data formatted for dashboard consumption"""
    try:
        summary = await gaming_analytics.get_protocol_summary()
        
        # Format data for dashboard charts and widgets
        dashboard_data = {
            "gaming_protocols_overview": {
                "total_protocols": summary["total_protocols"],
                "total_market_cap": summary["total_market_cap"],
                "total_daily_users": summary["total_daily_active_users"],
                "average_uptime": summary["average_protocol_uptime"]
            },
            "protocol_performance": [],
            "market_metrics": {
                "total_volume_24h": summary["total_transaction_volume_24h"],
                "nft_volume_24h": summary["total_nft_volume_24h"]
            },
            "timestamp": summary["last_updated"]
        }
        
        # Add individual protocol data for charts
        for protocol_name, protocol_data in summary["protocols"].items():
            dashboard_data["protocol_performance"].append({
                "name": protocol_data["name"],
                "market_cap": protocol_data["market_cap"],
                "daily_users": protocol_data["daily_active_users"],
                "token_price": protocol_data["token_price"],
                "price_change": protocol_data["token_price_change_24h"],
                "uptime": protocol_data["protocol_uptime"]
            })
        
        # Update dashboard metrics for Prometheus
        if dashboard_metrics:
            dashboard_metrics.gaming_protocols_total.set(summary["total_protocols"])
            dashboard_metrics.gaming_market_cap_total.set(summary["total_market_cap"])
            dashboard_metrics.gaming_users_total.set(summary["total_daily_active_users"])
        
        return dashboard_data

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating dashboard data: {str(e)}")


@router.websocket("/ws")
async def gaming_analytics_websocket(websocket: WebSocket):
    """WebSocket endpoint for real-time gaming analytics updates"""
    connection_id = await manager.connect(websocket)

    try:
        # Send initial gaming analytics snapshot
        await manager.send_gaming_analytics_snapshot(connection_id)

        # Handle incoming messages
        while True:
            try:
                # Wait for messages from client
                data = await websocket.receive_text()
                message = json.loads(data) if data else {}

                message_type = message.get("type")

                if message_type == "subscribe":
                    # Subscribe to specific topics
                    topics = message.get("topics", [])
                    available_topics = manager.get_available_topics()
                    valid_topics = []
                    invalid_topics = []

                    for topic in topics:
                        if topic in available_topics:
                            manager.subscribe(connection_id, topic)
                            valid_topics.append(topic)
                        else:
                            invalid_topics.append(topic)

                    if valid_topics:
                        await manager.send_personal_message({
                            "type": "subscription_confirmed",
                            "topics": valid_topics
                        }, connection_id)

                    if invalid_topics:
                        await manager.send_personal_message({
                            "type": "error",
                            "message": f"Invalid topics: {invalid_topics}",
                            "available_topics": list(available_topics)
                        }, connection_id)

                elif message_type == "unsubscribe":
                    # Unsubscribe from topics
                    topics = message.get("topics", [])
                    for topic in topics:
                        manager.unsubscribe(connection_id, topic)

                    await manager.send_personal_message({
                        "type": "unsubscription_confirmed",
                        "topics": topics
                    }, connection_id)

                elif message_type == "get_protocol_data":
                    # Get specific protocol data
                    protocol_name = message.get("protocol")
                    if protocol_name:
                        try:
                            protocol_data = await gaming_analytics.collect_protocol_metrics(protocol_name)
                            await manager.send_personal_message({
                                "type": "protocol_data",
                                "protocol": protocol_name,
                                "data": protocol_data.__dict__ if protocol_data else None
                            }, connection_id)
                        except Exception as e:
                            await manager.send_personal_message({
                                "type": "error",
                                "message": f"Failed to get protocol data: {str(e)}"
                            }, connection_id)

                elif message_type == "refresh_data":
                    # Force refresh and send updated data
                    await manager.send_gaming_analytics_snapshot(connection_id)

                else:
                    await manager.send_personal_message({
                        "type": "error",
                        "message": f"Unknown message type: {message_type}"
                    }, connection_id)

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Error processing message"
                }, connection_id)

    except WebSocketDisconnect:
        pass
    finally:
        await manager.disconnect(connection_id)
