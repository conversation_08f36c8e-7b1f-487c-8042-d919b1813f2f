"""
Gaming Configuration API
Provides endpoints for managing gaming project configuration
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
import logging
import sys
import os

# Add the config directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.gaming_config import gaming_project_manager, GamingProjectConfig

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/gaming-config", tags=["Gaming Configuration"])


class ProjectToggleRequest(BaseModel):
    """Request model for enabling/disabling projects"""
    project_name: str
    enabled: bool


class ProjectSummaryResponse(BaseModel):
    """Response model for project summary"""
    total_projects: int
    enabled_projects: int
    live_projects: int
    projects_with_tokens: int
    projects_with_nfts: int
    blockchains: List[str]
    genres: List[str]
    total_tokens: int
    total_contracts: int


class ProjectConfigResponse(BaseModel):
    """Response model for project configuration"""
    project_name: str
    website: str
    blockchain: str
    status: str
    enabled: bool
    tokens: List[Dict[str, Any]]
    nfts: List[Dict[str, Any]]
    genre: str
    sub_genre: str
    daily_active_users: Optional[int]
    daily_unique_wallets: Optional[int]


@router.get("/summary", response_model=ProjectSummaryResponse)
async def get_project_summary():
    """Get summary of all gaming projects"""
    try:
        summary = gaming_project_manager.get_project_summary()
        return ProjectSummaryResponse(**summary)
    except Exception as e:
        logger.error(f"Failed to get project summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get project summary")


@router.get("/projects")
async def get_all_projects():
    """Get all gaming projects"""
    try:
        projects = gaming_project_manager.get_all_projects()
        
        # Convert to serializable format
        project_list = []
        for project_key, project in projects.items():
            project_data = {
                "key": project_key,
                "project_name": project.project_name,
                "website": project.website,
                "blockchain": project.blockchain,
                "status": project.status,
                "enabled": project.enabled,
                "genre": project.genre,
                "sub_genre": project.sub_genre,
                "daily_active_users": project.daily_active_users,
                "daily_unique_wallets": project.daily_unique_wallets,
                "tokens": [
                    {
                        "symbol": token.symbol,
                        "contract_address": token.contract_address,
                        "token_type": token.token_type
                    }
                    for token in project.tokens
                ],
                "nfts": [
                    {
                        "contract_address": nft.contract_address,
                        "name": nft.name,
                        "function": nft.function
                    }
                    for nft in project.nfts
                ],
                "social_media": {
                    "twitter": project.twitter_link,
                    "discord": project.discord_link,
                    "telegram": project.telegram_link
                }
            }
            project_list.append(project_data)
        
        return {
            "projects": project_list,
            "total_count": len(project_list)
        }
    except Exception as e:
        logger.error(f"Failed to get projects: {e}")
        raise HTTPException(status_code=500, detail="Failed to get projects")


@router.get("/projects/enabled")
async def get_enabled_projects():
    """Get only enabled gaming projects"""
    try:
        projects = gaming_project_manager.get_enabled_projects()
        
        project_list = []
        for project_key, project in projects.items():
            project_data = {
                "key": project_key,
                "project_name": project.project_name,
                "website": project.website,
                "blockchain": project.blockchain,
                "status": project.status,
                "genre": project.genre,
                "token_symbols": [token.symbol for token in project.tokens],
                "contract_addresses": [token.contract_address for token in project.tokens] + 
                                   [nft.contract_address for nft in project.nfts]
            }
            project_list.append(project_data)
        
        return {
            "projects": project_list,
            "total_count": len(project_list)
        }
    except Exception as e:
        logger.error(f"Failed to get enabled projects: {e}")
        raise HTTPException(status_code=500, detail="Failed to get enabled projects")


@router.get("/projects/live")
async def get_live_projects():
    """Get only live/active gaming projects"""
    try:
        projects = gaming_project_manager.get_live_projects()
        
        project_list = []
        for project_key, project in projects.items():
            project_data = {
                "key": project_key,
                "project_name": project.project_name,
                "website": project.website,
                "blockchain": project.blockchain,
                "status": project.status,
                "genre": project.genre,
                "daily_active_users": project.daily_active_users,
                "daily_unique_wallets": project.daily_unique_wallets,
                "token_symbols": [token.symbol for token in project.tokens]
            }
            project_list.append(project_data)
        
        return {
            "projects": project_list,
            "total_count": len(project_list)
        }
    except Exception as e:
        logger.error(f"Failed to get live projects: {e}")
        raise HTTPException(status_code=500, detail="Failed to get live projects")


@router.get("/projects/{project_name}")
async def get_project_details(project_name: str):
    """Get detailed information about a specific project"""
    try:
        project = gaming_project_manager.get_project(project_name)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        return {
            "project_name": project.project_name,
            "website": project.website,
            "blockchain": project.blockchain,
            "status": project.status,
            "enabled": project.enabled,
            "genre": project.genre,
            "sub_genre": project.sub_genre,
            "game_style": project.game_style,
            "developer": project.developer,
            "platform": project.platform,
            "notes": project.notes,
            "daily_active_users": project.daily_active_users,
            "daily_unique_wallets": project.daily_unique_wallets,
            "dau_source": project.dau_source,
            "uaw_source": project.uaw_source,
            "tokens": [
                {
                    "symbol": token.symbol,
                    "contract_address": token.contract_address,
                    "token_type": token.token_type,
                    "coingecko_link": token.coingecko_link,
                    "blockchain_scanner_link": token.blockchain_scanner_link,
                    "total_supply": token.total_supply
                }
                for token in project.tokens
            ],
            "nfts": [
                {
                    "contract_address": nft.contract_address,
                    "name": nft.name,
                    "function": nft.function,
                    "blockchain_scanner_link": nft.blockchain_scanner_link,
                    "marketplace_link": nft.marketplace_link,
                    "holders_count": nft.holders_count
                }
                for nft in project.nfts
            ],
            "social_media": {
                "twitter": project.twitter_link,
                "discord": project.discord_link,
                "telegram": project.telegram_link,
                "medium": project.medium_link,
                "youtube": project.youtube_link,
                "linkedin": project.linkedin_link,
                "facebook": project.facebook_link,
                "reddit": project.reddit_link
            },
            "documentation": {
                "whitepaper": project.whitepaper_link,
                "token_schedule": project.token_schedule_link
            },
            "auto_clicker": {
                "bot": project.auto_clicker_bot,
                "community": project.auto_clicker_community,
                "membership": project.auto_clicker_membership
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get project details for {project_name}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get project details")


@router.post("/projects/toggle")
async def toggle_project(request: ProjectToggleRequest):
    """Enable or disable a gaming project"""
    try:
        if request.enabled:
            gaming_project_manager.enable_project(request.project_name)
        else:
            gaming_project_manager.disable_project(request.project_name)
        
        return {
            "success": True,
            "message": f"Project {request.project_name} {'enabled' if request.enabled else 'disabled'} successfully"
        }
    except Exception as e:
        logger.error(f"Failed to toggle project {request.project_name}: {e}")
        raise HTTPException(status_code=500, detail="Failed to toggle project")


@router.get("/tokens")
async def get_all_token_symbols():
    """Get all unique token symbols from enabled projects"""
    try:
        tokens = gaming_project_manager.get_all_token_symbols()
        return {
            "tokens": tokens,
            "total_count": len(tokens)
        }
    except Exception as e:
        logger.error(f"Failed to get token symbols: {e}")
        raise HTTPException(status_code=500, detail="Failed to get token symbols")


@router.get("/contracts")
async def get_all_contract_addresses():
    """Get all unique contract addresses from enabled projects"""
    try:
        contracts = gaming_project_manager.get_all_contract_addresses()
        return {
            "contracts": contracts,
            "total_count": len(contracts)
        }
    except Exception as e:
        logger.error(f"Failed to get contract addresses: {e}")
        raise HTTPException(status_code=500, detail="Failed to get contract addresses")


@router.get("/keywords")
async def get_gaming_keywords():
    """Get gaming keywords for social media monitoring"""
    try:
        keywords = gaming_project_manager.get_gaming_keywords()
        return {
            "keywords": keywords,
            "total_count": len(keywords)
        }
    except Exception as e:
        logger.error(f"Failed to get gaming keywords: {e}")
        raise HTTPException(status_code=500, detail="Failed to get gaming keywords")


@router.post("/reload")
async def reload_configuration():
    """Reload gaming configuration from CSV file"""
    try:
        # Reload the gaming project manager
        gaming_project_manager._load_projects()
        
        # Also reload gaming contracts and analytics
        from blockchain.gaming_contracts import gaming_contract_manager
        from blockchain.gaming_analytics import gaming_analytics
        
        gaming_contract_manager.reload_contracts_from_csv()
        gaming_analytics.reload_protocols_from_csv()
        
        return {
            "success": True,
            "message": "Gaming configuration reloaded successfully",
            "summary": gaming_project_manager.get_project_summary()
        }
    except Exception as e:
        logger.error(f"Failed to reload configuration: {e}")
        raise HTTPException(status_code=500, detail="Failed to reload configuration")
