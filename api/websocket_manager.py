"""
WebSocket Manager for Real-Time Data Streaming
Handles WebSocket connections for live dashboard updates and gaming news feeds
"""
import json
import asyncio
from typing import Dict, List, Set, Any, Optional
from datetime import datetime
import logging

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from models.base import get_db
from services.dashboard_analytics import DashboardAnalytics
from monitoring.prometheus_metrics import record_websocket_connection, record_websocket_disconnection
from blockchain.gaming_analytics import gaming_analytics

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections and broadcasts"""
    
    def __init__(self):
        # Active connections by connection ID
        self.active_connections: Dict[str, WebSocket] = {}
        # Subscriptions by topic
        self.subscriptions: Dict[str, Set[str]] = {}
        # Connection metadata
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
    async def connect(self, websocket: WebSocket, connection_id: str, client_info: Dict[str, Any] = None):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        self.connection_metadata[connection_id] = {
            "connected_at": datetime.utcnow(),
            "client_info": client_info or {},
            "subscriptions": set()
        }
        
        # Record metrics
        record_websocket_connection()
        
        logger.info(f"WebSocket connection established: {connection_id}")
        
        # Send welcome message
        await self.send_personal_message({
            "type": "connection_established",
            "connection_id": connection_id,
            "timestamp": datetime.utcnow().isoformat(),
            "available_topics": list(self.get_available_topics())
        }, connection_id)
    
    def disconnect(self, connection_id: str):
        """Remove a WebSocket connection"""
        if connection_id in self.active_connections:
            # Remove from all subscriptions
            for topic in list(self.subscriptions.keys()):
                self.unsubscribe(connection_id, topic)
            
            # Remove connection
            del self.active_connections[connection_id]
            del self.connection_metadata[connection_id]
            
            # Record metrics
            record_websocket_disconnection()
            
            logger.info(f"WebSocket connection closed: {connection_id}")
    
    def subscribe(self, connection_id: str, topic: str):
        """Subscribe a connection to a topic"""
        if topic not in self.subscriptions:
            self.subscriptions[topic] = set()
        
        self.subscriptions[topic].add(connection_id)
        
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]["subscriptions"].add(topic)
        
        logger.info(f"Connection {connection_id} subscribed to {topic}")
    
    def unsubscribe(self, connection_id: str, topic: str):
        """Unsubscribe a connection from a topic"""
        if topic in self.subscriptions:
            self.subscriptions[topic].discard(connection_id)
            
            # Remove empty topic
            if not self.subscriptions[topic]:
                del self.subscriptions[topic]
        
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]["subscriptions"].discard(topic)
        
        logger.info(f"Connection {connection_id} unsubscribed from {topic}")
    
    async def send_personal_message(self, message: Dict[str, Any], connection_id: str):
        """Send a message to a specific connection"""
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {connection_id}: {e}")
                self.disconnect(connection_id)
    
    async def broadcast_to_topic(self, message: Dict[str, Any], topic: str):
        """Broadcast a message to all connections subscribed to a topic"""
        if topic in self.subscriptions:
            message["topic"] = topic
            message["timestamp"] = datetime.utcnow().isoformat()
            
            disconnected_connections = []
            
            for connection_id in self.subscriptions[topic]:
                try:
                    websocket = self.active_connections[connection_id]
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error broadcasting to {connection_id}: {e}")
                    disconnected_connections.append(connection_id)
            
            # Clean up disconnected connections
            for connection_id in disconnected_connections:
                self.disconnect(connection_id)
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """Broadcast a message to all active connections"""
        message["timestamp"] = datetime.utcnow().isoformat()
        
        disconnected_connections = []
        
        for connection_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting to {connection_id}: {e}")
                disconnected_connections.append(connection_id)
        
        # Clean up disconnected connections
        for connection_id in disconnected_connections:
            self.disconnect(connection_id)
    
    def get_connection_count(self) -> int:
        """Get the number of active connections"""
        return len(self.active_connections)
    
    def get_topic_subscribers(self, topic: str) -> int:
        """Get the number of subscribers for a topic"""
        return len(self.subscriptions.get(topic, set()))
    
    def get_available_topics(self) -> Set[str]:
        """Get available subscription topics"""
        base_topics = {
            "news_updates",
            "gaming_analytics",
            "blockchain_events",
            "system_alerts",
            "market_data",
            "market_summary",
            "cross_chain_activity",
            "solana_updates",
            "nft_activity"
        }

        # Add individual protocol topics
        protocol_topics = {
            f"protocol_{protocol}" for protocol in [
                "axie-infinity", "the-sandbox", "decentraland", "splinterlands",
                "gods-unchained", "illuvium", "gala-games", "enjin"
            ]
        }

        return base_topics.union(protocol_topics)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "total_connections": self.get_connection_count(),
            "topics": {
                topic: self.get_topic_subscribers(topic)
                for topic in self.subscriptions.keys()
            },
            "available_topics": list(self.get_available_topics())
        }

    async def broadcast_gaming_analytics_update(self, data: Dict[str, Any]):
        """Broadcast gaming analytics updates to subscribed clients"""
        message = {
            "type": "gaming_analytics_update",
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_to_topic("gaming_analytics", message)

    async def broadcast_protocol_update(self, protocol_name: str, data: Dict[str, Any]):
        """Broadcast individual protocol updates"""
        message = {
            "type": "protocol_update",
            "protocol": protocol_name,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_to_topic(f"protocol_{protocol_name}", message)
        await self.broadcast_to_topic("gaming_analytics", message)

    async def broadcast_market_summary(self, summary: Dict[str, Any]):
        """Broadcast market summary updates"""
        message = {
            "type": "market_summary",
            "data": summary,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_to_topic("market_summary", message)
        await self.broadcast_to_topic("gaming_analytics", message)

    async def send_gaming_analytics_snapshot(self, connection_id: str):
        """Send current gaming analytics snapshot to a specific client"""
        try:
            # Get current dashboard data
            dashboard_data = await gaming_analytics.get_dashboard_summary()

            message = {
                "type": "gaming_analytics_snapshot",
                "data": dashboard_data,
                "timestamp": datetime.now().isoformat()
            }

            await self.send_personal_message(message, connection_id)

        except Exception as e:
            logger.error(f"Failed to send gaming analytics snapshot: {e}")
            await self.send_personal_message({
                "type": "error",
                "message": "Failed to load gaming analytics data"
            }, connection_id)


# Global connection manager instance
manager = ConnectionManager()


class RealTimeDataStreamer:
    """Handles real-time data streaming to WebSocket clients"""
    
    def __init__(self, db: Session):
        self.db = db
        self.analytics = DashboardAnalytics(db)
        self.is_running = False
    
    async def start_streaming(self):
        """Start the real-time data streaming loop"""
        self.is_running = True
        
        # Start background tasks
        await asyncio.gather(
            self.stream_news_updates(),
            self.stream_gaming_analytics(),
            self.stream_system_metrics(),
            self.stream_blockchain_events(),
            return_exceptions=True
        )
    
    async def stop_streaming(self):
        """Stop the real-time data streaming"""
        self.is_running = False
    
    async def stream_news_updates(self):
        """Stream live news updates"""
        while self.is_running:
            try:
                # Get latest articles
                latest_articles = await self.analytics.get_latest_articles(limit=5)
                
                if latest_articles:
                    await manager.broadcast_to_topic({
                        "type": "news_update",
                        "data": latest_articles
                    }, "news_updates")
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in news updates stream: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def stream_gaming_analytics(self):
        """Stream gaming analytics updates"""
        while self.is_running:
            try:
                # Get gaming analytics data
                analytics_data = {
                    "activity_score": await self.analytics.calculate_activity_score(),
                    "top_projects": await self.analytics.get_top_mentioned_projects(limit=5),
                    "network_activity": await self.analytics.get_articles_by_network()
                }
                
                await manager.broadcast_to_topic({
                    "type": "gaming_analytics_update",
                    "data": analytics_data
                }, "gaming_analytics")
                
                await asyncio.sleep(60)  # Update every minute
                
            except Exception as e:
                logger.error(f"Error in gaming analytics stream: {e}")
                await asyncio.sleep(120)
    
    async def stream_system_metrics(self):
        """Stream system health metrics"""
        while self.is_running:
            try:
                system_status = await self.analytics.get_system_status()
                performance_metrics = await self.analytics.get_performance_metrics()
                
                await manager.broadcast_to_topic({
                    "type": "system_metrics_update",
                    "data": {
                        "status": system_status,
                        "performance": performance_metrics,
                        "connections": manager.get_connection_stats()
                    }
                }, "system_alerts")
                
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in system metrics stream: {e}")
                await asyncio.sleep(60)
    
    async def stream_blockchain_events(self):
        """Stream blockchain events and cross-chain activity"""
        while self.is_running:
            try:
                # Get cross-chain activity
                cross_chain_data = await self.analytics.get_network_comparison(days=1)
                
                await manager.broadcast_to_topic({
                    "type": "blockchain_events_update",
                    "data": cross_chain_data
                }, "blockchain_events")
                
                # Get Solana-specific updates
                solana_data = await self.analytics.get_solana_analytics()
                
                await manager.broadcast_to_topic({
                    "type": "solana_update",
                    "data": solana_data
                }, "solana_updates")
                
                await asyncio.sleep(120)  # Update every 2 minutes
                
            except Exception as e:
                logger.error(f"Error in blockchain events stream: {e}")
                await asyncio.sleep(180)


async def handle_websocket_message(websocket: WebSocket, connection_id: str, message: Dict[str, Any]):
    """Handle incoming WebSocket messages"""
    try:
        message_type = message.get("type")
        
        if message_type == "subscribe":
            topic = message.get("topic")
            if topic in manager.get_available_topics():
                manager.subscribe(connection_id, topic)
                await manager.send_personal_message({
                    "type": "subscription_confirmed",
                    "topic": topic
                }, connection_id)
            else:
                await manager.send_personal_message({
                    "type": "error",
                    "message": f"Invalid topic: {topic}",
                    "available_topics": list(manager.get_available_topics())
                }, connection_id)
        
        elif message_type == "unsubscribe":
            topic = message.get("topic")
            manager.unsubscribe(connection_id, topic)
            await manager.send_personal_message({
                "type": "unsubscription_confirmed",
                "topic": topic
            }, connection_id)
        
        elif message_type == "ping":
            await manager.send_personal_message({
                "type": "pong",
                "timestamp": datetime.utcnow().isoformat()
            }, connection_id)
        
        elif message_type == "get_stats":
            stats = manager.get_connection_stats()
            await manager.send_personal_message({
                "type": "stats",
                "data": stats
            }, connection_id)
        
        else:
            await manager.send_personal_message({
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            }, connection_id)
    
    except Exception as e:
        logger.error(f"Error handling WebSocket message: {e}")
        await manager.send_personal_message({
            "type": "error",
            "message": "Error processing message"
        }, connection_id)
