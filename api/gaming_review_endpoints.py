"""
Gaming Project Review API Endpoints
Handles pending gaming projects that need admin review
"""
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel

from models.base import get_db
from models.gaming import GamingProject
from services.gaming_project_creator import gaming_project_creator

router = APIRouter(prefix="/api/v1/gaming-review", tags=["Gaming Review"])


class PendingProjectResponse(BaseModel):
    """Response model for pending projects"""
    project_id: int
    project_name: str
    contract_address: str
    blockchain: str
    confidence: str
    detected_patterns: List[str]
    created_at: str
    needs_review: bool
    admin_notes: Optional[str] = None


class ProjectApprovalRequest(BaseModel):
    """Request model for project approval/rejection"""
    project_id: int
    action: str  # 'approve' or 'reject'
    admin_notes: Optional[str] = None


class ProjectReviewStats(BaseModel):
    """Statistics for project review"""
    total_pending: int
    high_confidence: int
    medium_confidence: int
    needs_attention: int


@router.get("/pending", response_model=List[PendingProjectResponse])
async def get_pending_projects(
    db: Session = Depends(get_db)
):
    """Get all projects pending admin review"""
    try:
        # Get pending projects from gaming project creator
        pending_projects = await gaming_project_creator.get_pending_projects()
        
        response_data = []
        
        for project_id, project_info in pending_projects.items():
            # Get the actual project from database
            project = db.query(GamingProject).filter(GamingProject.id == project_id).first()
            
            if project:
                response_data.append(PendingProjectResponse(
                    project_id=project_id,
                    project_name=project.project_name,
                    contract_address=project_info['contract_address'],
                    blockchain=project_info['blockchain'],
                    confidence=project_info['confidence'],
                    detected_patterns=project_info['detected_patterns'],
                    created_at=project_info['created_at'],
                    needs_review=project_info['needs_review'],
                    admin_notes=project.admin_notes
                ))
        
        return response_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching pending projects: {str(e)}")


@router.get("/stats", response_model=ProjectReviewStats)
async def get_review_stats(
    db: Session = Depends(get_db)
):
    """Get statistics for project review"""
    try:
        pending_projects = await gaming_project_creator.get_pending_projects()
        
        total_pending = len(pending_projects)
        high_confidence = sum(1 for p in pending_projects.values() if p['confidence'] == 'HIGH')
        medium_confidence = sum(1 for p in pending_projects.values() if p['confidence'] == 'MEDIUM')
        
        # Count projects that need attention (have admin notes)
        needs_attention = 0
        for project_id in pending_projects.keys():
            project = db.query(GamingProject).filter(GamingProject.id == project_id).first()
            if project and project.admin_notes and "ATTN: Admin" in project.admin_notes:
                needs_attention += 1
        
        return ProjectReviewStats(
            total_pending=total_pending,
            high_confidence=high_confidence,
            medium_confidence=medium_confidence,
            needs_attention=needs_attention
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching review stats: {str(e)}")


@router.post("/approve/{project_id}")
async def approve_project(
    project_id: int,
    admin_notes: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Approve a pending gaming project"""
    try:
        # Get the project
        project = db.query(GamingProject).filter(GamingProject.id == project_id).first()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Update admin notes if provided
        if admin_notes:
            current_notes = project.admin_notes or ""
            project.admin_notes = f"{current_notes}; APPROVED: {admin_notes}".strip("; ")
        else:
            project.admin_notes = f"{project.admin_notes or ''}; APPROVED by admin".strip("; ")
        
        project.updated_at = datetime.utcnow()
        db.commit()
        
        # Remove from pending review
        await gaming_project_creator.approve_project(project_id, db)
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Project '{project.project_name}' approved successfully",
                "project_id": project_id
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error approving project: {str(e)}")


@router.post("/reject/{project_id}")
async def reject_project(
    project_id: int,
    admin_notes: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Reject a pending gaming project"""
    try:
        # Get the project
        project = db.query(GamingProject).filter(GamingProject.id == project_id).first()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Update admin notes if provided
        if admin_notes:
            current_notes = project.admin_notes or ""
            project.admin_notes = f"{current_notes}; REJECTED: {admin_notes}".strip("; ")
        
        # Reject the project (marks as inactive)
        await gaming_project_creator.reject_project(project_id, db)
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Project '{project.project_name}' rejected and marked inactive",
                "project_id": project_id
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error rejecting project: {str(e)}")


@router.post("/batch-action")
async def batch_project_action(
    actions: List[ProjectApprovalRequest],
    db: Session = Depends(get_db)
):
    """Perform batch approve/reject actions on multiple projects"""
    try:
        results = []
        
        for action_request in actions:
            try:
                if action_request.action == "approve":
                    await approve_project(action_request.project_id, action_request.admin_notes, db)
                    results.append({
                        "project_id": action_request.project_id,
                        "action": "approve",
                        "success": True
                    })
                elif action_request.action == "reject":
                    await reject_project(action_request.project_id, action_request.admin_notes, db)
                    results.append({
                        "project_id": action_request.project_id,
                        "action": "reject", 
                        "success": True
                    })
                else:
                    results.append({
                        "project_id": action_request.project_id,
                        "action": action_request.action,
                        "success": False,
                        "error": "Invalid action. Use 'approve' or 'reject'"
                    })
                    
            except Exception as e:
                results.append({
                    "project_id": action_request.project_id,
                    "action": action_request.action,
                    "success": False,
                    "error": str(e)
                })
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Processed {len(actions)} actions",
                "results": results
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing batch actions: {str(e)}")


@router.get("/project/{project_id}")
async def get_project_details(
    project_id: int,
    db: Session = Depends(get_db)
):
    """Get detailed information about a specific project"""
    try:
        project = db.query(GamingProject).filter(GamingProject.id == project_id).first()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get pending project info if available
        pending_projects = await gaming_project_creator.get_pending_projects()
        pending_info = pending_projects.get(project_id, {})
        
        return {
            "project": {
                "id": project.id,
                "project_name": project.project_name,
                "blockchain": project.blockchain,
                "token_1_symbol": project.token_1_symbol,
                "token_1_contract_address": project.token_1_contract_address,
                "validated_game_status": project.validated_game_status,
                "primary_genre": project.primary_genre,
                "subcategory": project.subcategory,
                "is_active": project.is_active,
                "admin_notes": project.admin_notes,
                "game_status_notes": project.game_status_notes,
                "additional_notes": project.additional_notes,
                "created_at": project.created_at.isoformat() if project.created_at else None,
                "updated_at": project.updated_at.isoformat() if project.updated_at else None
            },
            "pending_info": pending_info,
            "is_pending_review": project_id in pending_projects
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching project details: {str(e)}")
