"""
Contract Vetting API Endpoints
Provides REST API access to gaming contract vetting functionality
"""
from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from pydantic import BaseModel

from services.contract_vetting_service import contract_vetting_service, VettingResult, ContractVettingReport

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/contract-vetting", tags=["contract-vetting"])


# Pydantic models for API
class VettingRequest(BaseModel):
    contract_address: str
    blockchain: str


class VettingResponse(BaseModel):
    contract_address: str
    blockchain: str
    result: str
    confidence_score: float
    gaming_patterns: List[str]
    blacklist_patterns: List[str]
    token_standards: List[str]
    social_links: List[str]
    analysis_timestamp: str
    reviewer_notes: Optional[str] = None


class VettingStatsResponse(BaseModel):
    total_contracts_vetted: int
    approved_contracts: int
    rejected_contracts: int
    pending_review: int
    insufficient_data: int
    average_confidence_score: float


@router.post("/vet-contract", response_model=VettingResponse)
async def vet_contract(request: VettingRequest):
    """
    Vet a gaming contract address using comprehensive analysis
    Implements vetting process from blockchainScraperDraft.md lines 17-32
    """
    try:
        logger.info(f"Vetting contract {request.contract_address} on {request.blockchain}")
        
        # Perform comprehensive vetting
        vetting_report = await contract_vetting_service.vet_contract(
            request.contract_address, 
            request.blockchain
        )
        
        return VettingResponse(
            contract_address=vetting_report.contract_address,
            blockchain=vetting_report.blockchain,
            result=vetting_report.result.value,
            confidence_score=vetting_report.confidence_score,
            gaming_patterns=vetting_report.gaming_patterns,
            blacklist_patterns=vetting_report.blacklist_patterns,
            token_standards=vetting_report.token_standards,
            social_links=vetting_report.social_links,
            analysis_timestamp=vetting_report.analysis_timestamp.isoformat(),
            reviewer_notes=vetting_report.reviewer_notes
        )
        
    except Exception as e:
        logger.error(f"Error vetting contract {request.contract_address}: {e}")
        raise HTTPException(status_code=500, detail=f"Error vetting contract: {str(e)}")


@router.get("/contract/{contract_address}")
async def get_vetting_report(
    contract_address: str,
    blockchain: str = Query(..., description="Blockchain network")
):
    """Get detailed vetting report for a specific contract"""
    try:
        # In production, this would retrieve from database
        # For now, perform fresh analysis
        vetting_report = await contract_vetting_service.vet_contract(contract_address, blockchain)
        
        return {
            "contract_address": vetting_report.contract_address,
            "blockchain": vetting_report.blockchain,
            "result": vetting_report.result.value,
            "confidence_score": vetting_report.confidence_score,
            "detailed_analysis": {
                "function_analysis": vetting_report.function_analysis,
                "event_analysis": vetting_report.event_analysis,
                "metadata_analysis": vetting_report.metadata_analysis,
                "behavioral_analysis": vetting_report.behavioral_analysis
            },
            "patterns": {
                "gaming_patterns": vetting_report.gaming_patterns,
                "blacklist_patterns": vetting_report.blacklist_patterns
            },
            "token_standards": vetting_report.token_standards,
            "social_links": vetting_report.social_links,
            "transaction_patterns": vetting_report.transaction_patterns,
            "analysis_timestamp": vetting_report.analysis_timestamp.isoformat(),
            "reviewer_notes": vetting_report.reviewer_notes,
            "manual_override": vetting_report.manual_override
        }
        
    except Exception as e:
        logger.error(f"Error getting vetting report for {contract_address}: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving vetting report: {str(e)}")


@router.post("/batch-vet")
async def batch_vet_contracts(
    background_tasks: BackgroundTasks,
    contracts: List[VettingRequest]
):
    """Vet multiple contracts in batch"""
    try:
        if len(contracts) > 50:
            raise HTTPException(status_code=400, detail="Maximum 50 contracts per batch")
        
        results = []
        for contract_request in contracts:
            try:
                vetting_report = await contract_vetting_service.vet_contract(
                    contract_request.contract_address,
                    contract_request.blockchain
                )
                
                results.append({
                    "contract_address": vetting_report.contract_address,
                    "blockchain": vetting_report.blockchain,
                    "result": vetting_report.result.value,
                    "confidence_score": vetting_report.confidence_score,
                    "gaming_patterns_count": len(vetting_report.gaming_patterns),
                    "blacklist_patterns_count": len(vetting_report.blacklist_patterns)
                })
                
            except Exception as e:
                logger.error(f"Error vetting {contract_request.contract_address}: {e}")
                results.append({
                    "contract_address": contract_request.contract_address,
                    "blockchain": contract_request.blockchain,
                    "result": "error",
                    "error": str(e)
                })
        
        return {
            "total_contracts": len(contracts),
            "successful_vettings": len([r for r in results if "error" not in r]),
            "failed_vettings": len([r for r in results if "error" in r]),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error in batch vetting: {e}")
        raise HTTPException(status_code=500, detail=f"Error in batch vetting: {str(e)}")


@router.get("/stats", response_model=VettingStatsResponse)
async def get_vetting_stats():
    """Get statistics about contract vetting operations"""
    try:
        # In production, this would query database for actual stats
        # For now, return example stats
        return VettingStatsResponse(
            total_contracts_vetted=0,
            approved_contracts=0,
            rejected_contracts=0,
            pending_review=0,
            insufficient_data=0,
            average_confidence_score=0.0
        )
        
    except Exception as e:
        logger.error(f"Error getting vetting stats: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving vetting stats: {str(e)}")


@router.get("/patterns")
async def get_vetting_patterns():
    """Get information about gaming and blacklist patterns used in vetting"""
    try:
        return {
            "gaming_function_patterns": contract_vetting_service.gaming_function_patterns,
            "gaming_event_patterns": contract_vetting_service.gaming_event_patterns,
            "gaming_keywords": contract_vetting_service.gaming_keywords,
            "blacklist_patterns": contract_vetting_service.blacklist_patterns,
            "supported_token_standards": ["ERC-20", "ERC-721", "ERC-1155"],
            "vetting_criteria": {
                "minimum_confidence_for_approval": 0.7,
                "minimum_gaming_patterns_for_approval": 3,
                "maximum_blacklist_patterns_for_approval": 3,
                "confidence_threshold_for_review": 0.4
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting vetting patterns: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving vetting patterns: {str(e)}")


@router.post("/update-patterns")
async def update_vetting_patterns(
    gaming_patterns: Optional[List[str]] = None,
    blacklist_patterns: Optional[List[str]] = None,
    gaming_keywords: Optional[List[str]] = None
):
    """Update vetting patterns (admin only - would require authentication in production)"""
    try:
        updated = []
        
        if gaming_patterns:
            contract_vetting_service.gaming_function_patterns.extend(gaming_patterns)
            updated.append("gaming_patterns")
        
        if blacklist_patterns:
            contract_vetting_service.blacklist_patterns.extend(blacklist_patterns)
            updated.append("blacklist_patterns")
        
        if gaming_keywords:
            contract_vetting_service.gaming_keywords.extend(gaming_keywords)
            updated.append("gaming_keywords")
        
        return {
            "success": True,
            "message": f"Updated patterns: {', '.join(updated)}",
            "updated_categories": updated
        }
        
    except Exception as e:
        logger.error(f"Error updating vetting patterns: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating vetting patterns: {str(e)}")


@router.get("/health")
async def vetting_service_health():
    """Health check for contract vetting service"""
    try:
        # Test basic functionality
        test_result = await contract_vetting_service.vet_contract(
            "******************************************",
            "ethereum"
        )
        
        return {
            "status": "healthy",
            "service": "contract_vetting_service",
            "timestamp": datetime.utcnow().isoformat(),
            "test_vetting_completed": True,
            "supported_blockchains": ["ethereum", "polygon", "bsc", "solana", "arbitrum", "optimism", "base", "avalanche"],
            "vetting_criteria_loaded": True
        }
        
    except Exception as e:
        logger.error(f"Contract vetting service health check failed: {e}")
        return {
            "status": "unhealthy",
            "service": "contract_vetting_service",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }
