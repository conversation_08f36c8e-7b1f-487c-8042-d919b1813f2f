version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: web3-gaming-postgres
    environment:
      POSTGRES_DB: web3_gaming_news
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - web3-gaming-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d web3_gaming_news"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: web3-gaming-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - web3-gaming-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    container_name: web3-gaming-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - web3-gaming-network
    depends_on:
      - web3-gaming-api
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: web3-gaming-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
    networks:
      - web3-gaming-network
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web3 Gaming News Tracker API
  web3-gaming-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: web3-gaming-api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=****************************************/web3_gaming_news
      - REDIS_URL=redis://redis:6379/0
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_DEBUG=false
    volumes:
      - .:/app
    networks:
      - web3-gaming-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Browser-based Dashboard (React Frontend)
  dashboard-frontend:
    build:
      context: ./dashboard/frontend
      dockerfile: Dockerfile
    container_name: web3-gaming-dashboard
    ports:
      - "3001:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    networks:
      - web3-gaming-network
    depends_on:
      - web3-gaming-api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker for Background Tasks
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: web3-gaming-celery
    command: celery -A tasks.celery worker --loglevel=info
    environment:
      - DATABASE_URL=****************************************/web3_gaming_news
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - .:/app
    networks:
      - web3-gaming-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Celery Beat for Scheduled Tasks
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: web3-gaming-celery-beat
    command: celery -A tasks.celery beat --loglevel=info
    environment:
      - DATABASE_URL=****************************************/web3_gaming_news
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - .:/app
    networks:
      - web3-gaming-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: web3-gaming-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - web3-gaming-network
    depends_on:
      - web3-gaming-api
      - dashboard-frontend
      - grafana
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  web3-gaming-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
