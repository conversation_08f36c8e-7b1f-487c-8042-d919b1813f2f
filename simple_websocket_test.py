#!/usr/bin/env python3
"""
Simple WebSocket test client
"""
import asyncio
import websockets
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def simple_test():
    """Simple WebSocket connection test"""
    uri = "ws://localhost:8001/ws"
    
    try:
        logger.info(f"🔌 Connecting to {uri}")
        
        # Use a very simple connection
        websocket = await websockets.connect(uri)
        logger.info("✅ Connected successfully!")
        
        # Wait for welcome message
        try:
            message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            logger.info(f"📨 Received: {message}")
            
            # Parse and display the message
            data = json.loads(message)
            if data.get("type") == "connection_established":
                logger.info(f"🎯 Connection ID: {data.get('connection_id')}")
                logger.info(f"📋 Available topics: {len(data.get('available_topics', []))}")
                
                # Try to subscribe to a topic
                subscribe_msg = {
                    "type": "subscribe",
                    "topic": "gaming_analytics"
                }
                await websocket.send(json.dumps(subscribe_msg))
                logger.info("📡 Sent subscription request")
                
                # Wait for subscription confirmation
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"📨 Subscription response: {response}")
                
        except asyncio.TimeoutError:
            logger.error("⏰ Timeout waiting for message")
        except Exception as e:
            logger.error(f"❌ Error receiving message: {e}")
        
        await websocket.close()
        logger.info("🔌 Connection closed")
        
    except Exception as e:
        logger.error(f"❌ Connection failed: {e}")

if __name__ == "__main__":
    asyncio.run(simple_test())
