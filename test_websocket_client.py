#!/usr/bin/env python3
"""
Test WebSocket client for gaming analytics real-time updates
"""
import asyncio
import websockets
import json
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_websocket():
    """Test WebSocket connection and real-time updates"""
    uri = "ws://localhost:8001/ws"

    try:
        # Add connection options
        async with websockets.connect(
            uri,
            ping_interval=20,
            ping_timeout=10
        ) as websocket:
            logger.info("🔌 Connected to WebSocket")
            
            # Subscribe to gaming analytics updates (using single topic format for main endpoint)
            subscribe_message1 = {
                "type": "subscribe",
                "topic": "gaming_analytics"
            }
            subscribe_message2 = {
                "type": "subscribe",
                "topic": "market_summary"
            }
            await websocket.send(json.dumps(subscribe_message1))
            await websocket.send(json.dumps(subscribe_message2))
            logger.info("📡 Subscribed to gaming analytics and market summary")
            
            # Listen for messages for 30 seconds
            timeout = 30
            start_time = asyncio.get_event_loop().time()
            
            while True:
                try:
                    # Wait for message with timeout
                    remaining_time = timeout - (asyncio.get_event_loop().time() - start_time)
                    if remaining_time <= 0:
                        logger.info("⏰ Test timeout reached")
                        break
                    
                    message = await asyncio.wait_for(
                        websocket.recv(), 
                        timeout=min(5, remaining_time)
                    )
                    
                    data = json.loads(message)
                    message_type = data.get("type", "unknown")
                    
                    if message_type == "gaming_analytics_snapshot":
                        logger.info("📊 Received gaming analytics snapshot")
                        protocols = data.get("data", {}).get("protocols", {})
                        logger.info(f"   - {len(protocols)} protocols")
                        total_market_cap = data.get("data", {}).get("total_market_cap", 0)
                        logger.info(f"   - Total market cap: ${total_market_cap:,.0f}")
                        
                    elif message_type == "gaming_analytics_update":
                        logger.info("🔄 Received gaming analytics update")
                        
                    elif message_type == "protocol_update":
                        protocol = data.get("protocol", "unknown")
                        logger.info(f"🎮 Received protocol update for {protocol}")
                        
                    elif message_type == "subscription_confirmed":
                        topics = data.get("topics", [])
                        logger.info(f"✅ Subscription confirmed for: {', '.join(topics)}")

                    elif message_type == "error":
                        error_msg = data.get("message", "Unknown error")
                        logger.error(f"❌ WebSocket error: {error_msg}")

                    else:
                        logger.info(f"📨 Received message: {message_type}")
                        logger.debug(f"   Full data: {data}")
                        
                except asyncio.TimeoutError:
                    logger.info("⏳ No message received in last 5 seconds")
                    continue
                except Exception as e:
                    logger.error(f"❌ Error receiving message: {e}")
                    break
            
            # Test requesting specific protocol data
            logger.info("🔍 Requesting specific protocol data...")
            protocol_request = {
                "type": "get_protocol_data",
                "protocol": "axie-infinity"
            }
            await websocket.send(json.dumps(protocol_request))
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                data = json.loads(response)
                if data.get("type") == "protocol_data":
                    logger.info("✅ Received protocol data response")
                    protocol_data = data.get("data", {})
                    if protocol_data:
                        logger.info(f"   - Token price: ${protocol_data.get('token_price', 'N/A')}")
                        logger.info(f"   - Market cap: ${protocol_data.get('market_cap', 'N/A'):,}")
                else:
                    logger.info(f"📨 Received: {data.get('type', 'unknown')}")
            except asyncio.TimeoutError:
                logger.warning("⚠️ No response to protocol data request")
            
            logger.info("🏁 WebSocket test completed")
            
    except Exception as e:
        logger.error(f"❌ WebSocket connection failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket())
