#!/usr/bin/env python3
"""
Management CLI for Web3 Gaming News Tracker
"""
import click
import asyncio
import uvicorn
import subprocess
import sys
from sqlalchemy import text
from models.base import engine, create_tables, drop_tables, SessionLocal
from config.settings import get_settings
# Import all models to register them with SQLAlchemy
import models.gaming

settings = get_settings()


@click.group()
def cli():
    """Web3 Gaming News Tracker Management CLI"""
    pass


@cli.group()
def db():
    """Database management commands"""
    pass


@db.command()
def init():
    """Initialize the database"""
    click.echo("Initializing database...")
    try:
        create_tables()
        click.echo("✅ Database initialized successfully!")
    except Exception as e:
        click.echo(f"❌ Error initializing database: {e}")
        raise


@db.command()
def reset():
    """Reset the database (WARNING: This will delete all data)"""
    if click.confirm("This will delete all data. Are you sure?"):
        click.echo("Resetting database...")
        try:
            drop_tables()
            create_tables()
            click.echo("✅ Database reset successfully!")
        except Exception as e:
            click.echo(f"❌ Error resetting database: {e}")
            raise


@db.command()
def test():
    """Test database connection"""
    click.echo("Testing database connection...")
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone()[0] == 1:
                click.echo("✅ Database connection successful!")
            else:
                click.echo("❌ Database connection failed!")
    except Exception as e:
        click.echo(f"❌ Database connection error: {e}")
        raise


@db.command()
def migrate():
    """Create a new migration"""
    message = click.prompt("Migration message", default="Auto migration")
    click.echo(f"Creating migration: {message}")
    try:
        result = subprocess.run([
            sys.executable, "-m", "alembic", "revision",
            "--autogenerate", "-m", message
        ], capture_output=True, text=True)

        if result.returncode == 0:
            click.echo("✅ Migration created successfully!")
            click.echo(result.stdout)
        else:
            click.echo("❌ Migration creation failed!")
            click.echo(result.stderr)
            raise click.ClickException("Migration failed")
    except Exception as e:
        click.echo(f"❌ Error creating migration: {e}")
        raise


@db.command()
def upgrade():
    """Apply migrations to database"""
    revision = click.prompt("Revision (default: head)", default="head")
    click.echo(f"Upgrading database to: {revision}")
    try:
        result = subprocess.run([
            sys.executable, "-m", "alembic", "upgrade", revision
        ], capture_output=True, text=True)

        if result.returncode == 0:
            click.echo("✅ Database upgraded successfully!")
            click.echo(result.stdout)
        else:
            click.echo("❌ Database upgrade failed!")
            click.echo(result.stderr)
            raise click.ClickException("Upgrade failed")
    except Exception as e:
        click.echo(f"❌ Error upgrading database: {e}")
        raise


@db.command()
def downgrade():
    """Downgrade database to previous migration"""
    revision = click.prompt("Revision (default: -1)", default="-1")
    if click.confirm(f"This will downgrade database to {revision}. Continue?"):
        click.echo(f"Downgrading database to: {revision}")
        try:
            result = subprocess.run([
                sys.executable, "-m", "alembic", "downgrade", revision
            ], capture_output=True, text=True)

            if result.returncode == 0:
                click.echo("✅ Database downgraded successfully!")
                click.echo(result.stdout)
            else:
                click.echo("❌ Database downgrade failed!")
                click.echo(result.stderr)
                raise click.ClickException("Downgrade failed")
        except Exception as e:
            click.echo(f"❌ Error downgrading database: {e}")
            raise


@db.command()
def history():
    """Show migration history"""
    click.echo("Migration history:")
    try:
        result = subprocess.run([
            sys.executable, "-m", "alembic", "history", "--verbose"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            click.echo(result.stdout)
        else:
            click.echo("❌ Failed to get migration history!")
            click.echo(result.stderr)
    except Exception as e:
        click.echo(f"❌ Error getting migration history: {e}")


@db.command()
def current():
    """Show current migration revision"""
    click.echo("Current migration revision:")
    try:
        result = subprocess.run([
            sys.executable, "-m", "alembic", "current", "--verbose"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            click.echo(result.stdout)
        else:
            click.echo("❌ Failed to get current revision!")
            click.echo(result.stderr)
    except Exception as e:
        click.echo(f"❌ Error getting current revision: {e}")


@db.command()
def backup():
    """Create database backup"""
    backup_name = click.prompt("Backup name (optional)", default="", show_default=False)
    click.echo("Creating database backup...")
    try:
        from scripts.database_backup import DatabaseBackup
        backup_manager = DatabaseBackup()
        backup_path = backup_manager.create_backup(backup_name if backup_name else None)
        click.echo(f"✅ Backup created: {backup_path}")
    except Exception as e:
        click.echo(f"❌ Error creating backup: {e}")
        raise


@db.command()
def restore():
    """Restore database from backup"""
    from scripts.database_backup import DatabaseBackup
    backup_manager = DatabaseBackup()

    # List available backups
    backups = backup_manager.list_backups()
    if not backups:
        click.echo("❌ No backups found")
        return

    click.echo("📋 Available backups:")
    for i, backup in enumerate(backups):
        click.echo(f"  {i+1}. {backup['name']} - {backup['size']} - {backup['created']}")

    choice = click.prompt("Select backup number", type=int)
    if 1 <= choice <= len(backups):
        backup_path = backups[choice-1]['path']
        if click.confirm(f"Restore from {backup_path}? This will overwrite current data."):
            try:
                backup_manager.restore_backup(backup_path)
                click.echo("✅ Database restored successfully")
            except Exception as e:
                click.echo(f"❌ Error restoring backup: {e}")
                raise
    else:
        click.echo("❌ Invalid selection")


@db.command()
def stats():
    """Show database statistics"""
    click.echo("📊 Database Statistics:")
    try:
        from scripts.database_backup import DatabaseBackup
        backup_manager = DatabaseBackup()
        stats = backup_manager.get_database_stats()

        for table, count in stats.items():
            click.echo(f"  {table}: {count}")
    except Exception as e:
        click.echo(f"❌ Error getting database stats: {e}")
        raise


@cli.group()
def blockchain():
    """Blockchain management commands"""
    pass


@blockchain.command()
def test_rpc():
    """Test RPC connections"""
    from blockchain.rpc import test_all_connections
    
    click.echo("Testing RPC connections...")
    try:
        results = asyncio.run(test_all_connections())
        for chain, status in results.items():
            if status:
                click.echo(f"✅ {chain}: Connected")
            else:
                click.echo(f"❌ {chain}: Failed")
    except Exception as e:
        click.echo(f"❌ Error testing RPC connections: {e}")
        raise


@blockchain.command()
def sync_chains():
    """Sync blockchain data"""
    from scrapers.blockchain.sync import sync_all_chains

    click.echo("Starting blockchain sync...")
    try:
        asyncio.run(sync_all_chains())
        click.echo("✅ Blockchain sync completed!")
    except Exception as e:
        click.echo(f"❌ Error syncing blockchain data: {e}")
        raise


@blockchain.command()
@click.option('--chains', default='ethereum,polygon,bsc', help='Comma-separated list of chains to scrape')
@click.option('--mode', default='hybrid', type=click.Choice(['historical', 'real_time', 'hybrid']), help='Scraping mode')
@click.option('--blocks-back', default=1000, help='Number of historical blocks to scrape')
def start_scraper(chains, mode, blocks_back):
    """Start the blockchain scraper"""
    from scrapers.blockchain import blockchain_scraper_manager, ScrapingConfig, ScrapingMode

    async def run_scraper():
        try:
            # Configure scraper
            chain_list = [chain.strip() for chain in chains.split(',')]
            scraping_mode = ScrapingMode(mode)

            config = ScrapingConfig(
                chains=chain_list,
                mode=scraping_mode,
                historical_blocks_back=blocks_back,
                enable_ml_classification=True,
                enable_real_time_monitoring=True
            )

            blockchain_scraper_manager.config = config

            click.echo(f"Starting blockchain scraper...")
            click.echo(f"Chains: {chain_list}")
            click.echo(f"Mode: {mode}")
            click.echo(f"Historical blocks back: {blocks_back}")

            # Start scraping
            await blockchain_scraper_manager.start_scraping()

        except KeyboardInterrupt:
            click.echo("\nStopping blockchain scraper...")
            await blockchain_scraper_manager.stop_scraping()
        except Exception as e:
            click.echo(f"❌ Error in blockchain scraper: {e}")

    asyncio.run(run_scraper())


@blockchain.command()
@click.argument('contract_address')
@click.argument('blockchain')
def analyze_contract(contract_address, blockchain):
    """Analyze a specific contract for gaming patterns"""
    from scrapers.blockchain import blockchain_scraper_manager

    async def run_analysis():
        try:
            click.echo(f"Analyzing contract {contract_address} on {blockchain}...")

            result = await blockchain_scraper_manager.analyze_specific_contract(
                contract_address, blockchain
            )

            if 'error' in result:
                click.echo(f"❌ Error: {result['error']}")
                return

            click.echo("\n=== Analysis Results ===")
            click.echo(f"Contract: {result['contract_address']}")
            click.echo(f"Blockchain: {result['blockchain']}")
            click.echo(f"Combined Result: {'🎮 Gaming Contract' if result['combined_result'] else '❌ Not Gaming'}")

            click.echo("\n--- Heuristic Analysis ---")
            heuristic = result['heuristic_analysis']
            click.echo(f"Is Gaming: {heuristic['is_gaming']}")
            click.echo(f"Confidence: {heuristic['confidence']}")
            click.echo(f"Score: {heuristic['confidence_score']:.3f}")
            click.echo(f"Patterns: {', '.join(heuristic['detected_patterns'])}")

            if result['ml_analysis']:
                click.echo("\n--- ML Analysis ---")
                ml = result['ml_analysis']
                click.echo(f"Gaming Probability: {ml['is_gaming_probability']:.3f}")
                click.echo(f"Confidence Score: {ml['confidence_score']:.3f}")

                if ml['feature_importance']:
                    click.echo("Top Features:")
                    sorted_features = sorted(
                        ml['feature_importance'].items(),
                        key=lambda x: x[1],
                        reverse=True
                    )[:5]
                    for feature, importance in sorted_features:
                        click.echo(f"  {feature}: {importance:.3f}")

        except Exception as e:
            click.echo(f"❌ Error analyzing contract: {e}")

    asyncio.run(run_analysis())


@cli.group()
def scraper():
    """Scraper management commands"""
    pass


@scraper.command()
def start():
    """Start all scrapers"""
    from scrapers.manager import start_all_scrapers
    
    click.echo("Starting all scrapers...")
    try:
        start_all_scrapers()
        click.echo("✅ All scrapers started!")
    except Exception as e:
        click.echo(f"❌ Error starting scrapers: {e}")
        raise


@scraper.command()
def stop():
    """Stop all scrapers"""
    from scrapers.manager import stop_all_scrapers
    
    click.echo("Stopping all scrapers...")
    try:
        stop_all_scrapers()
        click.echo("✅ All scrapers stopped!")
    except Exception as e:
        click.echo(f"❌ Error stopping scrapers: {e}")
        raise


@scraper.command()
@click.argument('source_name')
def test_source(source_name):
    """Test a specific scraper source"""
    from scrapers.manager import test_scraper_source
    
    click.echo(f"Testing scraper source: {source_name}")
    try:
        result = test_scraper_source(source_name)
        if result:
            click.echo(f"✅ {source_name}: Working")
        else:
            click.echo(f"❌ {source_name}: Failed")
    except Exception as e:
        click.echo(f"❌ Error testing {source_name}: {e}")
        raise


@cli.command()
def run_api():
    """Run the API server"""
    click.echo(f"Starting API server on {settings.api.host}:{settings.api.port}")
    uvicorn.run(
        "api.main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.debug,
        workers=1 if settings.api.debug else settings.api.workers
    )


@cli.command()
def run_worker():
    """Run Celery worker"""
    import subprocess
    
    click.echo("Starting Celery worker...")
    try:
        subprocess.run([
            "celery", "-A", "scrapers.celery_app", "worker",
            "--loglevel=info"
        ])
    except KeyboardInterrupt:
        click.echo("Worker stopped.")


@cli.command()
def run_beat():
    """Run Celery beat scheduler"""
    import subprocess
    
    click.echo("Starting Celery beat scheduler...")
    try:
        subprocess.run([
            "celery", "-A", "scrapers.celery_app", "beat",
            "--loglevel=info"
        ])
    except KeyboardInterrupt:
        click.echo("Beat scheduler stopped.")


@cli.group()
def gaming():
    """Gaming-specific management commands"""
    pass


@gaming.command()
def sync_projects():
    """Sync gaming projects data"""
    from scrapers.gaming.projects import sync_gaming_projects
    
    click.echo("Syncing gaming projects...")
    try:
        asyncio.run(sync_gaming_projects())
        click.echo("✅ Gaming projects synced!")
    except Exception as e:
        click.echo(f"❌ Error syncing gaming projects: {e}")
        raise


@gaming.command()
def sync_nfts():
    """Sync NFT collections data"""
    from scrapers.gaming.nfts import sync_nft_collections
    
    click.echo("Syncing NFT collections...")
    try:
        asyncio.run(sync_nft_collections())
        click.echo("✅ NFT collections synced!")
    except Exception as e:
        click.echo(f"❌ Error syncing NFT collections: {e}")
        raise


@cli.command()
def status():
    """Show system status"""
    click.echo("Web3 Gaming News Tracker Status")
    click.echo("=" * 40)
    
    # Database status
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        click.echo("✅ Database: Connected")
    except:
        click.echo("❌ Database: Disconnected")
    
    # Redis status
    try:
        import redis
        r = redis.from_url(settings.redis.url)
        r.ping()
        click.echo("✅ Redis: Connected")
    except:
        click.echo("❌ Redis: Disconnected")
    
    # API status
    click.echo(f"🔧 API Config: {settings.api.host}:{settings.api.port}")
    click.echo(f"🔧 Environment: {settings.environment}")


if __name__ == "__main__":
    cli()
