#!/usr/bin/env python3
"""
Initialize gaming database with CSV data
"""
import os
import sys
import csv
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import get_settings
from models.base import Base, engine
from models.gaming import GamingProject

settings = get_settings()

def clean_string_value(value: Any) -> Optional[str]:
    """Clean and validate string values from CSV"""
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return None
    return str(value).strip()

def clean_integer_value(value: Any) -> Optional[int]:
    """Clean and validate integer values from CSV"""
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return None
    try:
        # Handle comma-separated numbers
        clean_val = str(value).replace(',', '').strip()
        return int(float(clean_val))
    except (ValueError, TypeError):
        return None

def clean_boolean_value(value: Any) -> bool:
    """Clean and validate boolean values from CSV"""
    if pd.isna(value) or value == '':
        return False
    str_val = str(value).strip().lower()
    return str_val in ['1', 'yes', 'true', 'y']

def parse_timestamp(value: Any) -> Optional[datetime]:
    """Parse timestamp from CSV"""
    if pd.isna(value) or value == '':
        return None
    try:
        return pd.to_datetime(value)
    except:
        return None

def generate_slug(project_name: str) -> str:
    """Generate URL-friendly slug from project name"""
    if not project_name:
        return ""
    return project_name.lower().replace(' ', '-').replace('(', '').replace(')', '').replace('/', '-')

def map_csv_to_gaming_project(row: Dict[str, Any]) -> Dict[str, Any]:
    """Map CSV row to GamingProject model fields"""
    project_name = clean_string_value(row.get('Project Name'))
    if not project_name:
        return None
    
    return {
        # Basic information
        'project_name': project_name,
        'project_website_link': clean_string_value(row.get('Project Website Link')),
        'whitepaper_link': clean_string_value(row.get('Whitepaper Link')),
        'blockchain': clean_string_value(row.get('Blockchain')),
        'validated_game_status': clean_string_value(row.get('Validated Game Status')),
        'game_status_notes': clean_string_value(row.get('Game Status Notes')),
        'token_schedule_link': clean_string_value(row.get('Token Schedule Link')),
        
        # Token 1
        'token1_symbol': clean_string_value(row.get('Token 1 - Symbol')),
        'token1_contract_address': clean_string_value(row.get('Token 1 - Token Contract Address')),
        'token1_type': clean_string_value(row.get('Token 1 - Type')),
        'token1_coingecko_link': clean_string_value(row.get('Token 1 Link to CoinGecko Listing')),
        'token1_blockchain_scanner_link': clean_string_value(row.get('Token 1 Blockchain Scanner link')),
        'token1_total_supply': clean_string_value(row.get('Token 1 Total Supply')),
        
        # Token 2
        'token2_symbol': clean_string_value(row.get('Token 2 - Symbol')),
        'token2_contract_address': clean_string_value(row.get('Token 2 - Token Contract Address')),
        'token2_type': clean_string_value(row.get('Token 2 - Type')),
        'token2_coingecko_link': clean_string_value(row.get('Token 2 Link to CoinGecko Listing')),
        'token2_blockchain_scanner_link': clean_string_value(row.get('Token 2 - Blockchain Scanner Link')),
        'token2_total_supply': clean_string_value(row.get('Token 2 Total Supply')),
        
        # Genre classification
        'primary_genre': clean_string_value(row.get('Which genre best fits this title? Choose only ONE')),
        'action_subgenre': clean_string_value(row.get('Choose ONE Action sub-genre that best fits this game')),
        'action_adventure_subgenre': clean_string_value(row.get('Choose ONE Action Adventure sub-genre that best fits this game')),
        'action_rpg_subgenre': clean_string_value(row.get('Choose ONE Action RPG sub-genre that best fits this game')),
        'adventure_subgenre': clean_string_value(row.get('Choose ONE Adventure sub-genre that best fits this game')),
        'battle_royale_subgenre': clean_string_value(row.get('Choose ONE Battle Royale sub-genre that best fits this game')),
        'casual_subgenre': clean_string_value(row.get('Choose ONE Casual sub-genre that best fits this game')),
        'fighting_subgenre': clean_string_value(row.get('Choose ONE Fighting sub-genre that best fits this game')),
        'fps_subgenre': clean_string_value(row.get('Choose ONE FPS sub-genre that best fits this game')),
        'mmorpg_subgenre': clean_string_value(row.get('Choose ONE MMORPG sub-genre that best fits this game')),
        'party_subgenre': clean_string_value(row.get('Choose ONE Party sub-genre that best fits this game')),
        'platformer_subgenre': clean_string_value(row.get('Choose ONE Platformer sub-genre that best fits this game')),
        'puzzle_subgenre': clean_string_value(row.get('Choose ONE Puzzle sub-genre that best fits this game')),
        'racing_subgenre': clean_string_value(row.get('Choose ONE Racing sub-genre that best fits this game')),
        'rts_subgenre': clean_string_value(row.get('Choose ONE RTS sub-genre that best fits this game')),
        'rpg_subgenre': clean_string_value(row.get('Choose ONE RPG sub-genre that best fits this game')),
        'shooter_subgenre': clean_string_value(row.get('Choose ONE Shooter sub-genre that best fits this game')),
        'simulation_subgenre': clean_string_value(row.get('Choose ONE Simulation sub-genre that best fits this game')),
        'sports_subgenre': clean_string_value(row.get('Choose ONE Sports sub-genre that best fits this game')),
        'stealth_subgenre': clean_string_value(row.get('Choose ONE stealth sub-genre that best fits this game')),
        'strategy_subgenre': clean_string_value(row.get('Choose ONE Strategy sub-genre that best fits this game')),
        'survival_subgenre': clean_string_value(row.get('Choose ONE Survival sub-genre that best fits this game')),
        'tactical_rpg_subgenre': clean_string_value(row.get('Choose ONE Tactical RPG sub-genre that best fits this game')),
        
        # NFT information
        'involves_nfts': clean_boolean_value(row.get('Does this project involve NFTs?')),
        'nft_marketplace_link': clean_string_value(row.get('Marketplace link for NFTs for this game')),
        'nft_contract_address': clean_string_value(row.get('What is the NFT Contract Address?')),
        'nft_blockchain_scanner_link': clean_string_value(row.get('Blockchain scanner link of NFT Contract Address')),
        'nft_function': clean_string_value(row.get('What are the NFTs used for?') or row.get('What is the function of the NFTs?')),
        'nft_marketplace_links': clean_string_value(row.get('NFT Marketplace Links')),
        'nft_name': clean_string_value(row.get('NFT name')),
        'nft_holding_wallets_count': clean_integer_value(row.get('How many wallets are holding this NFT?')),
        'nft_holding_wallets_source': clean_string_value(row.get('Source for number of NFT-holding wallets')),
        
        # Game style
        'game_style': clean_string_value(row.get('Game Style (check all that apply)')),
        
        # Social media
        'twitter_link': clean_string_value(row.get('Twitter (X) link')),
        'discord_link': clean_string_value(row.get('Discord Server Link')),
        'telegram_link': clean_string_value(row.get('Telegram Channel Link')),
        'medium_link': clean_string_value(row.get('Medium Link')),
        'youtube_link': clean_string_value(row.get('Youtube Channel')),
        'linkedin_link': clean_string_value(row.get('LinkedIn')),
        'facebook_link': clean_string_value(row.get('Facebook Page')),
        'reddit_link': clean_string_value(row.get('Reddit - Subreddit or User')),
        
        # Metrics
        'daily_active_users': clean_integer_value(row.get('On the average, how many daily active users (DAU) in the past month?')),
        'dau_source_link': clean_string_value(row.get('Link to Source of DAU count')),
        'daily_unique_active_wallets': clean_integer_value(row.get('On the average, how many daily unique active wallets (UAW) in the past month?')),
        'uaw_source_link': clean_string_value(row.get('Link to Source of UAW count')),
        
        # Auto-clicker fields
        'autoclicker_telegram_invite': clean_string_value(row.get('Auto-Clicker Telegram Invite Link')),
        'autoclicker_telegram_channel': clean_string_value(row.get('Auto-Clicker Telegram Community Channel Link')),
        'autoclicker_telegram_bot': clean_string_value(row.get('Auto-Clicker Telegram Bot Address')),
        'autoclicker_membership_population': clean_integer_value(row.get('Auto-Clicker TG Membership Population')),
        'autoclicker_twitter': clean_string_value(row.get('Auto-Clicker Twitter (X) Page')),
        'autoclicker_discord': clean_string_value(row.get('Auto-Clicker Discord invite link')),
        'autoclicker_medium': clean_string_value(row.get('Auto-Clicker Medium Link')),
        'autoclicker_youtube': clean_string_value(row.get('Auto-Clicker YouTube Link')),
        'autoclicker_has_token': clean_boolean_value(row.get('Does this Auto-Clicker have an official Token?')),
        
        # Development
        'developer': clean_string_value(row.get('Developer')),
        'platform': clean_string_value(row.get('Platform')),
        
        # Additional fields
        'additional_tokens': clean_string_value(row.get('Additional Tokens')),
        'notes_comments': clean_string_value(row.get('Notes and Comments')),
        'additional_notes': clean_string_value(row.get('Additional Notes and Comments')),
        'notes_additional_tokens': clean_string_value(row.get('Notes, Additional Tokens')),
        
        # Metadata
        'email_address': clean_string_value(row.get('Email Address')),
        'timestamp': parse_timestamp(row.get('Timestamp')),
        
        # Generated fields
        'slug': generate_slug(project_name),
        'description': clean_string_value(row.get('Game Status Notes')) or clean_string_value(row.get('Notes and Comments')),
        'category': 'Gaming',  # Default category
        'subcategory': clean_string_value(row.get('Which genre best fits this title? Choose only ONE')),
        'is_active': True
    }

def create_tables():
    """Create all database tables"""
    print("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    print("✅ Tables created successfully")

def load_csv_data(csv_path: str):
    """Load and process CSV data"""
    print(f"Loading CSV data from {csv_path}...")
    
    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"CSV file not found: {csv_path}")
    
    # Read CSV with pandas for better handling
    df = pd.read_csv(csv_path)
    print(f"Found {len(df)} rows in CSV")
    
    # Create database session
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Clear existing data
        session.query(GamingProject).delete()
        session.commit()
        print("Cleared existing gaming projects")
        
        # Process each row
        projects_added = 0
        seen_projects = set()
        for index, row in df.iterrows():
            try:
                project_data = map_csv_to_gaming_project(row.to_dict())
                if project_data and project_data.get('project_name'):
                    project_name = project_data['project_name']

                    # Handle duplicates by adding a suffix
                    original_name = project_name
                    counter = 1
                    while project_name in seen_projects:
                        project_name = f"{original_name}_{counter}"
                        counter += 1

                    project_data['project_name'] = project_name
                    project_data['slug'] = generate_slug(project_name)
                    seen_projects.add(project_name)

                    project = GamingProject(**project_data)
                    session.add(project)
                    projects_added += 1
                    print(f"Added project: {project_name}")
            except Exception as e:
                print(f"Error processing row {index}: {e}")
                continue
        
        # Commit all changes
        session.commit()
        print(f"✅ Successfully added {projects_added} gaming projects to database")
        
    except Exception as e:
        session.rollback()
        print(f"❌ Error loading CSV data: {e}")
        raise
    finally:
        session.close()

def test_database_connection():
    """Test database connection"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection successful")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def main():
    """Main initialization function"""
    print("🚀 Initializing Gaming Database...")

    # Debug database URL
    from config.settings import get_settings
    settings = get_settings()
    print(f"🔧 Database URL: {settings.database.url}")

    # Test database connection
    if not test_database_connection():
        print("❌ Cannot proceed without database connection")
        return False
    
    # Create tables
    create_tables()
    
    # Load CSV data
    csv_path = project_root / "GamingDBDraftResponses.csv"
    load_csv_data(str(csv_path))
    
    print("🎉 Database initialization complete!")
    return True

if __name__ == "__main__":
    main()
