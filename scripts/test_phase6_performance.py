#!/usr/bin/env python3
"""
Phase 6 Performance Optimization Testing Script
Tests database indexing, Redis caching, API optimization, and memory management
"""
import asyncio
import time
import statistics
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
import requests
import psutil
import logging
from sqlalchemy import text
from sqlalchemy.orm import Session

# Add project root to path
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.base import get_db_sync, db_manager
from services.redis_cache import gaming_cache
from services.api_optimization import perf_monitor, preload_critical_data, start_background_processing
from services.memory_management import memory_optimizer, get_system_performance_report
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

# Test configuration
API_BASE_URL = f"http://{settings.api.host}:{settings.api.port}"
TEST_ITERATIONS = 10
CONCURRENT_REQUESTS = 5


class PerformanceTestSuite:
    """Comprehensive performance testing for Phase 6 optimizations"""
    
    def __init__(self):
        self.results = {
            'database_tests': {},
            'cache_tests': {},
            'api_tests': {},
            'memory_tests': {},
            'overall_metrics': {}
        }
        self.start_time = datetime.utcnow()
    
    def log_test_result(self, category: str, test_name: str, result: Dict[str, Any]):
        """Log test result to results dictionary"""
        if category not in self.results:
            self.results[category] = {}
        self.results[category][test_name] = result
        print(f"✓ {category}.{test_name}: {result.get('status', 'completed')}")
    
    def test_database_performance(self):
        """Test database query performance with new indexes"""
        print("\n=== Database Performance Tests ===")
        
        db = get_db_sync()
        try:
            # Test 1: Article queries with gaming category filter
            start_time = time.time()
            query = text("""
                SELECT COUNT(*) 
                FROM articles 
                WHERE gaming_category IS NOT NULL 
                AND published_at >= NOW() - INTERVAL '24 hours'
            """)
            result = db.execute(query).scalar()
            duration = time.time() - start_time
            
            self.log_test_result('database_tests', 'gaming_category_filter', {
                'duration_ms': duration * 1000,
                'result_count': result,
                'status': 'pass' if duration < 0.1 else 'slow'
            })
            
            # Test 2: Gaming project lookup by name
            start_time = time.time()
            query = text("""
                SELECT * FROM gaming_projects 
                WHERE LOWER(project_name) LIKE LOWER('%axie%')
                AND is_active = true
            """)
            result = db.execute(query).fetchall()
            duration = time.time() - start_time
            
            self.log_test_result('database_tests', 'project_name_search', {
                'duration_ms': duration * 1000,
                'result_count': len(result),
                'status': 'pass' if duration < 0.05 else 'slow'
            })
            
            # Test 3: Blockchain data time-series query
            start_time = time.time()
            query = text("""
                SELECT blockchain, COUNT(*) as event_count
                FROM blockchain_data 
                WHERE block_timestamp >= NOW() - INTERVAL '7 days'
                GROUP BY blockchain
                ORDER BY event_count DESC
            """)
            result = db.execute(query).fetchall()
            duration = time.time() - start_time
            
            self.log_test_result('database_tests', 'blockchain_timeseries', {
                'duration_ms': duration * 1000,
                'result_count': len(result),
                'status': 'pass' if duration < 0.2 else 'slow'
            })
            
            # Test 4: Complex join query with JSON filtering
            start_time = time.time()
            query = text("""
                SELECT a.title, a.gaming_projects, s.name as source_name
                FROM articles a
                JOIN sources s ON a.source_id = s.id
                WHERE a.gaming_projects ? 'Axie Infinity'
                AND a.published_at >= NOW() - INTERVAL '7 days'
                ORDER BY a.relevance_score DESC NULLS LAST
                LIMIT 10
            """)
            result = db.execute(query).fetchall()
            duration = time.time() - start_time
            
            self.log_test_result('database_tests', 'json_filter_join', {
                'duration_ms': duration * 1000,
                'result_count': len(result),
                'status': 'pass' if duration < 0.15 else 'slow'
            })
            
        except Exception as e:
            self.log_test_result('database_tests', 'error', {
                'error': str(e),
                'status': 'fail'
            })
        finally:
            db.close()
    
    def test_redis_cache_performance(self):
        """Test Redis cache performance and hit rates"""
        print("\n=== Redis Cache Performance Tests ===")
        
        try:
            # Test 1: Cache write performance
            test_data = {'test': 'data', 'timestamp': datetime.utcnow().isoformat()}
            
            start_time = time.time()
            for i in range(100):
                gaming_cache.set(f"test_key_{i}", test_data, ttl=300)
            write_duration = time.time() - start_time
            
            self.log_test_result('cache_tests', 'write_performance', {
                'duration_ms': write_duration * 1000,
                'operations': 100,
                'ops_per_second': 100 / write_duration,
                'status': 'pass' if write_duration < 1.0 else 'slow'
            })
            
            # Test 2: Cache read performance
            start_time = time.time()
            for i in range(100):
                gaming_cache.get(f"test_key_{i}")
            read_duration = time.time() - start_time
            
            self.log_test_result('cache_tests', 'read_performance', {
                'duration_ms': read_duration * 1000,
                'operations': 100,
                'ops_per_second': 100 / read_duration,
                'status': 'pass' if read_duration < 0.5 else 'slow'
            })
            
            # Test 3: Cache hit rate simulation
            # Preload some gaming data
            gaming_cache.cache_gaming_projects([{'name': 'Test Project', 'blockchain': 'ethereum'}])
            
            hits = 0
            for i in range(10):
                result = gaming_cache.get_gaming_projects()
                if result:
                    hits += 1
            
            self.log_test_result('cache_tests', 'hit_rate', {
                'hits': hits,
                'total_requests': 10,
                'hit_rate': hits / 10,
                'status': 'pass' if hits >= 8 else 'low_hit_rate'
            })
            
            # Test 4: Cache stats
            cache_stats = gaming_cache.get_cache_stats()
            self.log_test_result('cache_tests', 'cache_stats', {
                'stats': cache_stats,
                'status': 'pass' if cache_stats else 'no_stats'
            })
            
            # Cleanup test keys
            for i in range(100):
                gaming_cache.delete(f"test_key_{i}")
                
        except Exception as e:
            self.log_test_result('cache_tests', 'error', {
                'error': str(e),
                'status': 'fail'
            })
    
    def test_api_response_times(self):
        """Test API endpoint response times"""
        print("\n=== API Response Time Tests ===")
        
        endpoints = [
            '/dashboard/overview',
            '/dashboard/news-analytics',
            '/dashboard/real-time',
            '/gaming/articles?limit=20',
            '/gaming/projects'
        ]
        
        for endpoint in endpoints:
            try:
                response_times = []
                
                for i in range(TEST_ITERATIONS):
                    start_time = time.time()
                    response = requests.get(f"{API_BASE_URL}{endpoint}", timeout=10)
                    duration = time.time() - start_time
                    
                    if response.status_code == 200:
                        response_times.append(duration)
                    else:
                        print(f"  ⚠ {endpoint}: HTTP {response.status_code}")
                
                if response_times:
                    avg_time = statistics.mean(response_times)
                    max_time = max(response_times)
                    min_time = min(response_times)
                    
                    self.log_test_result('api_tests', endpoint.replace('/', '_'), {
                        'avg_response_time_ms': avg_time * 1000,
                        'max_response_time_ms': max_time * 1000,
                        'min_response_time_ms': min_time * 1000,
                        'successful_requests': len(response_times),
                        'status': 'pass' if avg_time < 0.5 else 'slow'
                    })
                
            except Exception as e:
                self.log_test_result('api_tests', endpoint.replace('/', '_'), {
                    'error': str(e),
                    'status': 'fail'
                })
    
    def test_memory_management(self):
        """Test memory management and optimization"""
        print("\n=== Memory Management Tests ===")
        
        try:
            # Test 1: Initial memory baseline
            initial_memory = memory_optimizer.monitor.get_memory_metrics()
            
            # Test 2: Force garbage collection
            gc_stats = memory_optimizer.force_garbage_collection()
            
            # Test 3: Memory after cleanup
            post_cleanup_memory = memory_optimizer.monitor.get_memory_metrics()
            
            memory_saved = initial_memory.process_memory - post_cleanup_memory.process_memory
            
            self.log_test_result('memory_tests', 'garbage_collection', {
                'initial_memory_mb': initial_memory.process_memory,
                'post_cleanup_memory_mb': post_cleanup_memory.process_memory,
                'memory_saved_mb': memory_saved,
                'gc_collections': gc_stats['collected'],
                'status': 'pass' if memory_saved >= 0 else 'no_improvement'
            })
            
            # Test 4: Database connection pool status
            pool_status = db_manager.get_connection_pool_status()
            
            self.log_test_result('memory_tests', 'connection_pool', {
                'pool_status': pool_status,
                'status': 'pass' if pool_status['checked_out'] < pool_status['pool_size'] else 'pool_exhausted'
            })
            
            # Test 5: System performance report
            perf_report = get_system_performance_report()
            
            self.log_test_result('memory_tests', 'system_performance', {
                'memory_percent': perf_report['memory_metrics']['memory_percent'],
                'process_memory_mb': perf_report['memory_metrics']['process_memory'],
                'cache_hit_rate': perf_report['cache_stats'].get('hit_rate', 0),
                'status': 'pass' if perf_report['memory_metrics']['memory_percent'] < 80 else 'high_memory'
            })
            
        except Exception as e:
            self.log_test_result('memory_tests', 'error', {
                'error': str(e),
                'status': 'fail'
            })
    
    def run_concurrent_load_test(self):
        """Run concurrent load test to simulate real usage"""
        print("\n=== Concurrent Load Test ===")
        
        async def make_request(session, endpoint):
            try:
                start_time = time.time()
                response = requests.get(f"{API_BASE_URL}{endpoint}", timeout=10)
                duration = time.time() - start_time
                return {
                    'endpoint': endpoint,
                    'status_code': response.status_code,
                    'duration': duration,
                    'success': response.status_code == 200
                }
            except Exception as e:
                return {
                    'endpoint': endpoint,
                    'error': str(e),
                    'success': False
                }
        
        # Simulate concurrent requests
        endpoints = ['/dashboard/overview', '/gaming/articles', '/dashboard/real-time']
        
        results = []
        start_time = time.time()
        
        for _ in range(CONCURRENT_REQUESTS):
            for endpoint in endpoints:
                result = asyncio.run(make_request(None, endpoint))
                results.append(result)
        
        total_duration = time.time() - start_time
        
        successful_requests = [r for r in results if r.get('success', False)]
        failed_requests = [r for r in results if not r.get('success', False)]
        
        if successful_requests:
            avg_response_time = statistics.mean([r['duration'] for r in successful_requests])
        else:
            avg_response_time = 0
        
        self.log_test_result('api_tests', 'concurrent_load', {
            'total_requests': len(results),
            'successful_requests': len(successful_requests),
            'failed_requests': len(failed_requests),
            'avg_response_time_ms': avg_response_time * 1000,
            'total_duration_s': total_duration,
            'requests_per_second': len(results) / total_duration,
            'status': 'pass' if len(failed_requests) == 0 else 'some_failures'
        })
    
    def generate_report(self):
        """Generate comprehensive performance test report"""
        print("\n" + "="*60)
        print("PHASE 6 PERFORMANCE OPTIMIZATION TEST REPORT")
        print("="*60)
        
        # Overall metrics
        total_duration = (datetime.utcnow() - self.start_time).total_seconds()
        self.results['overall_metrics'] = {
            'test_duration_seconds': total_duration,
            'timestamp': datetime.utcnow().isoformat(),
            'test_environment': {
                'api_host': settings.api.host,
                'api_port': settings.api.port,
                'database_url': settings.database.url.split('@')[0] + '@***',  # Hide credentials
                'redis_url': settings.redis.url.split('@')[0] + '@***'  # Hide credentials
            }
        }
        
        # Print summary
        for category, tests in self.results.items():
            if category == 'overall_metrics':
                continue
                
            print(f"\n{category.upper().replace('_', ' ')}:")
            for test_name, result in tests.items():
                status = result.get('status', 'unknown')
                if status == 'pass':
                    print(f"  ✓ {test_name}: PASS")
                elif status == 'fail':
                    print(f"  ✗ {test_name}: FAIL - {result.get('error', 'Unknown error')}")
                else:
                    print(f"  ⚠ {test_name}: {status.upper()}")
        
        # Save detailed results
        report_file = f"performance_test_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"\nDetailed report saved to: {report_file}")
        print(f"Total test duration: {total_duration:.2f} seconds")


async def main():
    """Run comprehensive Phase 6 performance tests"""
    print("Starting Phase 6 Performance Optimization Tests...")
    print(f"Testing against: {API_BASE_URL}")

    # Start background processing
    await start_background_processing()

    # Preload critical data for cache testing
    preload_critical_data()

    # Initialize test suite
    test_suite = PerformanceTestSuite()
    
    # Run all test categories
    test_suite.test_database_performance()
    test_suite.test_redis_cache_performance()
    test_suite.test_api_response_times()
    test_suite.test_memory_management()
    test_suite.run_concurrent_load_test()
    
    # Generate final report
    test_suite.generate_report()


if __name__ == "__main__":
    asyncio.run(main())
