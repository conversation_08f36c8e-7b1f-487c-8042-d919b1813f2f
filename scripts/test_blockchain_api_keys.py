#!/usr/bin/env python3
"""
Test script to validate blockchain API key configuration and functionality
"""
import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import get_settings
from blockchain.data_clients.etherscan import etherscan_client
from blockchain.data_clients.solscan import solscan_client
from blockchain.data_clients.manager import BlockchainDataManager

settings = get_settings()


async def test_api_key_configuration():
    """Test that API keys are properly configured from environment variables"""
    print("🔑 Testing API Key Configuration")
    print("=" * 50)
    
    # Test Etherscan API key
    etherscan_key = settings.blockchain_data.etherscan_api_key
    print(f"Etherscan API Key: {'✅ Configured' if etherscan_key and etherscan_key != 'your_etherscan_api_key' else '❌ Not configured'}")
    if etherscan_key and etherscan_key != 'your_etherscan_api_key':
        print(f"  Key starts with: {etherscan_key[:8]}...")
    
    # Test Solscan API key
    solscan_key = settings.blockchain_data.solscan_api_key
    print(f"Solscan API Key: {'✅ Configured' if solscan_key and solscan_key != 'your_solscan_api_key' else '❌ Not configured'}")
    if solscan_key and solscan_key != 'your_solscan_api_key':
        print(f"  Key starts with: {solscan_key[:20]}...")
    
    print()


async def test_etherscan_client():
    """Test Etherscan client functionality"""
    print("🔗 Testing Etherscan Client")
    print("=" * 50)
    
    try:
        # Use async context manager
        async with etherscan_client:
            # Test health check
            health = await etherscan_client.health_check()
            print(f"Health Status: {health['status']}")
            print(f"API Key Configured: {health['api_key_configured']}")

            if health['status'] == 'healthy':
                # Test with a known gaming contract (example: Axie Infinity)
                test_contract = "******************************************"  # Axie Infinity contract
                print(f"\n🎮 Testing gaming contract analysis: {test_contract}")

                analysis = await etherscan_client.analyze_contract_for_gaming(test_contract)
                print(f"Contract Name: {analysis.get('contract_name', 'Unknown')}")
                print(f"Gaming Confidence Score: {analysis.get('confidence_score', 0):.2f}")
                print(f"Gaming Indicators: {len(analysis.get('gaming_indicators', []))}")

                if analysis.get('gaming_indicators'):
                    print("Gaming Indicators Found:")
                    for indicator in analysis['gaming_indicators'][:5]:  # Show first 5
                        print(f"  - {indicator}")

    except Exception as e:
        print(f"❌ Etherscan client test failed: {e}")
    
    print()


async def test_solscan_client():
    """Test Solscan client functionality"""
    print("☀️ Testing Solscan Client")
    print("=" * 50)
    
    try:
        # Use async context manager
        async with solscan_client:
            # Test health check
            health = await solscan_client.health_check()
            print(f"Health Status: {health['status']}")
            print(f"API Key Configured: {health['api_key_configured']}")

            if health['status'] == 'healthy':
                # Test with a known Solana gaming account (example: Star Atlas)
                test_account = "ATLASXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx"  # ATLAS token
                print(f"\n🎮 Testing gaming account analysis: {test_account}")

                analysis = await solscan_client.analyze_account_for_gaming(test_account)
                print(f"Account Exists: {analysis.get('account_exists', False)}")
                print(f"Gaming Confidence Score: {analysis.get('confidence_score', 0):.2f}")
                print(f"Gaming Indicators: {len(analysis.get('gaming_indicators', []))}")

                if analysis.get('gaming_indicators'):
                    print("Gaming Indicators Found:")
                    for indicator in analysis['gaming_indicators'][:5]:  # Show first 5
                        print(f"  - {indicator}")

    except Exception as e:
        print(f"❌ Solscan client test failed: {e}")
    
    print()


async def test_blockchain_data_manager():
    """Test blockchain data manager with new clients"""
    print("🔧 Testing Blockchain Data Manager")
    print("=" * 50)
    
    try:
        manager = BlockchainDataManager()
        await manager.initialize_clients()
        
        print(f"Initialized Clients: {list(manager.clients.keys())}")
        
        # Test connections
        connection_results = await manager.test_all_connections()
        print("\nConnection Test Results:")
        for client_name, is_connected in connection_results.items():
            status = "✅ Connected" if is_connected else "❌ Failed"
            print(f"  {client_name}: {status}")
        
    except Exception as e:
        print(f"❌ Blockchain data manager test failed: {e}")
    
    print()


async def test_contract_vetting_integration():
    """Test integration with contract vetting service"""
    print("🔍 Testing Contract Vetting Integration")
    print("=" * 50)
    
    try:
        # Import contract vetting service
        from services.contract_vetting_service import contract_vetting_service
        
        # Test Ethereum contract vetting
        eth_contract = "******************************************"  # Axie Infinity
        print(f"Vetting Ethereum contract: {eth_contract}")
        
        eth_report = await contract_vetting_service.vet_contract(eth_contract, "ethereum")
        print(f"Vetting Result: {eth_report.result}")
        print(f"Confidence Score: {eth_report.confidence_score:.2f}")
        print(f"Gaming Indicators: {len(eth_report.gaming_indicators)}")
        
        # Test Solana account vetting
        sol_account = "ATLASXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx"  # ATLAS token
        print(f"\nVetting Solana account: {sol_account}")
        
        sol_report = await contract_vetting_service.vet_contract(sol_account, "solana")
        print(f"Vetting Result: {sol_report.result}")
        print(f"Confidence Score: {sol_report.confidence_score:.2f}")
        print(f"Gaming Indicators: {len(sol_report.gaming_indicators)}")
        
    except Exception as e:
        print(f"❌ Contract vetting integration test failed: {e}")
    
    print()


async def main():
    """Run all tests"""
    print("🧪 Blockchain API Key and Client Testing")
    print("=" * 60)
    print()
    
    await test_api_key_configuration()
    await test_etherscan_client()
    await test_solscan_client()
    await test_blockchain_data_manager()
    await test_contract_vetting_integration()
    
    print("✅ All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
