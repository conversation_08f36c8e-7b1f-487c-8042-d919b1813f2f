#!/usr/bin/env python3
"""
Simple End-to-End Pipeline Test

Tests the core components without circular imports:
1. Database connection
2. Solscan API integration
3. Notification system
4. Dashboard connectivity
"""

import asyncio
import sys
import os
import json
import time
import aiohttp
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import get_settings
from models.base import get_db
from models.gaming import GamingProject

class SimplePipelineTest:
    """Simple test suite avoiding circular imports"""
    
    def __init__(self):
        self.settings = get_settings()
        self.test_results = []
        
    async def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Simple Pipeline Tests")
        print("=" * 50)
        
        # Test 1: Database Connection
        await self.test_database_connection()
        
        # Test 2: Solscan API
        await self.test_solscan_api()
        
        # Test 3: API Server Connection
        await self.test_api_server()
        
        # Test 4: Dashboard Connection
        await self.test_dashboard_connection()
        
        # Print Results
        self.print_test_results()
        
    async def test_database_connection(self):
        """Test PostgreSQL database connection"""
        print("\n📊 Testing Database Connection...")
        try:
            db = next(get_db())
            
            # Test basic query
            project_count = db.query(GamingProject).count()
            
            # Get sample projects
            sample_projects = db.query(GamingProject).limit(3).all()
            
            self.test_results.append({
                "test": "Database Connection",
                "status": "✅ PASS",
                "details": f"Connected successfully. Found {project_count} gaming projects."
            })
            
            print(f"   ✅ Database connected successfully")
            print(f"   📈 Found {project_count} existing gaming projects")
            
            if sample_projects:
                print(f"   🎮 Sample projects:")
                for project in sample_projects:
                    print(f"      - {project.project_name} ({project.blockchain})")
            
            db.close()
            
        except Exception as e:
            self.test_results.append({
                "test": "Database Connection",
                "status": "❌ FAIL",
                "details": f"Error: {str(e)}"
            })
            print(f"   ❌ Database connection failed: {e}")
            
    async def test_solscan_api(self):
        """Test Solscan API integration"""
        print("\n🌐 Testing Solscan API...")
        try:
            # Test both Pro API (with key) and Public API (fallback)
            solscan_api_key = os.getenv('SOLSCAN_API_KEY')

            async with aiohttp.ClientSession() as session:
                # Test with ATLAS token from Star Atlas
                test_address = "ATLASXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx"

                # Try Pro API first
                if solscan_api_key:
                    headers = {
                        'token': solscan_api_key,
                        'accept': 'application/json'
                    }
                    url = f"https://pro-api.solscan.io/v2.0/token/meta?address={test_address}"

                    async with session.get(url, headers=headers) as response:
                        if response.status == 200:
                            data = await response.json()
                            token_name = data.get('data', {}).get('name', 'Unknown')
                            token_symbol = data.get('data', {}).get('symbol', 'Unknown')

                            self.test_results.append({
                                "test": "Solscan API",
                                "status": "✅ PASS",
                                "details": f"Pro API connected. Token: {token_name} ({token_symbol})"
                            })

                            print(f"   ✅ Solscan Pro API connected successfully")
                            print(f"   🪙 Test token: {token_name} ({token_symbol})")
                            return
                        else:
                            print(f"   ⚠️  Pro API failed (status {response.status}), trying public API...")

                # Fallback to Public API
                url = f"https://public-api.solscan.io/token/meta?tokenAddress={test_address}"

                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        token_name = data.get('name', 'Unknown')
                        token_symbol = data.get('symbol', 'Unknown')

                        self.test_results.append({
                            "test": "Solscan API",
                            "status": "✅ PASS",
                            "details": f"Public API connected. Token: {token_name} ({token_symbol})"
                        })

                        print(f"   ✅ Solscan Public API connected successfully")
                        print(f"   🪙 Test token: {token_name} ({token_symbol})")
                        print(f"   📊 Response status: {response.status}")
                        print(f"   💡 Note: Pro API requires subscription upgrade")

                    else:
                        raise Exception(f"Both Pro and Public APIs failed. Status: {response.status}")

        except Exception as e:
            self.test_results.append({
                "test": "Solscan API",
                "status": "❌ FAIL",
                "details": f"Error: {str(e)}"
            })
            print(f"   ❌ Solscan API failed: {e}")
            
    async def test_api_server(self):
        """Test API server connection"""
        print("\n🔌 Testing API Server Connection...")
        try:
            async with aiohttp.ClientSession() as session:
                # Test basic stats endpoint
                async with session.get("http://localhost:8001/stats") as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        self.test_results.append({
                            "test": "API Server",
                            "status": "✅ PASS",
                            "details": f"Server responding. Projects: {data.get('projects', 0)}"
                        })
                        
                        print(f"   ✅ API server connected successfully")
                        print(f"   📊 Server stats: {data}")
                        
                    else:
                        raise Exception(f"Server returned status {response.status}")
                        
        except Exception as e:
            self.test_results.append({
                "test": "API Server",
                "status": "❌ FAIL",
                "details": f"Error: {str(e)}"
            })
            print(f"   ❌ API server connection failed: {e}")
            
    async def test_dashboard_connection(self):
        """Test dashboard connection"""
        print("\n🖥️  Testing Dashboard Connection...")
        try:
            async with aiohttp.ClientSession() as session:
                # Test dashboard accessibility
                async with session.get("http://localhost:3001") as response:
                    if response.status == 200:
                        content = await response.text()
                        
                        # Check if it's a React app
                        if "react" in content.lower() or "web3-gaming-dashboard" in content:
                            self.test_results.append({
                                "test": "Dashboard Connection",
                                "status": "✅ PASS",
                                "details": "Dashboard accessible and serving React app"
                            })
                            
                            print(f"   ✅ Dashboard connected successfully")
                            print(f"   🎯 React app serving on port 3001")
                            
                        else:
                            raise Exception("Dashboard not serving expected React content")
                            
                    else:
                        raise Exception(f"Dashboard returned status {response.status}")
                        
        except Exception as e:
            self.test_results.append({
                "test": "Dashboard Connection",
                "status": "❌ FAIL",
                "details": f"Error: {str(e)}"
            })
            print(f"   ❌ Dashboard connection failed: {e}")
            
    def print_test_results(self):
        """Print test results summary"""
        print("\n" + "=" * 50)
        print("📋 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        passed = 0
        failed = 0
        
        for result in self.test_results:
            print(f"\n{result['test']}: {result['status']}")
            print(f"   Details: {result['details']}")
            
            if "✅ PASS" in result['status']:
                passed += 1
            else:
                failed += 1
                
        print(f"\n" + "=" * 50)
        print(f"📊 FINAL RESULTS: {passed} PASSED, {failed} FAILED")
        
        if failed == 0:
            print("🎉 ALL TESTS PASSED! Core pipeline is working.")
            print("\n🔄 Next Steps:")
            print("   1. Dashboard is accessible at http://localhost:3001")
            print("   2. API server is running on http://localhost:8001")
            print("   3. Solscan API is configured and working")
            print("   4. Database has gaming projects ready")
            print("   5. Ready for blockchain scraper integration")
        else:
            print("⚠️  Some tests failed. Please review and fix issues.")
            
        print("=" * 50)

async def main():
    """Main test execution"""
    tester = SimplePipelineTest()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
