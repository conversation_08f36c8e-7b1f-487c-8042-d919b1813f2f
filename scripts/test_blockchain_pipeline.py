#!/usr/bin/env python3
"""
End-to-End Blockchain Detection Pipeline Test Script

This script tests the complete blockchain scraper → gaming projects integration pipeline:
1. Blockchain contract detection
2. Gaming project creation
3. Notification system
4. Database integration
5. Dashboard display

Usage:
    python scripts/test_blockchain_pipeline.py
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, List, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import get_settings
from models.base import get_db
from services.gaming_project_creator import GamingProjectCreator
from services.notification_service import NotificationService
from scrapers.blockchain.contract_analyzer import ContractAnalyzer
from scrapers.blockchain.data_types import ContractAnalysisResult, ContractConfidence
from models.gaming_projects import GamingProject
from sqlalchemy.orm import Session

class BlockchainPipelineTest:
    """Comprehensive test suite for blockchain detection pipeline"""
    
    def __init__(self):
        self.settings = get_settings()
        self.project_creator = GamingProjectCreator()
        self.notification_service = NotificationService()
        self.contract_analyzer = ContractAnalyzer()
        self.test_results = []
        
    async def run_all_tests(self):
        """Run complete end-to-end test suite"""
        print("🚀 Starting Blockchain Detection Pipeline Tests")
        print("=" * 60)
        
        # Test 1: Database Connection
        await self.test_database_connection()
        
        # Test 2: Notification Service
        await self.test_notification_service()
        
        # Test 3: Contract Analysis (Simulated)
        await self.test_contract_analysis()
        
        # Test 4: Gaming Project Creation
        await self.test_gaming_project_creation()
        
        # Test 5: End-to-End Pipeline
        await self.test_complete_pipeline()
        
        # Test 6: Solscan API Integration
        await self.test_solscan_integration()
        
        # Print Results
        self.print_test_results()
        
    async def test_database_connection(self):
        """Test PostgreSQL database connection"""
        print("\n📊 Testing Database Connection...")
        try:
            db = next(get_db())
            
            # Test basic query
            projects = db.query(GamingProject).limit(3).all()
            project_count = db.query(GamingProject).count()
            
            self.test_results.append({
                "test": "Database Connection",
                "status": "✅ PASS",
                "details": f"Connected successfully. Found {project_count} gaming projects."
            })
            
            print(f"   ✅ Database connected successfully")
            print(f"   📈 Found {project_count} existing gaming projects")
            
            db.close()
            
        except Exception as e:
            self.test_results.append({
                "test": "Database Connection",
                "status": "❌ FAIL",
                "details": f"Error: {str(e)}"
            })
            print(f"   ❌ Database connection failed: {e}")
            
    async def test_notification_service(self):
        """Test notification service functionality"""
        print("\n🔔 Testing Notification Service...")
        try:
            # Test notification creation
            await self.notification_service.notify_new_gaming_project(
                project_name="Test Gaming Project",
                contract_address="0x1234567890abcdef1234567890abcdef12345678",
                blockchain="solana",
                confidence="HIGH",
                project_id=999
            )
            
            # Test notification retrieval
            notifications = await self.notification_service.get_notifications(limit=5)
            
            self.test_results.append({
                "test": "Notification Service",
                "status": "✅ PASS",
                "details": f"Created and retrieved notifications successfully"
            })
            
            print(f"   ✅ Notification service working")
            print(f"   📬 Recent notifications: {len(notifications)}")
            
        except Exception as e:
            self.test_results.append({
                "test": "Notification Service",
                "status": "❌ FAIL",
                "details": f"Error: {str(e)}"
            })
            print(f"   ❌ Notification service failed: {e}")
            
    async def test_contract_analysis(self):
        """Test contract analysis with simulated data"""
        print("\n🔍 Testing Contract Analysis...")
        try:
            # Create simulated contract analysis result
            test_contract = ContractAnalysisResult(
                contract_address="0x1234567890abcdef1234567890abcdef12345678",
                blockchain="solana",
                confidence=ContractConfidence.HIGH,
                gaming_indicators={
                    "has_nft_functions": True,
                    "has_token_functions": True,
                    "has_gaming_keywords": True,
                    "transaction_patterns": "gaming"
                },
                metadata={
                    "name": "Test Gaming Contract",
                    "symbol": "TGC",
                    "total_supply": 1000000,
                    "description": "A test gaming contract for pipeline testing"
                }
            )
            
            self.test_results.append({
                "test": "Contract Analysis",
                "status": "✅ PASS",
                "details": f"Contract analysis simulation successful"
            })
            
            print(f"   ✅ Contract analysis working")
            print(f"   🎮 Gaming confidence: {test_contract.confidence.value}")
            print(f"   📝 Gaming indicators: {len(test_contract.gaming_indicators)}")
            
            return test_contract
            
        except Exception as e:
            self.test_results.append({
                "test": "Contract Analysis",
                "status": "❌ FAIL",
                "details": f"Error: {str(e)}"
            })
            print(f"   ❌ Contract analysis failed: {e}")
            return None
            
    async def test_gaming_project_creation(self):
        """Test gaming project creation from contract analysis"""
        print("\n🎮 Testing Gaming Project Creation...")
        try:
            # Create test contract analysis
            test_contract = ContractAnalysisResult(
                contract_address="0xtest123456789abcdef123456789abcdef12345678",
                blockchain="solana",
                confidence=ContractConfidence.HIGH,
                gaming_indicators={
                    "has_nft_functions": True,
                    "has_token_functions": True,
                    "has_gaming_keywords": True
                },
                metadata={
                    "name": "Pipeline Test Game",
                    "symbol": "PTG",
                    "description": "Test game for pipeline validation"
                }
            )
            
            # Create gaming project
            db = next(get_db())
            project = await self.project_creator.create_project_from_contract(
                test_contract, db
            )
            
            if project:
                self.test_results.append({
                    "test": "Gaming Project Creation",
                    "status": "✅ PASS",
                    "details": f"Created project: {project.project_name}"
                })
                
                print(f"   ✅ Gaming project created successfully")
                print(f"   🏷️  Project name: {project.project_name}")
                print(f"   🔗 Contract: {project.token1_contract_address}")
                print(f"   ⛓️  Blockchain: {project.blockchain}")
                
                # Clean up test project
                db.delete(project)
                db.commit()
                print(f"   🧹 Test project cleaned up")
                
            else:
                raise Exception("Project creation returned None")
                
            db.close()
            
        except Exception as e:
            self.test_results.append({
                "test": "Gaming Project Creation",
                "status": "❌ FAIL",
                "details": f"Error: {str(e)}"
            })
            print(f"   ❌ Gaming project creation failed: {e}")
            
    async def test_complete_pipeline(self):
        """Test complete end-to-end pipeline"""
        print("\n🔄 Testing Complete Pipeline...")
        try:
            # Simulate complete pipeline flow
            start_time = time.time()
            
            # 1. Contract Detection (simulated)
            contract_result = ContractAnalysisResult(
                contract_address="0xpipeline123456789abcdef123456789abcdef123",
                blockchain="solana",
                confidence=ContractConfidence.HIGH,
                gaming_indicators={
                    "has_nft_functions": True,
                    "has_token_functions": True,
                    "has_gaming_keywords": True,
                    "transaction_patterns": "gaming"
                },
                metadata={
                    "name": "End-to-End Test Game",
                    "symbol": "E2E",
                    "description": "Complete pipeline test game"
                }
            )
            
            # 2. Gaming Project Creation
            db = next(get_db())
            project = await self.project_creator.create_project_from_contract(
                contract_result, db
            )
            
            # 3. Notification Sending
            if project:
                await self.notification_service.notify_new_gaming_project(
                    project_name=project.project_name,
                    contract_address=contract_result.contract_address,
                    blockchain=contract_result.blockchain,
                    confidence=contract_result.confidence.value,
                    project_id=project.id
                )
            
            end_time = time.time()
            pipeline_time = end_time - start_time
            
            self.test_results.append({
                "test": "Complete Pipeline",
                "status": "✅ PASS",
                "details": f"Pipeline completed in {pipeline_time:.2f}s"
            })
            
            print(f"   ✅ Complete pipeline successful")
            print(f"   ⏱️  Pipeline time: {pipeline_time:.2f} seconds")
            print(f"   🎯 Project created and notification sent")
            
            # Clean up
            if project:
                db.delete(project)
                db.commit()
                print(f"   🧹 Pipeline test data cleaned up")
                
            db.close()
            
        except Exception as e:
            self.test_results.append({
                "test": "Complete Pipeline",
                "status": "❌ FAIL",
                "details": f"Error: {str(e)}"
            })
            print(f"   ❌ Complete pipeline failed: {e}")
            
    async def test_solscan_integration(self):
        """Test Solscan API integration"""
        print("\n🌐 Testing Solscan API Integration...")
        try:
            import aiohttp
            
            # Test Solscan API connection
            solscan_api_key = os.getenv('SOLSCAN_API_KEY')
            if not solscan_api_key:
                raise Exception("SOLSCAN_API_KEY not found in environment")
                
            # Test API endpoint
            headers = {
                'token': solscan_api_key,
                'accept': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                # Test with a known Solana gaming token (e.g., ATLAS from Star Atlas)
                test_address = "ATLASXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx"
                url = f"https://pro-api.solscan.io/v2.0/token/meta?address={test_address}"
                
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        self.test_results.append({
                            "test": "Solscan API Integration",
                            "status": "✅ PASS",
                            "details": f"API connected successfully. Token: {data.get('data', {}).get('name', 'Unknown')}"
                        })
                        
                        print(f"   ✅ Solscan API connected successfully")
                        print(f"   🪙 Test token: {data.get('data', {}).get('name', 'Unknown')}")
                        print(f"   📊 Response status: {response.status}")
                        
                    else:
                        raise Exception(f"API returned status {response.status}")
                        
        except Exception as e:
            self.test_results.append({
                "test": "Solscan API Integration",
                "status": "❌ FAIL",
                "details": f"Error: {str(e)}"
            })
            print(f"   ❌ Solscan API integration failed: {e}")
            
    def print_test_results(self):
        """Print comprehensive test results"""
        print("\n" + "=" * 60)
        print("📋 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        passed = 0
        failed = 0
        
        for result in self.test_results:
            print(f"\n{result['test']}: {result['status']}")
            print(f"   Details: {result['details']}")
            
            if "✅ PASS" in result['status']:
                passed += 1
            else:
                failed += 1
                
        print(f"\n" + "=" * 60)
        print(f"📊 FINAL RESULTS: {passed} PASSED, {failed} FAILED")
        
        if failed == 0:
            print("🎉 ALL TESTS PASSED! Pipeline is ready for production.")
        else:
            print("⚠️  Some tests failed. Please review and fix issues.")
            
        print("=" * 60)

async def main():
    """Main test execution function"""
    tester = BlockchainPipelineTest()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
