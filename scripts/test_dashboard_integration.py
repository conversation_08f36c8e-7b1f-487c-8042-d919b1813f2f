#!/usr/bin/env python3
"""
Test script to verify dashboard integration with gaming analytics API
"""

import requests
import json
import time
from typing import Dict, Any

# API Configuration
API_BASE_URL = "http://localhost:8001/api/v1"
DASHBOARD_URL = "http://localhost:3000"

def test_api_endpoint(endpoint: str, timeout: int = 10) -> Dict[str, Any]:
    """Test an API endpoint and return the response"""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        print(f"Testing: {url}")
        
        response = requests.get(url, timeout=timeout)
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ Success: {endpoint}")
        return {"success": True, "data": data, "status_code": response.status_code}
        
    except requests.exceptions.Timeout:
        print(f"⏰ Timeout: {endpoint}")
        return {"success": False, "error": "timeout", "status_code": None}
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error: {endpoint} - {str(e)}")
        return {"success": False, "error": str(e), "status_code": getattr(e.response, 'status_code', None)}

def test_dashboard_accessibility() -> bool:
    """Test if the React dashboard is accessible"""
    try:
        response = requests.get(DASHBOARD_URL, timeout=5)
        response.raise_for_status()
        
        if "Web3 Gaming News Tracker" in response.text:
            print("✅ Dashboard is accessible and contains expected content")
            return True
        else:
            print("⚠️ Dashboard is accessible but content may be incorrect")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard not accessible: {str(e)}")
        return False

def main():
    """Run comprehensive dashboard integration tests"""
    print("🚀 Starting Dashboard Integration Tests")
    print("=" * 50)
    
    # Test dashboard accessibility
    print("\n📱 Testing Dashboard Accessibility")
    dashboard_ok = test_dashboard_accessibility()
    
    # Test core API endpoints
    print("\n🔌 Testing Core API Endpoints")
    endpoints_to_test = [
        "/health",
        "/dashboard/overview",
        "/dashboard/health",
    ]
    
    api_results = {}
    for endpoint in endpoints_to_test:
        result = test_api_endpoint(endpoint)
        api_results[endpoint] = result
        time.sleep(0.5)  # Rate limiting
    
    # Test gaming analytics endpoints
    print("\n🎮 Testing Gaming Analytics Endpoints")
    gaming_endpoints = [
        "/gaming-analytics/health",
        "/gaming-analytics/protocols",
        "/gaming-analytics/dashboard-data",
    ]
    
    gaming_results = {}
    for endpoint in gaming_endpoints:
        result = test_api_endpoint(endpoint, timeout=15)  # Longer timeout for gaming endpoints
        gaming_results[endpoint] = result
        time.sleep(1)  # Longer delay for gaming endpoints
    
    # Test potentially slow endpoints with warnings
    print("\n⚠️ Testing Potentially Slow Endpoints (may timeout)")
    slow_endpoints = [
        "/gaming-analytics/summary",
    ]
    
    slow_results = {}
    for endpoint in slow_endpoints:
        print(f"⏳ Testing {endpoint} (this may take a while or timeout)...")
        result = test_api_endpoint(endpoint, timeout=30)
        slow_results[endpoint] = result
        time.sleep(2)
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    print(f"Dashboard Accessible: {'✅' if dashboard_ok else '❌'}")
    
    print("\nCore API Endpoints:")
    for endpoint, result in api_results.items():
        status = "✅" if result["success"] else "❌"
        print(f"  {status} {endpoint}")
    
    print("\nGaming Analytics Endpoints:")
    for endpoint, result in gaming_results.items():
        status = "✅" if result["success"] else "❌"
        print(f"  {status} {endpoint}")
    
    print("\nSlow Endpoints:")
    for endpoint, result in slow_results.items():
        if result["success"]:
            status = "✅"
        elif result.get("error") == "timeout":
            status = "⏰"
        else:
            status = "❌"
        print(f"  {status} {endpoint}")
    
    # Overall status
    total_tests = len(api_results) + len(gaming_results) + len(slow_results) + (1 if dashboard_ok else 0)
    successful_tests = (
        sum(1 for r in api_results.values() if r["success"]) +
        sum(1 for r in gaming_results.values() if r["success"]) +
        sum(1 for r in slow_results.values() if r["success"]) +
        (1 if dashboard_ok else 0)
    )
    
    print(f"\n🎯 Overall Success Rate: {successful_tests}/{total_tests} ({(successful_tests/total_tests)*100:.1f}%)")
    
    if successful_tests >= total_tests * 0.8:  # 80% success rate
        print("🎉 Dashboard integration is working well!")
        return True
    else:
        print("⚠️ Dashboard integration needs attention")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
