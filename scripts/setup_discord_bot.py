#!/usr/bin/env python3
"""
Discord Bot Setup Script
Helps set up the Discord bot for web3/crypto gaming server discovery.
"""
import os
import sys
import json
from pathlib import Path

def create_env_template():
    """Create .env template with Discord bot configuration"""
    env_template = """# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_bot_token_here

# Discord Application Settings
DISCORD_CLIENT_ID=your_client_id_here
DISCORD_CLIENT_SECRET=your_client_secret_here

# Bot Permissions (calculated value)
# Required permissions: Read Messages, Send Messages, Read Message History, Create Invite
DISCORD_BOT_PERMISSIONS=68608

# Optional: Webhook URL for notifications
DISCORD_WEBHOOK_URL=your_webhook_url_here
"""
    
    with open('.env.discord', 'w') as f:
        f.write(env_template)
    
    print("✅ Created .env.discord template")
    print("📝 Please fill in your Discord bot credentials")

def create_bot_invite_url():
    """Generate bot invite URL"""
    client_id = input("Enter your Discord Application Client ID: ").strip()
    
    if not client_id:
        print("❌ Client ID is required")
        return
    
    # Required permissions for the bot
    permissions = 68608  # Read Messages + Send Messages + Read Message History + Create Invite
    
    invite_url = f"https://discord.com/api/oauth2/authorize?client_id={client_id}&permissions={permissions}&scope=bot"
    
    print(f"\n🔗 Bot Invite URL:")
    print(f"{invite_url}")
    print(f"\n📋 Copy this URL and paste it in your browser to invite the bot to servers")
    
    # Save to file
    with open('data/discord/bot_invite_url.txt', 'w') as f:
        f.write(invite_url)
    
    return invite_url

def setup_directories():
    """Create necessary directories"""
    directories = [
        'data/discord',
        'logs/discord',
        'config/discord'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 Created directory: {directory}")

def create_discord_config():
    """Create Discord bot configuration file"""
    config = {
        "bot_settings": {
            "command_prefix": "!",
            "case_insensitive": True,
            "strip_after_prefix": True,
            "intents": {
                "guilds": True,
                "guild_messages": True,
                "message_content": True,
                "guild_members": False,
                "presences": False
            }
        },
        "monitoring": {
            "scan_interval_hours": 6,
            "message_analysis_interval_minutes": 30,
            "max_messages_per_channel": 50,
            "relevance_threshold": 0.3
        },
        "keywords": {
            "gaming": [
                "p2e", "play to earn", "play-to-earn", "gamefi", "gaming", 
                "nft game", "blockchain game", "crypto game", "metaverse", 
                "virtual world", "guild", "esports", "tournament", "rpg", 
                "mmorpg", "strategy", "card game", "trading card", 
                "collectible", "avatar", "character"
            ],
            "blockchain": [
                "solana", "ethereum", "avalanche", "base", "bsc", 
                "binance smart chain", "ton", "polygon", "arbitrum", 
                "optimism", "fantom", "near", "web3", "blockchain", 
                "crypto", "defi", "nft", "token", "coin", 
                "smart contract", "dapp", "dao", "yield farming", "staking"
            ],
            "target_blockchains": [
                "solana", "ethereum", "avalanche", "base", "bsc", "ton"
            ]
        },
        "scraping": {
            "listing_sites": [
                "https://disboard.org/servers/tag/gaming",
                "https://disboard.org/servers/tag/crypto",
                "https://discord.me/servers/tag/gaming",
                "https://discord.me/servers/tag/blockchain"
            ],
            "rate_limit_delay": 2,
            "max_retries": 3
        }
    }
    
    with open('config/discord/bot_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Created Discord bot configuration")

def install_dependencies():
    """Install required Python packages"""
    packages = [
        "discord.py>=2.3.0",
        "aiohttp>=3.8.0",
        "python-dotenv>=1.0.0"
    ]
    
    print("📦 Installing Discord bot dependencies...")
    
    for package in packages:
        print(f"Installing {package}...")
        os.system(f"pip install {package}")
    
    print("✅ Dependencies installed")

def create_systemd_service():
    """Create systemd service file for running the bot"""
    service_content = f"""[Unit]
Description=Discord Web3 Gaming Bot
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'ubuntu')}
WorkingDirectory={os.getcwd()}
Environment=PATH={os.environ.get('PATH')}
ExecStart={sys.executable} scripts/discord_scraper.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    with open('discord-bot.service', 'w') as f:
        f.write(service_content)
    
    print("✅ Created systemd service file: discord-bot.service")
    print("📝 To install: sudo cp discord-bot.service /etc/systemd/system/")
    print("📝 To enable: sudo systemctl enable discord-bot")
    print("📝 To start: sudo systemctl start discord-bot")

def main():
    """Main setup function"""
    print("🤖 Discord Web3 Gaming Bot Setup")
    print("=" * 40)
    
    # Create directories
    setup_directories()
    
    # Create configuration files
    create_env_template()
    create_discord_config()
    
    # Install dependencies
    install_dependencies()
    
    # Create systemd service
    create_systemd_service()
    
    print("\n" + "=" * 40)
    print("✅ Setup complete!")
    print("\n📋 Next steps:")
    print("1. Go to https://discord.com/developers/applications")
    print("2. Create a new application and bot")
    print("3. Copy the bot token to .env.discord")
    print("4. Run: python scripts/setup_discord_bot.py --invite")
    print("5. Use the generated invite URL to add bot to servers")
    print("6. Run: python scripts/discord_scraper.py")
    
    # Ask if user wants to generate invite URL now
    if len(sys.argv) > 1 and sys.argv[1] == '--invite':
        print("\n" + "=" * 40)
        create_bot_invite_url()

if __name__ == '__main__':
    main()
