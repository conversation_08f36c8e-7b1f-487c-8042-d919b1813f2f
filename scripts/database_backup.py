#!/usr/bin/env python3
"""
Database backup and recovery utilities for Web3 Gaming News Tracker
"""
import os
import sys
import json
import gzip
import shutil
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import get_settings
from models.base import SessionLocal, engine
from models.gaming import Article, Source, GamingProject, BlockchainData, NFTCollection
from sqlalchemy import text

settings = get_settings()


class DatabaseBackup:
    """Database backup and recovery manager"""
    
    def __init__(self, backup_dir: str = "backups"):
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        self.db_url = settings.database.url
        
    def create_backup(self, backup_name: Optional[str] = None) -> str:
        """Create a full database backup"""
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"web3_gaming_backup_{timestamp}"
        
        backup_path = self.backup_dir / f"{backup_name}.sql.gz"
        
        print(f"🔄 Creating database backup: {backup_name}")
        
        try:
            if self.db_url.startswith('postgresql'):
                self._backup_postgresql(backup_path)
            elif self.db_url.startswith('sqlite'):
                self._backup_sqlite(backup_path)
            else:
                raise ValueError(f"Unsupported database type: {self.db_url}")
            
            print(f"✅ Backup created successfully: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            print(f"❌ Backup failed: {e}")
            raise
    
    def _backup_postgresql(self, backup_path: Path):
        """Create PostgreSQL backup using pg_dump"""
        # Parse database URL
        from urllib.parse import urlparse
        parsed = urlparse(self.db_url)
        
        env = os.environ.copy()
        env['PGPASSWORD'] = parsed.password
        
        cmd = [
            'pg_dump',
            '-h', parsed.hostname,
            '-p', str(parsed.port or 5432),
            '-U', parsed.username,
            '-d', parsed.path[1:],  # Remove leading slash
            '--no-password',
            '--verbose',
            '--clean',
            '--if-exists'
        ]
        
        # Run pg_dump and compress output
        with gzip.open(backup_path, 'wt') as f:
            result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, 
                                  text=True, env=env)
        
        if result.returncode != 0:
            raise RuntimeError(f"pg_dump failed: {result.stderr}")
    
    def _backup_sqlite(self, backup_path: Path):
        """Create SQLite backup"""
        if self.db_url.startswith('sqlite:///'):
            db_file = self.db_url[10:]  # Remove 'sqlite:///'
        else:
            db_file = self.db_url.split(':///')[-1]
        
        if not os.path.exists(db_file):
            raise FileNotFoundError(f"SQLite database file not found: {db_file}")
        
        # Create compressed backup
        with open(db_file, 'rb') as f_in:
            with gzip.open(backup_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
    
    def restore_backup(self, backup_path: str):
        """Restore database from backup"""
        backup_file = Path(backup_path)
        
        if not backup_file.exists():
            raise FileNotFoundError(f"Backup file not found: {backup_path}")
        
        print(f"🔄 Restoring database from: {backup_path}")
        
        try:
            if self.db_url.startswith('postgresql'):
                self._restore_postgresql(backup_file)
            elif self.db_url.startswith('sqlite'):
                self._restore_sqlite(backup_file)
            else:
                raise ValueError(f"Unsupported database type: {self.db_url}")
            
            print("✅ Database restored successfully")
            
        except Exception as e:
            print(f"❌ Restore failed: {e}")
            raise
    
    def _restore_postgresql(self, backup_path: Path):
        """Restore PostgreSQL backup using psql"""
        from urllib.parse import urlparse
        parsed = urlparse(self.db_url)
        
        env = os.environ.copy()
        env['PGPASSWORD'] = parsed.password
        
        cmd = [
            'psql',
            '-h', parsed.hostname,
            '-p', str(parsed.port or 5432),
            '-U', parsed.username,
            '-d', parsed.path[1:],  # Remove leading slash
            '--no-password',
            '--verbose'
        ]
        
        # Decompress and restore
        with gzip.open(backup_path, 'rt') as f:
            result = subprocess.run(cmd, stdin=f, stderr=subprocess.PIPE, 
                                  text=True, env=env)
        
        if result.returncode != 0:
            raise RuntimeError(f"psql restore failed: {result.stderr}")
    
    def _restore_sqlite(self, backup_path: Path):
        """Restore SQLite backup"""
        if self.db_url.startswith('sqlite:///'):
            db_file = self.db_url[10:]  # Remove 'sqlite:///'
        else:
            db_file = self.db_url.split(':///')[-1]
        
        # Create backup of current database
        if os.path.exists(db_file):
            backup_current = f"{db_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(db_file, backup_current)
            print(f"📋 Current database backed up to: {backup_current}")
        
        # Restore from compressed backup
        with gzip.open(backup_path, 'rb') as f_in:
            with open(db_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
    
    def list_backups(self) -> List[Dict[str, str]]:
        """List available backups"""
        backups = []
        
        for backup_file in self.backup_dir.glob("*.sql.gz"):
            stat = backup_file.stat()
            backups.append({
                'name': backup_file.stem.replace('.sql', ''),
                'path': str(backup_file),
                'size': f"{stat.st_size / 1024 / 1024:.2f} MB",
                'created': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return sorted(backups, key=lambda x: x['created'], reverse=True)
    
    def export_data_json(self, output_file: str):
        """Export database data to JSON format"""
        print(f"🔄 Exporting data to JSON: {output_file}")
        
        db = SessionLocal()
        try:
            data = {
                'export_timestamp': datetime.utcnow().isoformat(),
                'sources': [],
                'articles': [],
                'gaming_projects': [],
                'nft_collections': [],
                'blockchain_data': []
            }
            
            # Export sources
            sources = db.query(Source).all()
            for source in sources:
                data['sources'].append({
                    'id': source.id,
                    'name': source.name,
                    'slug': source.slug,
                    'url': source.url,
                    'source_type': source.source_type,
                    'category': source.category,
                    'is_active': source.is_active,
                    'config': source.config,
                    'created_at': source.created_at.isoformat() if source.created_at else None
                })
            
            # Export articles (limit to recent ones to avoid huge files)
            articles = db.query(Article).order_by(Article.created_at.desc()).limit(10000).all()
            for article in articles:
                data['articles'].append({
                    'id': article.id,
                    'title': article.title,
                    'url': article.url,
                    'source_id': article.source_id,
                    'gaming_category': article.gaming_category,
                    'sentiment_score': article.sentiment_score,
                    'relevance_score': article.relevance_score,
                    'published_at': article.published_at.isoformat() if article.published_at else None,
                    'created_at': article.created_at.isoformat() if article.created_at else None
                })
            
            # Export gaming projects
            projects = db.query(GamingProject).all()
            for project in projects:
                data['gaming_projects'].append({
                    'id': project.id,
                    'name': project.name,
                    'slug': project.slug,
                    'category': project.category,
                    'blockchain': project.blockchain,
                    'token_symbol': project.token_symbol,
                    'market_cap': project.market_cap,
                    'is_active': project.is_active,
                    'created_at': project.created_at.isoformat() if project.created_at else None
                })
            
            # Export NFT collections
            collections = db.query(NFTCollection).all()
            for collection in collections:
                data['nft_collections'].append({
                    'id': collection.id,
                    'name': collection.name,
                    'slug': collection.slug,
                    'contract_address': collection.contract_address,
                    'blockchain': collection.blockchain,
                    'floor_price': collection.floor_price,
                    'is_verified': collection.is_verified,
                    'created_at': collection.created_at.isoformat() if collection.created_at else None
                })
            
            # Write JSON file
            with open(output_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            print(f"✅ Data exported successfully: {output_file}")
            print(f"📊 Exported: {len(data['sources'])} sources, {len(data['articles'])} articles, "
                  f"{len(data['gaming_projects'])} projects, {len(data['nft_collections'])} collections")
            
        finally:
            db.close()
    
    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics"""
        db = SessionLocal()
        try:
            stats = {}
            
            # Count records in each table
            stats['sources'] = db.query(Source).count()
            stats['articles'] = db.query(Article).count()
            stats['gaming_projects'] = db.query(GamingProject).count()
            stats['nft_collections'] = db.query(NFTCollection).count()
            stats['blockchain_data'] = db.query(BlockchainData).count()
            
            # Get database size (PostgreSQL only)
            if self.db_url.startswith('postgresql'):
                try:
                    result = db.execute(text("SELECT pg_size_pretty(pg_database_size(current_database()))"))
                    stats['database_size'] = result.fetchone()[0]
                except:
                    stats['database_size'] = 'Unknown'
            
            return stats
            
        finally:
            db.close()


def main():
    """Main CLI interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Database backup and recovery utilities')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Backup command
    backup_parser = subparsers.add_parser('backup', help='Create database backup')
    backup_parser.add_argument('--name', help='Backup name (optional)')
    
    # Restore command
    restore_parser = subparsers.add_parser('restore', help='Restore database from backup')
    restore_parser.add_argument('backup_path', help='Path to backup file')
    
    # List command
    subparsers.add_parser('list', help='List available backups')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export data to JSON')
    export_parser.add_argument('output_file', help='Output JSON file path')
    
    # Stats command
    subparsers.add_parser('stats', help='Show database statistics')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    backup_manager = DatabaseBackup()
    
    try:
        if args.command == 'backup':
            backup_manager.create_backup(args.name)
        
        elif args.command == 'restore':
            backup_manager.restore_backup(args.backup_path)
        
        elif args.command == 'list':
            backups = backup_manager.list_backups()
            if backups:
                print("📋 Available backups:")
                for backup in backups:
                    print(f"  {backup['name']} - {backup['size']} - {backup['created']}")
            else:
                print("📋 No backups found")
        
        elif args.command == 'export':
            backup_manager.export_data_json(args.output_file)
        
        elif args.command == 'stats':
            stats = backup_manager.get_database_stats()
            print("📊 Database Statistics:")
            for table, count in stats.items():
                print(f"  {table}: {count}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
