#!/usr/bin/env python3
"""
Gaming Analytics Optimization Test Script
Tests the optimized gaming analytics system with CryptoRank API v2 integration
"""
import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, Any

# Test configuration
BASE_URL = "http://localhost:8001"
GAMING_ANALYTICS_BASE = f"{BASE_URL}/api/v1/gaming-analytics"

class GamingAnalyticsOptimizationTest:
    """Test suite for gaming analytics optimization"""
    
    def __init__(self):
        self.session = None
        self.test_results = {}
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_endpoint(self, endpoint: str, test_name: str, timeout: int = 30) -> Dict[str, Any]:
        """Test a single endpoint and measure performance"""
        print(f"🧪 Testing {test_name}...")
        
        start_time = time.time()
        try:
            async with self.session.get(f"{GAMING_ANALYTICS_BASE}{endpoint}", timeout=timeout) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status == 200:
                    data = await response.json()
                    result = {
                        "status": "✅ PASS",
                        "response_time": f"{response_time:.2f}s",
                        "data_size": len(str(data)),
                        "endpoint": endpoint
                    }
                    
                    # Add specific validations
                    if endpoint == "/health":
                        result["protocols_count"] = data.get("supported_protocols", 0)
                    elif endpoint == "/summary":
                        result["total_market_cap"] = data.get("total_market_cap", 0)
                        result["protocols_count"] = data.get("total_protocols", 0)
                    elif endpoint == "/dashboard":
                        result["protocols_data"] = len(data.get("protocols", []))
                        result["market_cap"] = data.get("overview", {}).get("total_market_cap", 0)
                    elif endpoint == "/system-status":
                        result["initialized"] = data.get("system_health", {}).get("initialized", False)
                        result["available_apis"] = len(data.get("system_health", {}).get("available_apis", []))
                    
                    print(f"   ✅ {test_name}: {response_time:.2f}s")
                    return result
                else:
                    print(f"   ❌ {test_name}: HTTP {response.status}")
                    return {
                        "status": f"❌ FAIL (HTTP {response.status})",
                        "response_time": f"{response_time:.2f}s",
                        "endpoint": endpoint
                    }
                    
        except asyncio.TimeoutError:
            print(f"   ⏰ {test_name}: Timeout after {timeout}s")
            return {
                "status": "⏰ TIMEOUT",
                "response_time": f">{timeout}s",
                "endpoint": endpoint
            }
        except Exception as e:
            print(f"   ❌ {test_name}: {str(e)}")
            return {
                "status": f"❌ ERROR: {str(e)}",
                "endpoint": endpoint
            }
    
    async def test_individual_protocol(self, protocol_name: str) -> Dict[str, Any]:
        """Test individual protocol endpoint"""
        endpoint = f"/protocols/{protocol_name}"
        return await self.test_endpoint(endpoint, f"Protocol {protocol_name}", timeout=45)
    
    async def run_comprehensive_test(self):
        """Run comprehensive test suite"""
        print("🚀 Starting Gaming Analytics Optimization Test Suite")
        print("=" * 60)
        
        # Core endpoint tests
        core_tests = [
            ("/health", "Health Check"),
            ("/system-status", "System Status"),
            ("/summary", "Protocol Summary"),
            ("/dashboard", "Dashboard Data"),
            ("/protocols", "Supported Protocols List")
        ]
        
        print("\n📊 Testing Core Endpoints:")
        for endpoint, test_name in core_tests:
            result = await self.test_endpoint(endpoint, test_name)
            self.test_results[test_name] = result
        
        # Individual protocol tests (sample)
        print("\n🎮 Testing Individual Protocols:")
        sample_protocols = ["axie-infinity", "the-sandbox", "decentraland"]
        for protocol in sample_protocols:
            result = await self.test_individual_protocol(protocol)
            self.test_results[f"Protocol {protocol}"] = result
        
        # Performance tests
        print("\n⚡ Performance Tests:")
        await self.test_performance()
        
        # Generate report
        self.generate_report()
    
    async def test_performance(self):
        """Test system performance under load"""
        print("   🔄 Testing concurrent requests...")
        
        # Test concurrent dashboard requests
        start_time = time.time()
        tasks = [
            self.session.get(f"{GAMING_ANALYTICS_BASE}/dashboard", timeout=60)
            for _ in range(5)
        ]
        
        try:
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            successful_requests = sum(1 for r in responses if hasattr(r, 'status') and r.status == 200)
            total_time = end_time - start_time
            
            self.test_results["Concurrent Performance"] = {
                "status": "✅ PASS" if successful_requests >= 4 else "⚠️ PARTIAL",
                "successful_requests": f"{successful_requests}/5",
                "total_time": f"{total_time:.2f}s",
                "avg_time_per_request": f"{total_time/5:.2f}s"
            }
            
            print(f"   ✅ Concurrent test: {successful_requests}/5 successful in {total_time:.2f}s")
            
            # Close responses
            for response in responses:
                if hasattr(response, 'close'):
                    response.close()
                    
        except Exception as e:
            print(f"   ❌ Concurrent test failed: {e}")
            self.test_results["Concurrent Performance"] = {
                "status": f"❌ ERROR: {str(e)}"
            }
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📋 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        passed_tests = 0
        total_tests = len(self.test_results)
        
        for test_name, result in self.test_results.items():
            status = result.get("status", "UNKNOWN")
            response_time = result.get("response_time", "N/A")
            
            print(f"{status:<20} {test_name:<30} {response_time}")
            
            if "✅" in status:
                passed_tests += 1
        
        print("-" * 60)
        print(f"TOTAL: {passed_tests}/{total_tests} tests passed")
        
        # Performance summary
        if "Protocol Summary" in self.test_results:
            summary_result = self.test_results["Protocol Summary"]
            if "total_market_cap" in summary_result:
                print(f"Total Market Cap: ${summary_result['total_market_cap']:,.2f}")
            if "protocols_count" in summary_result:
                print(f"Active Protocols: {summary_result['protocols_count']}")
        
        print(f"Test completed at: {datetime.now().isoformat()}")
        print("=" * 60)

async def main():
    """Main test execution"""
    async with GamingAnalyticsOptimizationTest() as test_suite:
        await test_suite.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
