//jQuery time
var current_fs, next_fs, previous_fs; //fieldsets
var left, opacity, scale; //fieldset properties which we will animate
var animating; //flag to prevent quick multi-click glitches

// Form validation functions
function validateCurrentFieldset(fieldset) {
  let isValid = true;
  let errorMessages = [];

  // Check required fields
  fieldset.find('input[required]').each(function() {
    if (!$(this).val().trim()) {
      isValid = false;
      $(this).addClass('error');
      errorMessages.push('Project Name is required');
    } else {
      $(this).removeClass('error');
    }
  });

  // Validate email format if email field exists
  fieldset.find('input[type="email"]').each(function() {
    const email = $(this).val().trim();
    if (email && !isValidEmail(email)) {
      isValid = false;
      $(this).addClass('error');
      errorMessages.push('Please enter a valid email address');
    } else {
      $(this).removeClass('error');
    }
  });

  // Validate URL format for website fields
  fieldset.find('input[name*="link"], input[name*="Link"], input[name*="Website"]').each(function() {
    const url = $(this).val().trim();
    if (url && !isValidURL(url)) {
      isValid = false;
      $(this).addClass('error');
      errorMessages.push('Please enter a valid URL (include http:// or https://)');
    } else {
      $(this).removeClass('error');
    }
  });

  // Show validation errors
  if (!isValid) {
    showValidationErrors(errorMessages);
  }

  return isValid;
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidURL(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function showValidationErrors(errors) {
  // Remove existing error messages
  $('.validation-errors').remove();

  if (errors.length > 0) {
    const errorHtml = '<div class="validation-errors"><ul>' +
      errors.map(error => '<li>' + error + '</li>').join('') +
      '</ul></div>';
    current_fs.prepend(errorHtml);

    // Auto-hide after 5 seconds
    setTimeout(() => {
      $('.validation-errors').fadeOut();
    }, 5000);
  }
}

$(".next").click(function () {
  if (animating) return false;

  current_fs = $(this).parent();

  // Validate current fieldset before proceeding
  if (!validateCurrentFieldset(current_fs)) {
    return false;
  }

  animating = true;
  next_fs = $(this).parent().next();

  //activate next step on progressbar using the index of next_fs
  $("#progressbar li").eq($("fieldset").index(next_fs)).addClass("active");

  //show the next fieldset
  next_fs.show();
  //hide the current fieldset with style
  current_fs.animate(
    { opacity: 0 },
    {
      step: function (now) {
        //as the opacity of current_fs reduces to 0 - stored in "now"
        //1. scale current_fs down to 80%
        scale = 1 - (1 - now) * 0.2;
        //2. bring next_fs from the right(50%)
        left = now * 50 + "%";
        //3. increase opacity of next_fs to 1 as it moves in
        opacity = 1 - now;
        current_fs.css({
          transform: "scale(" + scale + ")",
          position: "absolute"
        });
        next_fs.css({ left: left, opacity: opacity });
      },
      duration: 800,
      complete: function () {
        current_fs.hide();
        animating = false;
      },
      //this comes from the custom easing plugin
      easing: "easeInOutBack"
    }
  );
});

$(".previous").click(function () {
  if (animating) return false;
  animating = true;

  current_fs = $(this).parent();
  previous_fs = $(this).parent().prev();

  //de-activate current step on progressbar
  $("#progressbar li")
    .eq($("fieldset").index(current_fs))
    .removeClass("active");

  //show the previous fieldset
  previous_fs.show();
  //hide the current fieldset with style
  current_fs.animate(
    { opacity: 0 },
    {
      step: function (now) {
        //as the opacity of current_fs reduces to 0 - stored in "now"
        //1. scale previous_fs from 80% to 100%
        scale = 0.8 + (1 - now) * 0.2;
        //2. take current_fs to the right(50%) - from 0%
        left = (1 - now) * 50 + "%";
        //3. increase opacity of previous_fs to 1 as it moves in
        opacity = 1 - now;
        current_fs.css({ left: left });
        previous_fs.css({
          transform: "scale(" + scale + ")",
          opacity: opacity
        });
      },
      duration: 800,
      complete: function () {
        current_fs.hide();
        animating = false;
      },
      //this comes from the custom easing plugin
      easing: "easeInOutBack"
    }
  );
});

// Data collection and CSV export functionality
function collectFormData() {
  const formData = {};

  // Add timestamp and email placeholder
  formData['Timestamp'] = new Date().toISOString();
  formData['Email Address'] = ''; // Can be filled later if needed

  // Collect all input fields
  $('input[type="text"], input[type="email"], input[type="url"]').each(function() {
    const name = $(this).attr('name');
    const value = $(this).val().trim();
    if (name && value) {
      formData[name] = value;
    }
  });

  // Collect radio button selections
  $('input[type="radio"]:checked').each(function() {
    const name = $(this).attr('name');
    const value = $(this).val();
    if (name) {
      formData[name] = value;
    }
  });

  // Collect checkbox selections (multiple values for same name)
  const checkboxGroups = {};
  $('input[type="checkbox"]:checked').each(function() {
    const name = $(this).attr('name');
    const value = $(this).val();
    if (name) {
      if (!checkboxGroups[name]) {
        checkboxGroups[name] = [];
      }
      checkboxGroups[name].push(value);
    }
  });

  // Convert checkbox arrays to comma-separated strings
  Object.keys(checkboxGroups).forEach(name => {
    formData[name] = checkboxGroups[name].join(', ');
  });

  // Collect select dropdown values
  $('select').each(function() {
    const name = $(this).attr('name');
    const value = $(this).val();
    if (name && value) {
      formData[name] = value;
    }
  });

  return formData;
}

function generateCSVRow(data) {
  // Define the expected CSV column order based on the original CSV
  const csvColumns = [
    'Timestamp', 'Email Address', 'Project Name', 'Project Website Link', 'Whitepaper Link',
    'Blockchain', 'Validated Game Status', 'Game Status Notes', 'Token Schedule Link',
    'Token 1 - Symbol', 'Token 1 - Token Contract Address', 'Token 1 - Type',
    'Token 1 Link to CoinGecko Listing', 'Token 2 - Symbol', 'Token 2 - Token Contract Address',
    'Token 2 - Type', 'Token 2 Link to CoinGecko Listing', 'Which genre best fits this title? Choose only ONE',
    'Choose ONE Action sub-genre that best fits this game', 'Choose ONE Action Adventure sub-genre that best fits this game',
    'Choose ONE Action RPG sub-genre that best fits this game', 'Choose ONE Adventure sub-genre that best fits this game',
    'Choose ONE Battle Royale sub-genre that best fits this game', 'Choose ONE Casual sub-genre that best fits this game',
    'Choose ONE Fighting sub-genre that best fits this game', 'Choose ONE FPS sub-genre that best fits this game',
    'Choose ONE MMORPG sub-genre that best fits this game', 'Choose ONE Party sub-genre that best fits this game',
    'Choose ONE Platformer sub-genre that best fits this game', 'Choose ONE Puzzle sub-genre that best fits this game',
    'Choose ONE Racing sub-genre that best fits this game', 'Choose ONE RTS sub-genre that best fits this game',
    'Choose ONE RPG sub-genre that best fits this game', 'Choose ONE Shooter sub-genre that best fits this game',
    'Choose ONE Simulation sub-genre that best fits this game', 'Choose ONE Sports sub-genre that best fits this game',
    'Choose ONE stealth sub-genre that best fits this game', 'Choose ONE Strategy sub-genre that best fits this game',
    'Choose ONE Survival sub-genre that best fits this game', 'Choose ONE Tactical RPG sub-genre that best fits this game',
    'Does this project involve NFTs?', 'Marketplace link for NFTs for this game',
    'Game Style (check all that apply)', 'Twitter (X) link', 'Discord Server Link',
    'Telegram Channel Link', 'Medium Link', 'Youtube Channel', 'LinkedIn',
    'Token 1 Blockchain Scanner link', 'Token 2 - Blockchain Scanner Link',
    'Blockchain scanner link of NFT Contract Address', 'What are the NFTs used for?',
    'NFT Marketplace Links', 'On the average, how many daily active users (DAU) in the past month?',
    'Link to Source of DAU count', 'On the average, how many daily unique active wallets (UAW) in the past month?',
    'Link to Source of UAW count', 'What is the NFT Contract Address?',
    'Does this game include NFTs?', 'What is the NFT Contract Address?',
    'Blockchain scanner link of NFT Contract Address', 'Marketplace link for NFTs for this game',
    'What is the function of the NFTs?', 'Auto-Clicker Telegram Invite Link',
    'Auto-Clicker Telegram Community Channel Link', 'Auto-Clicker Telegram Bot Address',
    'Notes and Comments', 'Auto-Clicker TG Membership Population',
    'Auto-Clicker Twitter (X) Page', 'Auto-Clicker Discord invite link',
    'Auto-Clicker Medium Link', 'Auto-Clicker YouTube Link',
    'Additional Notes and Comments', 'Does this Auto-Clicker have an official Token?',
    'Facebook Page', 'Additional Tokens', 'Reddit - Subreddit or User',
    'How many wallets are holding this NFT?', 'NFT name',
    'Source for number of NFT-holding wallets', 'Token 1 Total Supply',
    'Token 2 Total Supply', 'Developer', 'Platform', 'Notes, Additional Tokens'
  ];

  // Create CSV row with proper escaping
  const csvRow = csvColumns.map(column => {
    const value = data[column] || '';
    // Escape quotes and wrap in quotes if contains comma, quote, or newline
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return '"' + value.replace(/"/g, '""') + '"';
    }
    return value;
  }).join(',');

  return csvRow;
}

function downloadCSV(csvContent, filename) {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

function submitForm() {
  const formData = collectFormData();

  // Validate required fields
  if (!formData['Project Name']) {
    alert('Project Name is required!');
    return false;
  }

  // Generate CSV row
  const csvRow = generateCSVRow(formData);

  // Create filename with timestamp
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  const filename = `gaming_project_${formData['Project Name'].replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.csv`;

  // Download CSV file
  downloadCSV(csvRow, filename);

  // Show success message
  showSuccessMessage();

  // Optional: Send to backend API
  sendToBackend(formData);

  return true;
}

function showSuccessMessage() {
  const successHtml = `
    <div class="success-message">
      <h3>✅ Form Submitted Successfully!</h3>
      <p>Your gaming project data has been exported to CSV format.</p>
      <p>The file has been downloaded to your computer.</p>
    </div>
  `;

  $('.form-container').html(successHtml);
}

function sendToBackend(formData) {
  // Optional: Send data to backend API endpoint
  fetch('/api/gaming-projects', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(formData)
  })
  .then(response => response.json())
  .then(data => {
    console.log('Data sent to backend successfully:', data);
  })
  .catch(error => {
    console.error('Error sending data to backend:', error);
  });
}

// Initialize form functionality when document is ready
$(document).ready(function() {
  // Add submit button functionality to the last fieldset
  const lastFieldset = $('fieldset').last();

  // Replace the "Next" button with "Submit" button in the last fieldset
  lastFieldset.find('.next').text('Submit Form').removeClass('next').addClass('submit');

  // Add submit event handler
  $(document).on('click', '.submit', function(e) {
    e.preventDefault();

    // Validate current fieldset
    if (!validateCurrentFieldset($(this).parent())) {
      return false;
    }

    // Submit the form
    submitForm();
  });

  // Add real-time validation for required fields
  $('input[required]').on('blur', function() {
    if (!$(this).val().trim()) {
      $(this).addClass('error');
    } else {
      $(this).removeClass('error');
    }
  });

  // Add URL validation for link fields
  $('input[name*="link"], input[name*="Link"], input[name*="Website"]').on('blur', function() {
    const url = $(this).val().trim();
    if (url && !isValidURL(url)) {
      $(this).addClass('error');
      $(this).attr('title', 'Please enter a valid URL (include http:// or https://)');
    } else {
      $(this).removeClass('error');
      $(this).removeAttr('title');
    }
  });

  // Auto-save form data to localStorage
  $('input, select').on('change', function() {
    saveFormProgress();
  });

  // Load saved form data on page load
  loadFormProgress();
});

function saveFormProgress() {
  const formData = collectFormData();
  localStorage.setItem('gamingFormProgress', JSON.stringify(formData));
}

function loadFormProgress() {
  const savedData = localStorage.getItem('gamingFormProgress');
  if (savedData) {
    try {
      const formData = JSON.parse(savedData);

      // Restore form values
      Object.keys(formData).forEach(name => {
        const value = formData[name];

        // Handle text inputs
        $(`input[name="${name}"]`).val(value);

        // Handle radio buttons
        $(`input[name="${name}"][value="${value}"]`).prop('checked', true);

        // Handle checkboxes (comma-separated values)
        if (value.includes(',')) {
          const values = value.split(', ');
          values.forEach(val => {
            $(`input[name="${name}"][value="${val}"]`).prop('checked', true);
          });
        } else {
          $(`input[name="${name}"][value="${value}"]`).prop('checked', true);
        }

        // Handle select dropdowns
        $(`select[name="${name}"]`).val(value);
      });

      console.log('Form progress restored from localStorage');
    } catch (error) {
      console.error('Error loading form progress:', error);
    }
  }
}
