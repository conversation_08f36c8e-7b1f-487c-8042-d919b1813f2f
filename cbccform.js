//jQuery time
var current_fs, next_fs, previous_fs; //fieldsets
var left, opacity, scale; //fieldset properties which we will animate
var animating; //flag to prevent quick multi-click glitches

// Form validation functions
function validateCurrentFieldset(fieldset) {
  let isValid = true;
  let errorMessages = [];

  // Check required fields
  fieldset.find('input[required]').each(function() {
    if (!$(this).val().trim()) {
      isValid = false;
      $(this).addClass('error');
      errorMessages.push('Project Name is required');
    } else {
      $(this).removeClass('error');
    }
  });

  // Validate email format if email field exists
  fieldset.find('input[type="email"]').each(function() {
    const email = $(this).val().trim();
    if (email && !isValidEmail(email)) {
      isValid = false;
      $(this).addClass('error');
      errorMessages.push('Please enter a valid email address');
    } else {
      $(this).removeClass('error');
    }
  });

  // Validate URL format for website fields
  fieldset.find('input[name*="link"], input[name*="Link"], input[name*="Website"]').each(function() {
    const url = $(this).val().trim();
    if (url && !isValidURL(url)) {
      isValid = false;
      $(this).addClass('error');
      errorMessages.push('Please enter a valid URL (include http:// or https://)');
    } else {
      $(this).removeClass('error');
    }
  });

  // Show validation errors
  if (!isValid) {
    showValidationErrors(errorMessages);
  }

  return isValid;
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidURL(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function showValidationErrors(errors) {
  // Remove existing error messages
  $('.validation-errors').remove();

  if (errors.length > 0) {
    const errorHtml = '<div class="validation-errors"><ul>' +
      errors.map(error => '<li>' + error + '</li>').join('') +
      '</ul></div>';
    current_fs.prepend(errorHtml);

    // Auto-hide after 5 seconds
    setTimeout(() => {
      $('.validation-errors').fadeOut();
    }, 5000);
  }
}

$(".next").click(function () {
  if (animating) return false;

  current_fs = $(this).parent();

  // Validate current fieldset before proceeding
  if (!validateCurrentFieldset(current_fs)) {
    return false;
  }

  animating = true;
  next_fs = $(this).parent().next();

  //activate next step on progressbar using the index of next_fs
  $("#progressbar li").eq($("fieldset").index(next_fs)).addClass("active");

  //show the next fieldset
  next_fs.show();
  //hide the current fieldset with style
  current_fs.animate(
    { opacity: 0 },
    {
      step: function (now) {
        //as the opacity of current_fs reduces to 0 - stored in "now"
        //1. scale current_fs down to 80%
        scale = 1 - (1 - now) * 0.2;
        //2. bring next_fs from the right(50%)
        left = now * 50 + "%";
        //3. increase opacity of next_fs to 1 as it moves in
        opacity = 1 - now;
        current_fs.css({
          transform: "scale(" + scale + ")",
          position: "absolute"
        });
        next_fs.css({ left: left, opacity: opacity });
      },
      duration: 800,
      complete: function () {
        current_fs.hide();
        animating = false;
      },
      //this comes from the custom easing plugin
      easing: "easeInOutBack"
    }
  );
});

$(".previous").click(function () {
  if (animating) return false;
  animating = true;

  current_fs = $(this).parent();
  previous_fs = $(this).parent().prev();

  //de-activate current step on progressbar
  $("#progressbar li")
    .eq($("fieldset").index(current_fs))
    .removeClass("active");

  //show the previous fieldset
  previous_fs.show();
  //hide the current fieldset with style
  current_fs.animate(
    { opacity: 0 },
    {
      step: function (now) {
        //as the opacity of current_fs reduces to 0 - stored in "now"
        //1. scale previous_fs from 80% to 100%
        scale = 0.8 + (1 - now) * 0.2;
        //2. take current_fs to the right(50%) - from 0%
        left = (1 - now) * 50 + "%";
        //3. increase opacity of previous_fs to 1 as it moves in
        opacity = 1 - now;
        current_fs.css({ left: left });
        previous_fs.css({
          transform: "scale(" + scale + ")",
          opacity: opacity
        });
      },
      duration: 800,
      complete: function () {
        current_fs.hide();
        animating = false;
      },
      //this comes from the custom easing plugin
      easing: "easeInOutBack"
    }
  );
});

// Data collection and CSV export functionality
function collectFormData() {
  const formData = {};

  // Add timestamp and email placeholder
  formData['Timestamp'] = new Date().toISOString();
  formData['Email Address'] = ''; // Can be filled later if needed

  // Collect all input fields
  $('input[type="text"], input[type="email"], input[type="url"]').each(function() {
    const name = $(this).attr('name');
    const value = $(this).val().trim();
    if (name && value) {
      formData[name] = value;
    }
  });

  // Collect radio button selections
  $('input[type="radio"]:checked').each(function() {
    const name = $(this).attr('name');
    const value = $(this).val();
    if (name) {
      formData[name] = value;
    }
  });

  // Collect checkbox selections (multiple values for same name)
  const checkboxGroups = {};
  $('input[type="checkbox"]:checked').each(function() {
    const name = $(this).attr('name');
    const value = $(this).val();
    if (name) {
      if (!checkboxGroups[name]) {
        checkboxGroups[name] = [];
      }
      checkboxGroups[name].push(value);
    }
  });

  // Convert checkbox arrays to comma-separated strings
  Object.keys(checkboxGroups).forEach(name => {
    formData[name] = checkboxGroups[name].join(', ');
  });

  // Collect select dropdown values
  $('select').each(function() {
    const name = $(this).attr('name');
    const value = $(this).val();
    if (name && value) {
      formData[name] = value;
    }
  });

  return formData;
}

function generateCSVRow(data) {
  // Define the expected CSV column order based on the original CSV
  const csvColumns = [
    'Timestamp', 'Email Address', 'Project Name', 'Project Website Link', 'Whitepaper Link',
    'Blockchain', 'Validated Game Status', 'Game Status Notes', 'Token Schedule Link',
    'Token 1 - Symbol', 'Token 1 - Token Contract Address', 'Token 1 - Type',
    'Token 1 Link to CoinGecko Listing', 'Token 2 - Symbol', 'Token 2 - Token Contract Address',
    'Token 2 - Type', 'Token 2 Link to CoinGecko Listing', 'Which genre best fits this title? Choose only ONE',
    'Choose ONE Action sub-genre that best fits this game', 'Choose ONE Action Adventure sub-genre that best fits this game',
    'Choose ONE Action RPG sub-genre that best fits this game', 'Choose ONE Adventure sub-genre that best fits this game',
    'Choose ONE Battle Royale sub-genre that best fits this game', 'Choose ONE Casual sub-genre that best fits this game',
    'Choose ONE Fighting sub-genre that best fits this game', 'Choose ONE FPS sub-genre that best fits this game',
    'Choose ONE MMORPG sub-genre that best fits this game', 'Choose ONE Party sub-genre that best fits this game',
    'Choose ONE Platformer sub-genre that best fits this game', 'Choose ONE Puzzle sub-genre that best fits this game',
    'Choose ONE Racing sub-genre that best fits this game', 'Choose ONE RTS sub-genre that best fits this game',
    'Choose ONE RPG sub-genre that best fits this game', 'Choose ONE Shooter sub-genre that best fits this game',
    'Choose ONE Simulation sub-genre that best fits this game', 'Choose ONE Sports sub-genre that best fits this game',
    'Choose ONE stealth sub-genre that best fits this game', 'Choose ONE Strategy sub-genre that best fits this game',
    'Choose ONE Survival sub-genre that best fits this game', 'Choose ONE Tactical RPG sub-genre that best fits this game',
    'Does this project involve NFTs?', 'Marketplace link for NFTs for this game',
    'Game Style (check all that apply)', 'Twitter (X) link', 'Discord Server Link',
    'Telegram Channel Link', 'Medium Link', 'Youtube Channel', 'LinkedIn',
    'Token 1 Blockchain Scanner link', 'Token 2 - Blockchain Scanner Link',
    'Blockchain scanner link of NFT Contract Address', 'What are the NFTs used for?',
    'NFT Marketplace Links', 'On the average, how many daily active users (DAU) in the past month?',
    'Link to Source of DAU count', 'On the average, how many daily unique active wallets (UAW) in the past month?',
    'Link to Source of UAW count', 'What is the NFT Contract Address?',
    'Does this game include NFTs?', 'What is the NFT Contract Address?',
    'Blockchain scanner link of NFT Contract Address', 'Marketplace link for NFTs for this game',
    'What is the function of the NFTs?', 'Auto-Clicker Telegram Invite Link',
    'Auto-Clicker Telegram Community Channel Link', 'Auto-Clicker Telegram Bot Address',
    'Notes and Comments', 'Auto-Clicker TG Membership Population',
    'Auto-Clicker Twitter (X) Page', 'Auto-Clicker Discord invite link',
    'Auto-Clicker Medium Link', 'Auto-Clicker YouTube Link',
    'Additional Notes and Comments', 'Does this Auto-Clicker have an official Token?',
    'Facebook Page', 'Additional Tokens', 'Reddit - Subreddit or User',
    'How many wallets are holding this NFT?', 'NFT name',
    'Source for number of NFT-holding wallets', 'Token 1 Total Supply',
    'Token 2 Total Supply', 'Developer', 'Platform', 'Notes, Additional Tokens'
  ];

  // Create CSV row with proper escaping
  const csvRow = csvColumns.map(column => {
    const value = data[column] || '';
    // Escape quotes and wrap in quotes if contains comma, quote, or newline
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return '"' + value.replace(/"/g, '""') + '"';
    }
    return value;
  }).join(',');

  return csvRow;
}

function downloadCSV(csvContent, filename) {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

async function submitForm() {
  const formData = collectFormData();

  // Validate required fields
  if (!formData['Project Name']) {
    alert('Project Name is required!');
    return false;
  }

  try {
    // Primary method: Send to backend API
    await sendToBackend(formData);
    return true;

  } catch (error) {
    console.error('Backend submission failed, falling back to CSV download:', error);

    // Fallback method: Download CSV file
    const csvRow = generateCSVRow(formData);
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `gaming_project_${formData['Project Name'].replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.csv`;

    downloadCSV(csvRow, filename);
    showSuccessMessage();

    return false; // Indicate backend submission failed
  }
}

function showSuccessMessage() {
  const successHtml = `
    <div class="success-message">
      <h3>✅ Form Submitted Successfully!</h3>
      <p>Your gaming project data has been exported to CSV format.</p>
      <p>The file has been downloaded to your computer.</p>
    </div>
  `;

  $('.form-container').html(successHtml);
}

async function sendToBackend(formData) {
  // Send data to backend API endpoint
  try {
    showLoadingMessage('Submitting form data...');

    const response = await fetch('/api/v1/gaming-form/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Data sent to backend successfully:', data);

    // Show enhanced success message with backend confirmation
    showEnhancedSuccessMessage(data);

    return data;

  } catch (error) {
    console.error('❌ Error sending data to backend:', error);
    showErrorMessage(`Failed to submit form: ${error.message}`);
    throw error;
  }
}

function showLoadingMessage(message) {
  const loadingHtml = `
    <div class="loading-message">
      <div class="spinner"></div>
      <h3>🔄 ${message}</h3>
      <p>Please wait while we process your submission...</p>
    </div>
  `;

  $('.form-container').html(loadingHtml);
}

function showEnhancedSuccessMessage(responseData) {
  const successHtml = `
    <div class="success-message">
      <h3>✅ Form Submitted Successfully!</h3>
      <p><strong>Project:</strong> ${responseData.project_name}</p>
      <p><strong>Status:</strong> ${responseData.message}</p>
      <div class="success-details">
        <p>✓ CSV file updated: ${responseData.csv_row_added ? 'Yes' : 'No'}</p>
        <p>✓ Configuration reloaded: ${responseData.config_reloaded ? 'Yes' : 'No'}</p>
        <p>✓ Timestamp: ${new Date(responseData.timestamp).toLocaleString()}</p>
      </div>
      <div class="success-actions">
        <button onclick="location.reload()" class="action-button">Add Another Project</button>
        <button onclick="window.open('/api/v1/gaming-config/projects', '_blank')" class="action-button">View All Projects</button>
      </div>
    </div>
  `;

  $('.form-container').html(successHtml);
}

function showErrorMessage(message) {
  const errorHtml = `
    <div class="error-message">
      <h3>❌ Submission Failed</h3>
      <p>${message}</p>
      <div class="error-actions">
        <button onclick="location.reload()" class="action-button">Try Again</button>
        <button onclick="downloadCSVFallback()" class="action-button">Download CSV Instead</button>
      </div>
    </div>
  `;

  $('.form-container').html(errorHtml);
}

function downloadCSVFallback() {
  // Fallback to CSV download if backend submission fails
  const formData = collectFormData();
  const csvRow = generateCSVRow(formData);
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  const filename = `gaming_project_${formData['Project Name'].replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.csv`;
  downloadCSV(csvRow, filename);
}

async function validateProjectName(projectName, inputElement) {
  try {
    const response = await fetch(`/api/v1/gaming-form/validate-project/${encodeURIComponent(projectName)}`);
    const data = await response.json();

    if (data.exists) {
      inputElement.addClass('error');
      inputElement.attr('title', `Project "${projectName}" already exists. Please choose a different name.`);

      // Show warning message
      const warningHtml = `
        <div class="validation-warning">
          <p>⚠️ Project "${projectName}" already exists in the database.</p>
          <p>Existing project: ${data.existing_project.name} (${data.existing_project.blockchain})</p>
        </div>
      `;

      // Remove existing warnings and add new one
      inputElement.parent().find('.validation-warning').remove();
      inputElement.after(warningHtml);

      return false;
    } else {
      inputElement.removeClass('error');
      inputElement.removeAttr('title');
      inputElement.parent().find('.validation-warning').remove();
      return true;
    }

  } catch (error) {
    console.error('Error validating project name:', error);
    // Don't block submission if validation fails
    return true;
  }
}

// Initialize form functionality when document is ready
$(document).ready(function() {
  // Add submit button functionality to the last fieldset
  const lastFieldset = $('fieldset').last();

  // Replace the "Next" button with "Submit" button in the last fieldset
  lastFieldset.find('.next').text('Submit Form').removeClass('next').addClass('submit');

  // Add submit event handler
  $(document).on('click', '.submit', async function(e) {
    e.preventDefault();

    // Validate current fieldset
    if (!validateCurrentFieldset($(this).parent())) {
      return false;
    }

    // Disable submit button to prevent double submission
    $(this).prop('disabled', true).text('Submitting...');

    try {
      // Submit the form
      await submitForm();
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      // Re-enable submit button (though form will likely be replaced with success message)
      $(this).prop('disabled', false).text('Submit Form');
    }
  });

  // Add real-time validation for required fields
  $('input[required]').on('blur', function() {
    if (!$(this).val().trim()) {
      $(this).addClass('error');
    } else {
      $(this).removeClass('error');
    }
  });

  // Add project name validation
  $('input[name="Project Name"]').on('blur', async function() {
    const projectName = $(this).val().trim();
    if (projectName) {
      await validateProjectName(projectName, $(this));
    }
  });

  // Add URL validation for link fields
  $('input[name*="link"], input[name*="Link"], input[name*="Website"]').on('blur', function() {
    const url = $(this).val().trim();
    if (url && !isValidURL(url)) {
      $(this).addClass('error');
      $(this).attr('title', 'Please enter a valid URL (include http:// or https://)');
    } else {
      $(this).removeClass('error');
      $(this).removeAttr('title');
    }
  });

  // Auto-save form data to localStorage
  $('input, select').on('change', function() {
    saveFormProgress();
  });

  // Load saved form data on page load
  loadFormProgress();
});

function saveFormProgress() {
  const formData = collectFormData();
  localStorage.setItem('gamingFormProgress', JSON.stringify(formData));
}

function loadFormProgress() {
  const savedData = localStorage.getItem('gamingFormProgress');
  if (savedData) {
    try {
      const formData = JSON.parse(savedData);

      // Restore form values
      Object.keys(formData).forEach(name => {
        const value = formData[name];

        // Handle text inputs
        $(`input[name="${name}"]`).val(value);

        // Handle radio buttons
        $(`input[name="${name}"][value="${value}"]`).prop('checked', true);

        // Handle checkboxes (comma-separated values)
        if (value.includes(',')) {
          const values = value.split(', ');
          values.forEach(val => {
            $(`input[name="${name}"][value="${val}"]`).prop('checked', true);
          });
        } else {
          $(`input[name="${name}"][value="${value}"]`).prop('checked', true);
        }

        // Handle select dropdowns
        $(`select[name="${name}"]`).val(value);
      });

      console.log('Form progress restored from localStorage');
    } catch (error) {
      console.error('Error loading form progress:', error);
    }
  }
}
