"""Advanced gaming performance indexes for Phase 6 optimization

Revision ID: 2025_07_07_001
Revises: 3d931947043d
Create Date: 2025-07-07 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2025_07_07_001'
down_revision = '3d931947043d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add advanced performance indexes for gaming data queries"""
    
    # === ARTICLES TABLE OPTIMIZATIONS ===
    
    # Dashboard overview queries - articles by time periods
    op.create_index(
        'idx_articles_published_at_desc', 
        'articles', 
        [sa.text('published_at DESC')],
        postgresql_where=sa.text('published_at IS NOT NULL')
    )
    
    # Gaming category filtering with time-based queries
    op.create_index(
        'idx_articles_gaming_category_published_desc', 
        'articles', 
        ['gaming_category', sa.text('published_at DESC')],
        postgresql_where=sa.text('gaming_category IS NOT NULL AND published_at IS NOT NULL')
    )
    
    # Source-based queries with time filtering
    op.create_index(
        'idx_articles_source_published_relevance', 
        'articles', 
        ['source_id', sa.text('published_at DESC'), sa.text('relevance_score DESC NULLS LAST')]
    )
    
    # Sentiment and relevance scoring for analytics
    op.create_index(
        'idx_articles_sentiment_relevance_published', 
        'articles', 
        [sa.text('sentiment_score DESC NULLS LAST'), sa.text('relevance_score DESC NULLS LAST'), sa.text('published_at DESC')]
    )
    
    # Processing status queries for data pipeline
    op.create_index(
        'idx_articles_processing_status', 
        'articles', 
        ['is_processed', 'is_duplicate', sa.text('created_at DESC')]
    )
    
    # JSON field optimizations for gaming metadata
    op.create_index(
        'idx_articles_gaming_projects_gin', 
        'articles', 
        ['gaming_projects'], 
        postgresql_using='gin',
        postgresql_where=sa.text('gaming_projects IS NOT NULL')
    )
    
    op.create_index(
        'idx_articles_gaming_tokens_gin', 
        'articles', 
        ['gaming_tokens'], 
        postgresql_using='gin',
        postgresql_where=sa.text('gaming_tokens IS NOT NULL')
    )
    
    # === GAMING PROJECTS TABLE OPTIMIZATIONS ===
    
    # Project lookup by name (most common query)
    op.create_index(
        'idx_gaming_projects_name_lower', 
        'gaming_projects', 
        [sa.text('LOWER(project_name)')]
    )
    
    # Active projects filtering
    op.create_index(
        'idx_gaming_projects_active_blockchain', 
        'gaming_projects', 
        ['is_active', 'blockchain'],
        postgresql_where=sa.text('is_active = true')
    )
    
    # Gaming category and blockchain filtering
    op.create_index(
        'idx_gaming_projects_category_blockchain_active', 
        'gaming_projects', 
        ['category', 'blockchain', 'is_active']
    )
    
    # Token-related queries
    op.create_index(
        'idx_gaming_projects_token_info', 
        'gaming_projects', 
        ['token_symbol', 'token_address'],
        postgresql_where=sa.text('token_symbol IS NOT NULL')
    )
    
    # === BLOCKCHAIN DATA TABLE OPTIMIZATIONS ===
    
    # Time-series queries for blockchain events
    op.create_index(
        'idx_blockchain_data_timestamp_desc', 
        'blockchain_data', 
        [sa.text('block_timestamp DESC')],
        postgresql_where=sa.text('block_timestamp IS NOT NULL')
    )
    
    # Contract-specific event queries
    op.create_index(
        'idx_blockchain_data_contract_timestamp', 
        'blockchain_data', 
        ['contract_address', sa.text('block_timestamp DESC')],
        postgresql_where=sa.text('contract_address IS NOT NULL AND block_timestamp IS NOT NULL')
    )
    
    # Cross-chain analysis queries
    op.create_index(
        'idx_blockchain_data_blockchain_event_timestamp', 
        'blockchain_data', 
        ['blockchain', 'event_type', sa.text('block_timestamp DESC')]
    )
    
    # Gaming project blockchain data correlation
    op.create_index(
        'idx_blockchain_data_gaming_project_timestamp', 
        'blockchain_data', 
        ['gaming_project_id', sa.text('block_timestamp DESC')],
        postgresql_where=sa.text('gaming_project_id IS NOT NULL')
    )
    
    # === SOCIAL MEDIA OPTIMIZATIONS ===
    
    # Twitter posts - gaming relevance and engagement
    op.create_index(
        'idx_twitter_posts_gaming_engagement', 
        'twitter_posts', 
        ['is_gaming_related', sa.text('engagement_score DESC NULLS LAST'), sa.text('created_at DESC')],
        postgresql_where=sa.text('is_gaming_related = true')
    )
    
    # Reddit posts - quality threshold and gaming relevance
    op.create_index(
        'idx_reddit_posts_quality_gaming', 
        'reddit_posts', 
        ['meets_quality_threshold', 'is_gaming_related', sa.text('score DESC'), sa.text('created_utc DESC')],
        postgresql_where=sa.text('meets_quality_threshold = true AND is_gaming_related = true')
    )
    
    # === NFT COLLECTIONS OPTIMIZATIONS ===
    
    # Floor price tracking queries
    op.create_index(
        'idx_nft_collections_floor_price_desc', 
        'nft_collections', 
        [sa.text('floor_price DESC NULLS LAST'), 'blockchain'],
        postgresql_where=sa.text('floor_price IS NOT NULL AND is_active = true')
    )
    
    # Volume tracking for analytics
    op.create_index(
        'idx_nft_collections_volume_24h_desc', 
        'nft_collections', 
        [sa.text('volume_24h DESC NULLS LAST'), 'gaming_category'],
        postgresql_where=sa.text('volume_24h IS NOT NULL AND is_active = true')
    )
    
    # === GAMING CONTRACTS OPTIMIZATIONS ===
    
    # Gaming contract detection queries
    op.create_index(
        'idx_gaming_contracts_detection', 
        'gaming_contracts', 
        ['is_gaming', 'confidence_level', sa.text('confidence_score DESC NULLS LAST')]
    )
    
    # Monitoring status queries
    op.create_index(
        'idx_gaming_contracts_monitoring', 
        'gaming_contracts', 
        ['is_monitored', 'blockchain', sa.text('last_activity DESC NULLS LAST')]
    )
    
    # === SOURCES TABLE OPTIMIZATIONS ===
    
    # Active source scheduling queries
    op.create_index(
        'idx_sources_scraping_schedule', 
        'sources', 
        ['is_active', 'scrape_frequency', sa.text('last_scraped_at ASC NULLS FIRST')],
        postgresql_where=sa.text('is_active = true')
    )
    
    # Source reliability and performance tracking
    op.create_index(
        'idx_sources_reliability_performance', 
        'sources', 
        [sa.text('reliability_score DESC NULLS LAST'), sa.text('article_count DESC'), 'source_type']
    )


def downgrade() -> None:
    """Remove advanced performance indexes"""
    
    # Drop all the indexes we created
    indexes_to_drop = [
        'idx_sources_reliability_performance',
        'idx_sources_scraping_schedule',
        'idx_gaming_contracts_monitoring',
        'idx_gaming_contracts_detection',
        'idx_nft_collections_volume_24h_desc',
        'idx_nft_collections_floor_price_desc',
        'idx_reddit_posts_quality_gaming',
        'idx_twitter_posts_gaming_engagement',
        'idx_blockchain_data_gaming_project_timestamp',
        'idx_blockchain_data_blockchain_event_timestamp',
        'idx_blockchain_data_contract_timestamp',
        'idx_blockchain_data_timestamp_desc',
        'idx_gaming_projects_token_info',
        'idx_gaming_projects_category_blockchain_active',
        'idx_gaming_projects_active_blockchain',
        'idx_gaming_projects_name_lower',
        'idx_articles_gaming_tokens_gin',
        'idx_articles_gaming_projects_gin',
        'idx_articles_processing_status',
        'idx_articles_sentiment_relevance_published',
        'idx_articles_source_published_relevance',
        'idx_articles_gaming_category_published_desc',
        'idx_articles_published_at_desc'
    ]
    
    for index_name in indexes_to_drop:
        try:
            op.drop_index(index_name)
        except Exception:
            # Index might not exist, continue with others
            pass
