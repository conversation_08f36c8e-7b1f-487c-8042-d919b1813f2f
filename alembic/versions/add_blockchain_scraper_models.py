"""Add blockchain scraper models

Revision ID: blockchain_scraper_001
Revises: 
Create Date: 2025-07-06 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'blockchain_scraper_001'
down_revision = None  # Update this to the latest revision
branch_labels = None
depends_on = None


def upgrade():
    # Create gaming_contracts table
    op.create_table('gaming_contracts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('contract_address', sa.String(length=100), nullable=False),
        sa.Column('blockchain', sa.String(length=50), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=True),
        sa.Column('symbol', sa.String(length=50), nullable=True),
        sa.Column('contract_type', sa.String(length=50), nullable=True),
        sa.Column('is_gaming', sa.<PERSON>(), nullable=True),
        sa.Column('confidence_level', sa.String(length=20), nullable=True),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('heuristic_patterns', sa.JSON(), nullable=True),
        sa.Column('heuristic_score', sa.Float(), nullable=True),
        sa.Column('ml_probability', sa.Float(), nullable=True),
        sa.Column('ml_confidence', sa.Float(), nullable=True),
        sa.Column('ml_features', sa.JSON(), nullable=True),
        sa.Column('deployment_block', sa.Integer(), nullable=True),
        sa.Column('deployment_timestamp', sa.DateTime(), nullable=True),
        sa.Column('creator_address', sa.String(length=100), nullable=True),
        sa.Column('bytecode_size', sa.Integer(), nullable=True),
        sa.Column('function_count', sa.Integer(), nullable=True),
        sa.Column('event_count', sa.Integer(), nullable=True),
        sa.Column('transaction_count_24h', sa.Integer(), nullable=True),
        sa.Column('unique_users_24h', sa.Integer(), nullable=True),
        sa.Column('last_activity', sa.DateTime(), nullable=True),
        sa.Column('is_monitored', sa.Boolean(), nullable=True),
        sa.Column('monitoring_started', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('contract_address', 'blockchain', name='uq_contract_blockchain')
    )
    
    # Create indexes for gaming_contracts
    op.create_index('idx_gaming_contracts_address_blockchain', 'gaming_contracts', ['contract_address', 'blockchain'])
    op.create_index('idx_gaming_contracts_gaming', 'gaming_contracts', ['is_gaming'])
    op.create_index('idx_gaming_contracts_monitored', 'gaming_contracts', ['is_monitored'])
    op.create_index(op.f('ix_gaming_contracts_blockchain'), 'gaming_contracts', ['blockchain'])
    op.create_index(op.f('ix_gaming_contracts_confidence_level'), 'gaming_contracts', ['confidence_level'])
    op.create_index(op.f('ix_gaming_contracts_contract_address'), 'gaming_contracts', ['contract_address'])
    op.create_index(op.f('ix_gaming_contracts_creator_address'), 'gaming_contracts', ['creator_address'])
    op.create_index(op.f('ix_gaming_contracts_id'), 'gaming_contracts', ['id'])
    op.create_index(op.f('ix_gaming_contracts_is_gaming'), 'gaming_contracts', ['is_gaming'])
    op.create_index(op.f('ix_gaming_contracts_is_monitored'), 'gaming_contracts', ['is_monitored'])

    # Create game_events table
    op.create_table('game_events',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('event_id', sa.String(length=100), nullable=False),
        sa.Column('blockchain', sa.String(length=50), nullable=False),
        sa.Column('block_number', sa.Integer(), nullable=False),
        sa.Column('transaction_hash', sa.String(length=100), nullable=False),
        sa.Column('log_index', sa.Integer(), nullable=True),
        sa.Column('contract_address', sa.String(length=100), nullable=False),
        sa.Column('gaming_contract_id', sa.Integer(), nullable=True),
        sa.Column('event_type', sa.String(length=50), nullable=True),
        sa.Column('event_name', sa.String(length=100), nullable=True),
        sa.Column('event_signature', sa.String(length=200), nullable=True),
        sa.Column('raw_data', sa.JSON(), nullable=True),
        sa.Column('decoded_data', sa.JSON(), nullable=True),
        sa.Column('player_address', sa.String(length=100), nullable=True),
        sa.Column('token_id', sa.String(length=100), nullable=True),
        sa.Column('amount', sa.String(length=100), nullable=True),
        sa.Column('game_action', sa.String(length=50), nullable=True),
        sa.Column('gas_used', sa.Integer(), nullable=True),
        sa.Column('gas_price', sa.String(length=50), nullable=True),
        sa.Column('block_timestamp', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['gaming_contract_id'], ['gaming_contracts.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('event_id')
    )
    
    # Create indexes for game_events
    op.create_index('idx_game_events_contract_timestamp', 'game_events', ['contract_address', 'block_timestamp'])
    op.create_index('idx_game_events_player_timestamp', 'game_events', ['player_address', 'block_timestamp'])
    op.create_index('idx_game_events_type_timestamp', 'game_events', ['event_type', 'block_timestamp'])
    op.create_index(op.f('ix_game_events_blockchain'), 'game_events', ['blockchain'])
    op.create_index(op.f('ix_game_events_block_number'), 'game_events', ['block_number'])
    op.create_index(op.f('ix_game_events_block_timestamp'), 'game_events', ['block_timestamp'])
    op.create_index(op.f('ix_game_events_contract_address'), 'game_events', ['contract_address'])
    op.create_index(op.f('ix_game_events_event_id'), 'game_events', ['event_id'])
    op.create_index(op.f('ix_game_events_event_name'), 'game_events', ['event_name'])
    op.create_index(op.f('ix_game_events_event_type'), 'game_events', ['event_type'])
    op.create_index(op.f('ix_game_events_game_action'), 'game_events', ['game_action'])
    op.create_index(op.f('ix_game_events_id'), 'game_events', ['id'])
    op.create_index(op.f('ix_game_events_player_address'), 'game_events', ['player_address'])
    op.create_index(op.f('ix_game_events_token_id'), 'game_events', ['token_id'])
    op.create_index(op.f('ix_game_events_transaction_hash'), 'game_events', ['transaction_hash'])

    # Create contract_analyses table
    op.create_table('contract_analyses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('contract_address', sa.String(length=100), nullable=False),
        sa.Column('blockchain', sa.String(length=50), nullable=False),
        sa.Column('analysis_type', sa.String(length=20), nullable=True),
        sa.Column('analysis_version', sa.String(length=20), nullable=True),
        sa.Column('is_gaming', sa.Boolean(), nullable=True),
        sa.Column('confidence_level', sa.String(length=20), nullable=True),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('detected_patterns', sa.JSON(), nullable=True),
        sa.Column('feature_scores', sa.JSON(), nullable=True),
        sa.Column('analysis_details', sa.JSON(), nullable=True),
        sa.Column('analysis_duration', sa.Float(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for contract_analyses
    op.create_index('idx_contract_analyses_address_blockchain', 'contract_analyses', ['contract_address', 'blockchain'])
    op.create_index('idx_contract_analyses_gaming', 'contract_analyses', ['is_gaming'])
    op.create_index('idx_contract_analyses_type', 'contract_analyses', ['analysis_type'])
    op.create_index(op.f('ix_contract_analyses_blockchain'), 'contract_analyses', ['blockchain'])
    op.create_index(op.f('ix_contract_analyses_confidence_level'), 'contract_analyses', ['confidence_level'])
    op.create_index(op.f('ix_contract_analyses_contract_address'), 'contract_analyses', ['contract_address'])
    op.create_index(op.f('ix_contract_analyses_id'), 'contract_analyses', ['id'])
    op.create_index(op.f('ix_contract_analyses_is_gaming'), 'contract_analyses', ['is_gaming'])


def downgrade():
    # Drop contract_analyses table and indexes
    op.drop_index(op.f('ix_contract_analyses_is_gaming'), table_name='contract_analyses')
    op.drop_index(op.f('ix_contract_analyses_id'), table_name='contract_analyses')
    op.drop_index(op.f('ix_contract_analyses_contract_address'), table_name='contract_analyses')
    op.drop_index(op.f('ix_contract_analyses_confidence_level'), table_name='contract_analyses')
    op.drop_index(op.f('ix_contract_analyses_blockchain'), table_name='contract_analyses')
    op.drop_index('idx_contract_analyses_type', table_name='contract_analyses')
    op.drop_index('idx_contract_analyses_gaming', table_name='contract_analyses')
    op.drop_index('idx_contract_analyses_address_blockchain', table_name='contract_analyses')
    op.drop_table('contract_analyses')

    # Drop game_events table and indexes
    op.drop_index(op.f('ix_game_events_transaction_hash'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_token_id'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_player_address'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_id'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_game_action'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_event_type'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_event_name'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_event_id'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_contract_address'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_block_timestamp'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_block_number'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_blockchain'), table_name='game_events')
    op.drop_index('idx_game_events_type_timestamp', table_name='game_events')
    op.drop_index('idx_game_events_player_timestamp', table_name='game_events')
    op.drop_index('idx_game_events_contract_timestamp', table_name='game_events')
    op.drop_table('game_events')

    # Drop gaming_contracts table and indexes
    op.drop_index(op.f('ix_gaming_contracts_is_monitored'), table_name='gaming_contracts')
    op.drop_index(op.f('ix_gaming_contracts_is_gaming'), table_name='gaming_contracts')
    op.drop_index(op.f('ix_gaming_contracts_id'), table_name='gaming_contracts')
    op.drop_index(op.f('ix_gaming_contracts_creator_address'), table_name='gaming_contracts')
    op.drop_index(op.f('ix_gaming_contracts_contract_address'), table_name='gaming_contracts')
    op.drop_index(op.f('ix_gaming_contracts_confidence_level'), table_name='gaming_contracts')
    op.drop_index(op.f('ix_gaming_contracts_blockchain'), table_name='gaming_contracts')
    op.drop_index('idx_gaming_contracts_monitored', table_name='gaming_contracts')
    op.drop_index('idx_gaming_contracts_gaming', table_name='gaming_contracts')
    op.drop_index('idx_gaming_contracts_address_blockchain', table_name='gaming_contracts')
    op.drop_table('gaming_contracts')
