"""Add social media models

Revision ID: b46d7e24cda6
Revises: 1ff4d5927858
Create Date: 2025-07-06 15:02:18.536475

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b46d7e24cda6'
down_revision: Union[str, None] = '1ff4d5927858'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('reddit_posts',
    sa.Column('reddit_id', sa.String(length=50), nullable=False),
    sa.Column('title', sa.String(length=500), nullable=False),
    sa.Column('selftext', sa.Text(), nullable=True),
    sa.Column('author', sa.String(length=100), nullable=True),
    sa.Column('subreddit', sa.String(length=100), nullable=False),
    sa.Column('created_utc', sa.DateTime(), nullable=False),
    sa.Column('url', sa.String(length=1000), nullable=True),
    sa.Column('permalink', sa.String(length=500), nullable=True),
    sa.Column('post_type', sa.String(length=50), nullable=True),
    sa.Column('score', sa.Integer(), nullable=True),
    sa.Column('upvote_ratio', sa.Float(), nullable=True),
    sa.Column('num_comments', sa.Integer(), nullable=True),
    sa.Column('gaming_keywords', sa.JSON(), nullable=True),
    sa.Column('gaming_projects', sa.JSON(), nullable=True),
    sa.Column('gaming_tokens', sa.JSON(), nullable=True),
    sa.Column('is_gaming_related', sa.Boolean(), nullable=True),
    sa.Column('gaming_category', sa.String(length=50), nullable=True),
    sa.Column('sentiment_score', sa.Float(), nullable=True),
    sa.Column('relevance_score', sa.Float(), nullable=True),
    sa.Column('quality_score', sa.Float(), nullable=True),
    sa.Column('is_processed', sa.Boolean(), nullable=True),
    sa.Column('meets_quality_threshold', sa.Boolean(), nullable=True),
    sa.Column('extra_metadata', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_reddit_posts_author'), 'reddit_posts', ['author'], unique=False)
    op.create_index(op.f('ix_reddit_posts_created_utc'), 'reddit_posts', ['created_utc'], unique=False)
    op.create_index(op.f('ix_reddit_posts_id'), 'reddit_posts', ['id'], unique=False)
    op.create_index(op.f('ix_reddit_posts_is_gaming_related'), 'reddit_posts', ['is_gaming_related'], unique=False)
    op.create_index(op.f('ix_reddit_posts_meets_quality_threshold'), 'reddit_posts', ['meets_quality_threshold'], unique=False)
    op.create_index(op.f('ix_reddit_posts_reddit_id'), 'reddit_posts', ['reddit_id'], unique=True)
    op.create_index(op.f('ix_reddit_posts_score'), 'reddit_posts', ['score'], unique=False)
    op.create_index(op.f('ix_reddit_posts_subreddit'), 'reddit_posts', ['subreddit'], unique=False)
    op.create_table('social_media_filters',
    sa.Column('filter_name', sa.String(length=100), nullable=False),
    sa.Column('filter_type', sa.String(length=50), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_locked', sa.Boolean(), nullable=True),
    sa.Column('keywords', sa.JSON(), nullable=True),
    sa.Column('excluded_keywords', sa.JSON(), nullable=True),
    sa.Column('gaming_projects', sa.JSON(), nullable=True),
    sa.Column('gaming_influencers', sa.JSON(), nullable=True),
    sa.Column('gaming_tokens', sa.JSON(), nullable=True),
    sa.Column('min_engagement_score', sa.Integer(), nullable=True),
    sa.Column('min_relevance_score', sa.Float(), nullable=True),
    sa.Column('min_sentiment_score', sa.Float(), nullable=True),
    sa.Column('max_age_hours', sa.Integer(), nullable=True),
    sa.Column('twitter_settings', sa.JSON(), nullable=True),
    sa.Column('reddit_settings', sa.JSON(), nullable=True),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('extra_metadata', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_social_media_filter_active', 'social_media_filters', ['is_active'], unique=False)
    op.create_index('idx_social_media_filter_name', 'social_media_filters', ['filter_name'], unique=False)
    op.create_index('idx_social_media_filter_type', 'social_media_filters', ['filter_type'], unique=False)
    op.create_index(op.f('ix_social_media_filters_id'), 'social_media_filters', ['id'], unique=False)
    op.create_index(op.f('ix_social_media_filters_is_active'), 'social_media_filters', ['is_active'], unique=False)
    op.create_table('twitter_posts',
    sa.Column('twitter_id', sa.String(length=50), nullable=False),
    sa.Column('text', sa.Text(), nullable=False),
    sa.Column('author_username', sa.String(length=100), nullable=False),
    sa.Column('author_name', sa.String(length=200), nullable=True),
    sa.Column('author_verified', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('language', sa.String(length=10), nullable=True),
    sa.Column('url', sa.String(length=500), nullable=True),
    sa.Column('retweet_count', sa.Integer(), nullable=True),
    sa.Column('like_count', sa.Integer(), nullable=True),
    sa.Column('reply_count', sa.Integer(), nullable=True),
    sa.Column('quote_count', sa.Integer(), nullable=True),
    sa.Column('hashtags', sa.JSON(), nullable=True),
    sa.Column('mentions', sa.JSON(), nullable=True),
    sa.Column('gaming_keywords', sa.JSON(), nullable=True),
    sa.Column('gaming_projects', sa.JSON(), nullable=True),
    sa.Column('gaming_influencers', sa.JSON(), nullable=True),
    sa.Column('is_gaming_related', sa.Boolean(), nullable=True),
    sa.Column('gaming_category', sa.String(length=50), nullable=True),
    sa.Column('sentiment_score', sa.Float(), nullable=True),
    sa.Column('relevance_score', sa.Float(), nullable=True),
    sa.Column('is_processed', sa.Boolean(), nullable=True),
    sa.Column('collection_source', sa.String(length=50), nullable=True),
    sa.Column('extra_metadata', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_twitter_posts_author_username'), 'twitter_posts', ['author_username'], unique=False)
    op.create_index(op.f('ix_twitter_posts_created_at'), 'twitter_posts', ['created_at'], unique=False)
    op.create_index(op.f('ix_twitter_posts_id'), 'twitter_posts', ['id'], unique=False)
    op.create_index(op.f('ix_twitter_posts_is_gaming_related'), 'twitter_posts', ['is_gaming_related'], unique=False)
    op.create_index(op.f('ix_twitter_posts_twitter_id'), 'twitter_posts', ['twitter_id'], unique=True)
    op.drop_index('idx_protocol_metrics_chain_time', table_name='gaming_protocol_metrics_history')
    op.drop_index('idx_protocol_metrics_protocol_price', table_name='gaming_protocol_metrics_history')
    op.drop_index('idx_protocol_metrics_protocol_time', table_name='gaming_protocol_metrics_history')
    op.drop_index('idx_protocol_metrics_time_only', table_name='gaming_protocol_metrics_history')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_protocol_metrics_time_only', 'gaming_protocol_metrics_history', ['collection_timestamp'], unique=False)
    op.create_index('idx_protocol_metrics_protocol_time', 'gaming_protocol_metrics_history', ['protocol_name', 'collection_timestamp'], unique=False)
    op.create_index('idx_protocol_metrics_protocol_price', 'gaming_protocol_metrics_history', ['protocol_name', 'token_price'], unique=False)
    op.create_index('idx_protocol_metrics_chain_time', 'gaming_protocol_metrics_history', ['chain', 'collection_timestamp'], unique=False)
    op.drop_index(op.f('ix_twitter_posts_twitter_id'), table_name='twitter_posts')
    op.drop_index(op.f('ix_twitter_posts_is_gaming_related'), table_name='twitter_posts')
    op.drop_index(op.f('ix_twitter_posts_id'), table_name='twitter_posts')
    op.drop_index(op.f('ix_twitter_posts_created_at'), table_name='twitter_posts')
    op.drop_index(op.f('ix_twitter_posts_author_username'), table_name='twitter_posts')
    op.drop_table('twitter_posts')
    op.drop_index(op.f('ix_social_media_filters_is_active'), table_name='social_media_filters')
    op.drop_index(op.f('ix_social_media_filters_id'), table_name='social_media_filters')
    op.drop_index('idx_social_media_filter_type', table_name='social_media_filters')
    op.drop_index('idx_social_media_filter_name', table_name='social_media_filters')
    op.drop_index('idx_social_media_filter_active', table_name='social_media_filters')
    op.drop_table('social_media_filters')
    op.drop_index(op.f('ix_reddit_posts_subreddit'), table_name='reddit_posts')
    op.drop_index(op.f('ix_reddit_posts_score'), table_name='reddit_posts')
    op.drop_index(op.f('ix_reddit_posts_reddit_id'), table_name='reddit_posts')
    op.drop_index(op.f('ix_reddit_posts_meets_quality_threshold'), table_name='reddit_posts')
    op.drop_index(op.f('ix_reddit_posts_is_gaming_related'), table_name='reddit_posts')
    op.drop_index(op.f('ix_reddit_posts_id'), table_name='reddit_posts')
    op.drop_index(op.f('ix_reddit_posts_created_utc'), table_name='reddit_posts')
    op.drop_index(op.f('ix_reddit_posts_author'), table_name='reddit_posts')
    op.drop_table('reddit_posts')
    # ### end Alembic commands ###
