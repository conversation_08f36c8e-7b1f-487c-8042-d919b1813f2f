"""Initial migration with gaming models

Revision ID: fb6bcd3d34e3
Revises: 
Create Date: 2025-07-03 19:33:54.708432

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fb6bcd3d34e3'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('gaming_projects',
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('slug', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('website', sa.String(length=500), nullable=True),
    sa.Column('category', sa.String(length=50), nullable=False),
    sa.Column('subcategory', sa.String(length=100), nullable=True),
    sa.Column('blockchain', sa.String(length=50), nullable=True),
    sa.Column('contract_addresses', sa.JSON(), nullable=True),
    sa.Column('token_symbol', sa.String(length=20), nullable=True),
    sa.Column('token_address', sa.String(length=100), nullable=True),
    sa.Column('market_cap', sa.Float(), nullable=True),
    sa.Column('token_price', sa.Float(), nullable=True),
    sa.Column('daily_active_users', sa.Integer(), nullable=True),
    sa.Column('total_value_locked', sa.Float(), nullable=True),
    sa.Column('twitter_followers', sa.Integer(), nullable=True),
    sa.Column('discord_members', sa.Integer(), nullable=True),
    sa.Column('telegram_members', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('launch_date', sa.DateTime(), nullable=True),
    sa.Column('extra_metadata', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_gaming_projects_id'), 'gaming_projects', ['id'], unique=False)
    op.create_index(op.f('ix_gaming_projects_slug'), 'gaming_projects', ['slug'], unique=True)
    op.create_table('sources',
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('slug', sa.String(length=100), nullable=False),
    sa.Column('url', sa.String(length=1000), nullable=False),
    sa.Column('source_type', sa.String(length=50), nullable=False),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('scrape_frequency', sa.Integer(), nullable=True),
    sa.Column('last_scraped_at', sa.DateTime(), nullable=True),
    sa.Column('reliability_score', sa.Float(), nullable=True),
    sa.Column('article_count', sa.Integer(), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    sa.UniqueConstraint('slug')
    )
    op.create_index(op.f('ix_sources_id'), 'sources', ['id'], unique=False)
    op.create_table('articles',
    sa.Column('title', sa.String(length=500), nullable=False),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('summary', sa.Text(), nullable=True),
    sa.Column('url', sa.String(length=1000), nullable=False),
    sa.Column('author', sa.String(length=200), nullable=True),
    sa.Column('published_at', sa.DateTime(), nullable=True),
    sa.Column('source_id', sa.Integer(), nullable=False),
    sa.Column('source_url', sa.String(length=1000), nullable=True),
    sa.Column('gaming_category', sa.String(length=50), nullable=True),
    sa.Column('gaming_subcategory', sa.String(length=100), nullable=True),
    sa.Column('gaming_projects', sa.JSON(), nullable=True),
    sa.Column('gaming_tokens', sa.JSON(), nullable=True),
    sa.Column('sentiment_score', sa.Float(), nullable=True),
    sa.Column('relevance_score', sa.Float(), nullable=True),
    sa.Column('keywords', sa.JSON(), nullable=True),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('views', sa.Integer(), nullable=True),
    sa.Column('likes', sa.Integer(), nullable=True),
    sa.Column('shares', sa.Integer(), nullable=True),
    sa.Column('comments', sa.Integer(), nullable=True),
    sa.Column('is_processed', sa.Boolean(), nullable=True),
    sa.Column('is_duplicate', sa.Boolean(), nullable=True),
    sa.Column('duplicate_of_id', sa.Integer(), nullable=True),
    sa.Column('extra_metadata', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['duplicate_of_id'], ['articles.id'], ),
    sa.ForeignKeyConstraint(['source_id'], ['sources.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_articles_gaming_category', 'articles', ['gaming_category'], unique=False)
    op.create_index('idx_articles_published_at', 'articles', ['published_at'], unique=False)
    op.create_index('idx_articles_relevance', 'articles', ['relevance_score'], unique=False)
    op.create_index('idx_articles_source_published', 'articles', ['source_id', 'published_at'], unique=False)
    op.create_index(op.f('ix_articles_gaming_category'), 'articles', ['gaming_category'], unique=False)
    op.create_index(op.f('ix_articles_id'), 'articles', ['id'], unique=False)
    op.create_index(op.f('ix_articles_published_at'), 'articles', ['published_at'], unique=False)
    op.create_index(op.f('ix_articles_title'), 'articles', ['title'], unique=False)
    op.create_index(op.f('ix_articles_url'), 'articles', ['url'], unique=True)
    op.create_table('nft_collections',
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('slug', sa.String(length=200), nullable=False),
    sa.Column('contract_address', sa.String(length=100), nullable=False),
    sa.Column('blockchain', sa.String(length=50), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image_url', sa.String(length=500), nullable=True),
    sa.Column('website', sa.String(length=500), nullable=True),
    sa.Column('gaming_category', sa.String(length=50), nullable=True),
    sa.Column('gaming_project_id', sa.Integer(), nullable=True),
    sa.Column('total_supply', sa.Integer(), nullable=True),
    sa.Column('floor_price', sa.Float(), nullable=True),
    sa.Column('floor_price_usd', sa.Float(), nullable=True),
    sa.Column('volume_24h', sa.Float(), nullable=True),
    sa.Column('volume_total', sa.Float(), nullable=True),
    sa.Column('owners_count', sa.Integer(), nullable=True),
    sa.Column('discord_members', sa.Integer(), nullable=True),
    sa.Column('twitter_followers', sa.Integer(), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('extra_metadata', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['gaming_project_id'], ['gaming_projects.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_nft_collections_blockchain', 'nft_collections', ['blockchain'], unique=False)
    op.create_index('idx_nft_collections_floor_price', 'nft_collections', ['floor_price'], unique=False)
    op.create_index('idx_nft_collections_gaming_category', 'nft_collections', ['gaming_category'], unique=False)
    op.create_index(op.f('ix_nft_collections_contract_address'), 'nft_collections', ['contract_address'], unique=False)
    op.create_index(op.f('ix_nft_collections_id'), 'nft_collections', ['id'], unique=False)
    op.create_index(op.f('ix_nft_collections_slug'), 'nft_collections', ['slug'], unique=True)
    op.create_table('blockchain_data',
    sa.Column('article_id', sa.Integer(), nullable=True),
    sa.Column('gaming_project_id', sa.Integer(), nullable=True),
    sa.Column('blockchain', sa.String(length=50), nullable=False),
    sa.Column('block_number', sa.Integer(), nullable=True),
    sa.Column('transaction_hash', sa.String(length=100), nullable=True),
    sa.Column('contract_address', sa.String(length=100), nullable=True),
    sa.Column('event_type', sa.String(length=100), nullable=True),
    sa.Column('event_data', sa.JSON(), nullable=True),
    sa.Column('token_symbol', sa.String(length=20), nullable=True),
    sa.Column('token_address', sa.String(length=100), nullable=True),
    sa.Column('token_amount', sa.Float(), nullable=True),
    sa.Column('token_price_usd', sa.Float(), nullable=True),
    sa.Column('nft_collection', sa.String(length=200), nullable=True),
    sa.Column('nft_token_id', sa.String(length=100), nullable=True),
    sa.Column('nft_metadata', sa.JSON(), nullable=True),
    sa.Column('from_address', sa.String(length=100), nullable=True),
    sa.Column('to_address', sa.String(length=100), nullable=True),
    sa.Column('gas_used', sa.Integer(), nullable=True),
    sa.Column('gas_price', sa.Float(), nullable=True),
    sa.Column('block_timestamp', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['article_id'], ['articles.id'], ),
    sa.ForeignKeyConstraint(['gaming_project_id'], ['gaming_projects.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_blockchain_data_blockchain', 'blockchain_data', ['blockchain'], unique=False)
    op.create_index('idx_blockchain_data_contract', 'blockchain_data', ['contract_address'], unique=False)
    op.create_index('idx_blockchain_data_event_type', 'blockchain_data', ['event_type'], unique=False)
    op.create_index('idx_blockchain_data_timestamp', 'blockchain_data', ['block_timestamp'], unique=False)
    op.create_index(op.f('ix_blockchain_data_block_timestamp'), 'blockchain_data', ['block_timestamp'], unique=False)
    op.create_index(op.f('ix_blockchain_data_blockchain'), 'blockchain_data', ['blockchain'], unique=False)
    op.create_index(op.f('ix_blockchain_data_contract_address'), 'blockchain_data', ['contract_address'], unique=False)
    op.create_index(op.f('ix_blockchain_data_id'), 'blockchain_data', ['id'], unique=False)
    op.create_index(op.f('ix_blockchain_data_transaction_hash'), 'blockchain_data', ['transaction_hash'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_blockchain_data_transaction_hash'), table_name='blockchain_data')
    op.drop_index(op.f('ix_blockchain_data_id'), table_name='blockchain_data')
    op.drop_index(op.f('ix_blockchain_data_contract_address'), table_name='blockchain_data')
    op.drop_index(op.f('ix_blockchain_data_blockchain'), table_name='blockchain_data')
    op.drop_index(op.f('ix_blockchain_data_block_timestamp'), table_name='blockchain_data')
    op.drop_index('idx_blockchain_data_timestamp', table_name='blockchain_data')
    op.drop_index('idx_blockchain_data_event_type', table_name='blockchain_data')
    op.drop_index('idx_blockchain_data_contract', table_name='blockchain_data')
    op.drop_index('idx_blockchain_data_blockchain', table_name='blockchain_data')
    op.drop_table('blockchain_data')
    op.drop_index(op.f('ix_nft_collections_slug'), table_name='nft_collections')
    op.drop_index(op.f('ix_nft_collections_id'), table_name='nft_collections')
    op.drop_index(op.f('ix_nft_collections_contract_address'), table_name='nft_collections')
    op.drop_index('idx_nft_collections_gaming_category', table_name='nft_collections')
    op.drop_index('idx_nft_collections_floor_price', table_name='nft_collections')
    op.drop_index('idx_nft_collections_blockchain', table_name='nft_collections')
    op.drop_table('nft_collections')
    op.drop_index(op.f('ix_articles_url'), table_name='articles')
    op.drop_index(op.f('ix_articles_title'), table_name='articles')
    op.drop_index(op.f('ix_articles_published_at'), table_name='articles')
    op.drop_index(op.f('ix_articles_id'), table_name='articles')
    op.drop_index(op.f('ix_articles_gaming_category'), table_name='articles')
    op.drop_index('idx_articles_source_published', table_name='articles')
    op.drop_index('idx_articles_relevance', table_name='articles')
    op.drop_index('idx_articles_published_at', table_name='articles')
    op.drop_index('idx_articles_gaming_category', table_name='articles')
    op.drop_table('articles')
    op.drop_index(op.f('ix_sources_id'), table_name='sources')
    op.drop_table('sources')
    op.drop_index(op.f('ix_gaming_projects_slug'), table_name='gaming_projects')
    op.drop_index(op.f('ix_gaming_projects_id'), table_name='gaming_projects')
    op.drop_table('gaming_projects')
    # ### end Alembic commands ###
