"""Add gaming protocol historical data tables

Revision ID: 1ff4d5927858
Revises: 3d931947043d
Create Date: 2025-07-05 00:19:26.989481

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1ff4d5927858'
down_revision: Union[str, None] = '3d931947043d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('gaming_protocol_daily_summary',
    sa.Column('protocol_name', sa.String(length=100), nullable=False),
    sa.Column('date', sa.DateTime(), nullable=False),
    sa.Column('avg_token_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('min_token_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('max_token_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('price_change_percent', sa.Float(), nullable=True),
    sa.Column('avg_market_cap', sa.BigInteger(), nullable=True),
    sa.Column('total_trading_volume', sa.BigInteger(), nullable=True),
    sa.Column('max_daily_active_users', sa.Integer(), nullable=True),
    sa.Column('total_transactions', sa.Integer(), nullable=True),
    sa.Column('total_transaction_volume', sa.BigInteger(), nullable=True),
    sa.Column('total_nft_trades', sa.Integer(), nullable=True),
    sa.Column('total_nft_volume', sa.BigInteger(), nullable=True),
    sa.Column('avg_floor_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('avg_protocol_uptime', sa.Float(), nullable=True),
    sa.Column('total_protocol_revenue', sa.BigInteger(), nullable=True),
    sa.Column('data_points_collected', sa.Integer(), nullable=True),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_daily_summary_date', 'gaming_protocol_daily_summary', ['date'], unique=False)
    op.create_index('idx_daily_summary_protocol_date', 'gaming_protocol_daily_summary', ['protocol_name', 'date'], unique=True)
    op.create_index(op.f('ix_gaming_protocol_daily_summary_date'), 'gaming_protocol_daily_summary', ['date'], unique=False)
    op.create_index(op.f('ix_gaming_protocol_daily_summary_id'), 'gaming_protocol_daily_summary', ['id'], unique=False)
    op.create_index(op.f('ix_gaming_protocol_daily_summary_protocol_name'), 'gaming_protocol_daily_summary', ['protocol_name'], unique=False)
    op.create_table('gaming_protocol_metrics_history',
    sa.Column('protocol_name', sa.String(length=100), nullable=False),
    sa.Column('protocol_display_name', sa.String(length=200), nullable=True),
    sa.Column('chain', sa.String(length=50), nullable=False),
    sa.Column('token_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('token_price_change_24h', sa.Float(), nullable=True),
    sa.Column('market_cap', sa.BigInteger(), nullable=True),
    sa.Column('trading_volume_24h', sa.BigInteger(), nullable=True),
    sa.Column('circulating_supply', sa.BigInteger(), nullable=True),
    sa.Column('daily_active_users', sa.Integer(), nullable=True),
    sa.Column('monthly_active_users', sa.Integer(), nullable=True),
    sa.Column('new_users_24h', sa.Integer(), nullable=True),
    sa.Column('user_retention_rate', sa.Float(), nullable=True),
    sa.Column('transaction_count_24h', sa.Integer(), nullable=True),
    sa.Column('transaction_volume_24h', sa.BigInteger(), nullable=True),
    sa.Column('average_transaction_value', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('gas_fees_24h', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('nft_trades_24h', sa.Integer(), nullable=True),
    sa.Column('nft_volume_24h', sa.BigInteger(), nullable=True),
    sa.Column('floor_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('unique_holders', sa.Integer(), nullable=True),
    sa.Column('total_value_locked', sa.BigInteger(), nullable=True),
    sa.Column('protocol_revenue_24h', sa.BigInteger(), nullable=True),
    sa.Column('staking_rewards_distributed', sa.BigInteger(), nullable=True),
    sa.Column('average_earnings_per_user', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('reward_token_distribution', sa.BigInteger(), nullable=True),
    sa.Column('gameplay_sessions_24h', sa.Integer(), nullable=True),
    sa.Column('protocol_uptime', sa.Float(), nullable=True),
    sa.Column('smart_contract_interactions', sa.Integer(), nullable=True),
    sa.Column('developer_activity_score', sa.Float(), nullable=True),
    sa.Column('data_source', sa.String(length=100), nullable=True),
    sa.Column('collection_timestamp', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_protocol_metrics_chain_time', 'gaming_protocol_metrics_history', ['chain', 'collection_timestamp'], unique=False)
    op.create_index('idx_protocol_metrics_protocol_price', 'gaming_protocol_metrics_history', ['protocol_name', 'token_price'], unique=False)
    op.create_index('idx_protocol_metrics_protocol_time', 'gaming_protocol_metrics_history', ['protocol_name', 'collection_timestamp'], unique=False)
    op.create_index('idx_protocol_metrics_time_only', 'gaming_protocol_metrics_history', ['collection_timestamp'], unique=False)
    op.create_index(op.f('ix_gaming_protocol_metrics_history_collection_timestamp'), 'gaming_protocol_metrics_history', ['collection_timestamp'], unique=False)
    op.create_index(op.f('ix_gaming_protocol_metrics_history_id'), 'gaming_protocol_metrics_history', ['id'], unique=False)
    op.create_index(op.f('ix_gaming_protocol_metrics_history_protocol_name'), 'gaming_protocol_metrics_history', ['protocol_name'], unique=False)
    op.drop_index('idx_articles_category_published', table_name='articles')
    op.drop_index('idx_articles_gaming_projects', table_name='articles')
    op.drop_index('idx_articles_gaming_tokens', table_name='articles')
    op.drop_index('idx_articles_keywords', table_name='articles')
    op.drop_index('idx_articles_processed_created', table_name='articles')
    op.drop_index('idx_articles_sentiment_relevance', table_name='articles')
    op.drop_index('idx_articles_tags', table_name='articles')
    op.drop_index('idx_gaming_projects_active_launch', table_name='gaming_projects')
    op.drop_index('idx_gaming_projects_category_blockchain', table_name='gaming_projects')
    op.drop_index('idx_gaming_projects_dau', table_name='gaming_projects')
    op.drop_index('idx_gaming_projects_market_cap', table_name='gaming_projects')
    op.drop_index('idx_gaming_projects_token_symbol', table_name='gaming_projects')
    op.drop_index('idx_gaming_projects_tvl', table_name='gaming_projects')
    op.drop_index('idx_nft_collections_owners', table_name='nft_collections')
    op.drop_index('idx_nft_collections_project', table_name='nft_collections')
    op.drop_index('idx_nft_collections_verified_active', table_name='nft_collections')
    op.drop_index('idx_nft_collections_volume_24h', table_name='nft_collections')
    op.drop_index('idx_sources_active_frequency', table_name='sources')
    op.drop_index('idx_sources_last_scraped', table_name='sources')
    op.drop_index('idx_sources_reliability', table_name='sources')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_sources_reliability', 'sources', ['reliability_score'], unique=False)
    op.create_index('idx_sources_last_scraped', 'sources', ['last_scraped_at'], unique=False)
    op.create_index('idx_sources_active_frequency', 'sources', ['is_active', 'scrape_frequency'], unique=False)
    op.create_index('idx_nft_collections_volume_24h', 'nft_collections', ['volume_24h'], unique=False)
    op.create_index('idx_nft_collections_verified_active', 'nft_collections', ['is_verified', 'is_active'], unique=False)
    op.create_index('idx_nft_collections_project', 'nft_collections', ['gaming_project_id'], unique=False)
    op.create_index('idx_nft_collections_owners', 'nft_collections', ['owners_count'], unique=False)
    op.create_index('idx_gaming_projects_tvl', 'gaming_projects', ['total_value_locked'], unique=False)
    op.create_index('idx_gaming_projects_token_symbol', 'gaming_projects', ['token_symbol'], unique=False)
    op.create_index('idx_gaming_projects_market_cap', 'gaming_projects', ['market_cap'], unique=False)
    op.create_index('idx_gaming_projects_dau', 'gaming_projects', ['daily_active_users'], unique=False)
    op.create_index('idx_gaming_projects_category_blockchain', 'gaming_projects', ['category', 'blockchain'], unique=False)
    op.create_index('idx_gaming_projects_active_launch', 'gaming_projects', ['is_active', 'launch_date'], unique=False)
    op.create_index('idx_articles_tags', 'articles', ['tags'], unique=False)
    op.create_index('idx_articles_sentiment_relevance', 'articles', ['sentiment_score', 'relevance_score'], unique=False)
    op.create_index('idx_articles_processed_created', 'articles', ['is_processed', 'created_at'], unique=False)
    op.create_index('idx_articles_keywords', 'articles', ['keywords'], unique=False)
    op.create_index('idx_articles_gaming_tokens', 'articles', ['gaming_tokens'], unique=False)
    op.create_index('idx_articles_gaming_projects', 'articles', ['gaming_projects'], unique=False)
    op.create_index('idx_articles_category_published', 'articles', ['gaming_category', 'published_at'], unique=False)
    op.drop_index(op.f('ix_gaming_protocol_metrics_history_protocol_name'), table_name='gaming_protocol_metrics_history')
    op.drop_index(op.f('ix_gaming_protocol_metrics_history_id'), table_name='gaming_protocol_metrics_history')
    op.drop_index(op.f('ix_gaming_protocol_metrics_history_collection_timestamp'), table_name='gaming_protocol_metrics_history')
    op.drop_index('idx_protocol_metrics_time_only', table_name='gaming_protocol_metrics_history')
    op.drop_index('idx_protocol_metrics_protocol_time', table_name='gaming_protocol_metrics_history')
    op.drop_index('idx_protocol_metrics_protocol_price', table_name='gaming_protocol_metrics_history')
    op.drop_index('idx_protocol_metrics_chain_time', table_name='gaming_protocol_metrics_history')
    op.drop_table('gaming_protocol_metrics_history')
    op.drop_index(op.f('ix_gaming_protocol_daily_summary_protocol_name'), table_name='gaming_protocol_daily_summary')
    op.drop_index(op.f('ix_gaming_protocol_daily_summary_id'), table_name='gaming_protocol_daily_summary')
    op.drop_index(op.f('ix_gaming_protocol_daily_summary_date'), table_name='gaming_protocol_daily_summary')
    op.drop_index('idx_daily_summary_protocol_date', table_name='gaming_protocol_daily_summary')
    op.drop_index('idx_daily_summary_date', table_name='gaming_protocol_daily_summary')
    op.drop_table('gaming_protocol_daily_summary')
    # ### end Alembic commands ###
