"""Add gaming-specific performance indexes

Revision ID: 3d931947043d
Revises: fb6bcd3d34e3
Create Date: 2025-07-03 19:36:48.804871

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3d931947043d'
down_revision: Union[str, None] = 'fb6bcd3d34e3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Gaming-specific performance indexes for articles
    op.create_index('idx_articles_gaming_projects', 'articles', ['gaming_projects'], postgresql_using='gin')
    op.create_index('idx_articles_gaming_tokens', 'articles', ['gaming_tokens'], postgresql_using='gin')
    op.create_index('idx_articles_keywords', 'articles', ['keywords'], postgresql_using='gin')
    op.create_index('idx_articles_tags', 'articles', ['tags'], postgresql_using='gin')
    op.create_index('idx_articles_sentiment_relevance', 'articles', ['sentiment_score', 'relevance_score'])
    op.create_index('idx_articles_category_published', 'articles', ['gaming_category', 'published_at'])
    op.create_index('idx_articles_processed_created', 'articles', ['is_processed', 'created_at'])

    # Gaming project performance indexes
    op.create_index('idx_gaming_projects_category_blockchain', 'gaming_projects', ['category', 'blockchain'])
    op.create_index('idx_gaming_projects_token_symbol', 'gaming_projects', ['token_symbol'])
    op.create_index('idx_gaming_projects_market_cap', 'gaming_projects', ['market_cap'])
    op.create_index('idx_gaming_projects_active_launch', 'gaming_projects', ['is_active', 'launch_date'])
    op.create_index('idx_gaming_projects_dau', 'gaming_projects', ['daily_active_users'])
    op.create_index('idx_gaming_projects_tvl', 'gaming_projects', ['total_value_locked'])

    # NFT collection performance indexes
    op.create_index('idx_nft_collections_project', 'nft_collections', ['gaming_project_id'])
    op.create_index('idx_nft_collections_verified_active', 'nft_collections', ['is_verified', 'is_active'])
    op.create_index('idx_nft_collections_volume_24h', 'nft_collections', ['volume_24h'])
    op.create_index('idx_nft_collections_owners', 'nft_collections', ['owners_count'])

    # Source performance indexes
    op.create_index('idx_sources_active_frequency', 'sources', ['is_active', 'scrape_frequency'])
    op.create_index('idx_sources_reliability', 'sources', ['reliability_score'])
    op.create_index('idx_sources_last_scraped', 'sources', ['last_scraped_at'])


def downgrade() -> None:
    # Drop gaming-specific performance indexes
    op.drop_index('idx_sources_last_scraped', table_name='sources')
    op.drop_index('idx_sources_reliability', table_name='sources')
    op.drop_index('idx_sources_active_frequency', table_name='sources')

    op.drop_index('idx_nft_collections_owners', table_name='nft_collections')
    op.drop_index('idx_nft_collections_volume_24h', table_name='nft_collections')
    op.drop_index('idx_nft_collections_verified_active', table_name='nft_collections')
    op.drop_index('idx_nft_collections_project', table_name='nft_collections')

    op.drop_index('idx_gaming_projects_tvl', table_name='gaming_projects')
    op.drop_index('idx_gaming_projects_dau', table_name='gaming_projects')
    op.drop_index('idx_gaming_projects_active_launch', table_name='gaming_projects')
    op.drop_index('idx_gaming_projects_market_cap', table_name='gaming_projects')
    op.drop_index('idx_gaming_projects_token_symbol', table_name='gaming_projects')
    op.drop_index('idx_gaming_projects_category_blockchain', table_name='gaming_projects')

    op.drop_index('idx_articles_processed_created', table_name='articles')
    op.drop_index('idx_articles_category_published', table_name='articles')
    op.drop_index('idx_articles_sentiment_relevance', table_name='articles')
    op.drop_index('idx_articles_tags', table_name='articles')
    op.drop_index('idx_articles_keywords', table_name='articles')
    op.drop_index('idx_articles_gaming_tokens', table_name='articles')
    op.drop_index('idx_articles_gaming_projects', table_name='articles')
