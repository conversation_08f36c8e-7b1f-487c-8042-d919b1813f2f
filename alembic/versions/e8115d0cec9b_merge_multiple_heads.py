"""merge_multiple_heads

Revision ID: e8115d0cec9b
Revises: 2025_07_07_001, blockchain_scraper_001, b46d7e24cda6
Create Date: 2025-07-07 14:04:46.661445

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e8115d0cec9b'
down_revision: Union[str, None] = ('2025_07_07_001', 'blockchain_scraper_001', 'b46d7e24cda6')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
