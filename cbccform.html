<!-- multistep form -->
<form id="msform">
  <!-- progressbar -->
  <ul id="progressbar">
    <li class="active">Project Overview</li>
    <li>Blockchain</li>
    <li>Game Style</li>
    <li>Validated Game Status</li>
    <li>Classification</li>
    <li>Social Link Tree</li>
    <li>Tokenomics Token 1</li>
    <li>Tokenomics Token 2</li>
    <li>NFTs pt 1</li>
    <li>NFTs pt 2</li>
    <li>User Base</li>
    <li>Auto-Clicker</li>
    <li>Finish</li>
  </ul>
  <!-- fieldsets -->
  <fieldset>
    <h2 class="fs-title">Project Overview</h2>
    <h3 class="fs-subtitle">Get started here.</h3>
    <input type="text" required name="Project Name" placeholder="Project Name" />
    <input type="text" name="Project Website Link" placeholder="Website" />
    <input type="text" name="Whitepaper Link" placeholder="Link to Whitepaper" />
    <input type="text" name="Developer" placeholder="Developer/Studio Name" />
    <input type="text" name="Platform" placeholder="Platform (PC, Mobile, Web, etc.)" />
    <input type="button" name="next" class="next action-button" value="Next" />
  </fieldset>
  <fieldset>
    <h2 class="fs-title">Blockchain</h2>
    <h3 class="fs-subtitle">Check as many as necessary.</h3>
    <div class="checkbox-container">
      <label for="checkbox1">Aptos</label>
      <input type="checkbox" id="aptos" name="aptos" value="aptos">
      <label for="checkbox2">Arbitrum One</label>
      <input type="checkbox" id="arbitrum" name="arbitrum" value="arbitrum">
      <label for="checkbox3">Avalanche</label>
      <input type="checkbox" id="avalanche" name="avalanche" value="avalanche">
      <label for="checkbox4">Base</label>
      <input type="checkbox" id="base" name="base" value="base">
      <label for="checkbox5">Blast</label>
      <input type="checkbox" id="blast" name="blast" value="blast">
      <label for="checkbox6">BRC20</label>
      <input type="checkbox" id="brc20" name="brc20" value="brc20">
      <label for="checkbox7">BSC</label>
      <input type="checkbox" id="bsc" name="bsc" value="bsc">
      <label for="checkbox8">Cardano</label>
      <input type="checkbox" id="cardano" name="cardano" value="cardano">
      <label for="checkbox9">Catheon</label>
      <input type="checkbox" id="catheon" name="catheon" value="catheon">
      <label for="checkbox10">Elysium</label>
      <input type="checkbox" id="elysium" name="elysium" value="elysium">
      <label for="checkbox11">Enjin</label>
      <input type="checkbox" id="enjin" name="enjin" value="enjin">
      <label for="checkbox12">EOS</label>
      <input type="checkbox" id="eos" name="eos" value="eos">
      <label for="checkbox13">Ethereum</label>
      <input type="checkbox" id="ethereum" name="ethereum" value="ethereum">
      <label for="checkbox14">ETHW</label>
      <input type="checkbox" id="ethw" name="ethw" value="ethw">
      <label for="checkbox15">Fantom</label>
      <input type="checkbox" id="fantom" name="fantom" value="fantom">
      <label for="checkbox16">Flow</label>
      <input type="checkbox" id="flow" name="flow" value="flow">
      <label for="checkbox17">Gala Games</label>
      <input type="checkbox" id="gala" name="gala" value="gala">
      <label for="checkbox18">Gem Hub</label>
      <input type="checkbox" id="gemhub" name="gemhub" value="gemhub">
      <label for="checkbox19">Harmony ONE</label>
      <input type="checkbox" id="harmonyone" name="harmonyone" value="harmonyone">
      <label for="checkbox20">HECO</label>
      <input type="checkbox" id="heco" name="heco" value="heco">
      <label for="checkbox21">Hive</label>
      <input type="checkbox" id="hive" name="hive" value="hive">
      <label for="checkbox22">Immutable-X</label>
      <input type="checkbox" id="imx" name="imx" value="imx">
      <label for="checkbox23">Klaytn</label>
      <input type="checkbox" id="klaytn" name="klaytn" value="klaytn">
      <label for="checkbox24">Marblex</label>
      <input type="checkbox" id="marblex" name="marblex" value="marblex">
      <label for="checkbox25">Mirai</label>
      <input type="checkbox" id="mirai" name="mirai" value="mirai">
      <label for="checkbox26">Myria</label>
      <input type="checkbox" id="myria" name="myria" value="myria">
      <label for="checkbox27">NEAR</label>
      <input type="checkbox" id="near" name="near" value="near">
      <label for="checkbox28">NEO</label>
      <input type="checkbox" id="neo" name="neo" value="neo">
      <label for="checkbox29">OEC</label>
      <input type="checkbox" id="oec" name="oec" value="oec">
      <label for="checkbox30">OKEX</label>
      <input type="checkbox" id="okex" name="okex" value="okex">
      <label for="checkbox31">opBNB</label>
      <input type="checkbox" id="opbnb" name="opbnb" value="opbnb">
      <label for="checkbox32">Polkadot</label>
      <input type="checkbox" id="polkadot" name="polkadot" value="polkadot">
      <label for="checkbox33">Polygon</label>
      <input type="checkbox" id="polygon" name="polygon" value="polygon">
      <label for="checkbox34">Ronin</label>
      <input type="checkbox" id="ronin" name="ronin" value="ronin">
      <label for="checkbox35">Solana</label>
      <input type="checkbox" id="solana" name="solana" value="solana">
      <label for="checkbox36">STEEM</label>
      <input type="checkbox" id="steem" name="steem" value="steem">
      <label for="checkbox37">SUI</label>
      <input type="checkbox" id="sui" name="sui" value="sui">
      <label for="checkbox38">Telos</label>
      <input type="checkbox" id="telos" name="telos" value="telos">
      <label for="checkbox39">TON</label>
      <input type="checkbox" id="ton" name="ton" value="ton">
      <label for="checkbox40">TRON</label>
      <input type="checkbox" id="tron" name="tron" value="tron">
      <label for="checkbox41">VeChain</label>
      <input type="checkbox" id="vechain" name="vechain" value="vechain">
      <label for="checkbox42">Vulcan Forged</label>
      <input type="checkbox" id="vulcan" name="vulcan" value="vulcan">
      <label for="checkbox43">WAVES</label>
      <input type="checkbox" id="waves" name="waves" value="waves">
      <label for="checkbox44">WAX</label>
      <input type="checkbox" id="wax" name="wax" value="wax">
      <label for="checkbox45">WEMIX</label>
      <input type="checkbox" id="wemix" name="wemix" value="wemix">
      <label for="checkbox46">XPLA</label>
      <input type="checkbox" id="xpla" name="xpla" value="xpla">
      <label for="checkbox47">Other</label>
      <input type="checkbox" id="other-chain" name="other" value="other">
      <!--<label>
        <input type="checkbox" value="vanilla"> Vanilla<span class="tooltip">This is vanilla flavor</span>
      </label>-->
    </div>
    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />
  </fieldset>

  <fieldset>
    <h2 class="fs-title">Game Style</h2>
    <h3 class="fs-subtitle">Check all that apply</h3>

    <div class="checkbox-container">
      <label for="checkbox48">Free to play</label>
      <input type="checkbox" id="freetoplay" name="Game Style (check all that apply)" value="Free to Play">
      <label for="checkbox49">Play to earn</label>
      <input type="checkbox" id="playtoearn" name="Game Style (check all that apply)" value="Play to Earn">
      <label for="checkbox50">Play to airdrop</label>
      <input type="checkbox" id="playtoairdrop" name="Game Style (check all that apply)" value="Play to Airdrop">
      <label for="checkbox51">GameFi</label>
      <input type="checkbox" id="gamefi" name="Game Style (check all that apply)" value="GameFi">
      <label for="checkbox52">DeFi</label>
      <input type="checkbox" id="defi" name="Game Style (check all that apply)" value="DeFi">
      <label for="checkbox53">Move to earn</label>
      <input type="checkbox" id="movetoearn" name="Game Style (check all that apply)" value="move to earn">
      <label for="checkbox54">Other</label>
      <input type="checkbox" id="other-style" name="Game Style (check all that apply)" value="other">
    </div>

    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />

  </fieldset>
  <fieldset>
    <h2 class="fs-title">Validated Game Status</h2>
    <h3 class="fs-subtitle">Choose one</h3>

    <label><input type="radio" name="Validated Game Status" value="Live" checked>Live</label><br>
    <label><input type="radio" name="Validated Game Status" value="Alpha">Alpha</label><br>
    <label><input type="radio" name="Validated Game Status" value="Beta - closer to its final form">Beta</label><br>
    <label><input type="radio" name="Validated Game Status" value="Coming Soon (with ETA)">Coming Soon with ETA</label><br>
    <label><input type="radio" name="Validated Game Status" value="Coming Soon (no ETA)">Coming Soon, no ETA</label><br>
    <label><input type="radio" name="Validated Game Status" value="Presale">Presale</label><br>
    <label><input type="radio" name="Validated Game Status" value="Abandoned">Abandoned</label><br>
    <label><input type="radio" name="Validated Game Status" value="Unknown">Unknown</label><br>

    <input type="text" name="Game Status Notes" placeholder="Additional notes about game status" />
    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />
  </fieldset>

  <fieldset>
    <h2 class="fs-title">Classification By Genre</h2>
    <h3 class="fs-subtitle">Which genre best fits this title? Choose only ONE. Then choose a subgenre.</h3>
    <script>
      function updateChoices() {
        const productSelect = document.getElementById("product");
        const choiceSelect = document.getElementById("choices");
        const selectedProduct = productSelect.value;
        // Clear previous options
        choiceSelect.innerHTML = "";
        let options = [];
        if (selectedProduct === "action") {
          options = ["Shooters", "Fighting Games", "Hack and Slash"];
        } else if (selectedProduct === "action-adventure") {
          options = ["Cinematic Action Adventure", "Action RPG", "Open World"];
        } else if (selectedProduct === "action-rpg") {
          options = ["Looter-Shooter", "Soulslikes", "Isometric"];
        } else if (selectedProduct === "adventure") {
          options = ["Point-and-Click", "Visual Novel", "Interactive Movie"];
        } else if (selectedProduct === "battle-royale") {
          options = ["Hero Shooters", "Military Shooters"];
        } else if (selectedProduct === "casual") {
          options = ["Matching", "Hidden Object", "Hyper-casual", "Auto-Clicker"];
        } else if (selectedProduct === "fighting") {
          options = ["2D Fighters", "3D Fighters", "Platform Fighters"];
        } else if (selectedProduct === "firstperson-shooter") {
          options = ["Military FPS", "Hero FPS", "Immersive Sims"];
        } else if (selectedProduct === "mmorpg") {
          options = ["Fantasy MMORPG", "Sandbox MMORPG", "Action Combat"];
        } else if (selectedProduct === "party") {
          options = ["Board and Card Games", "Trivia Games", "Activity Games"];
        } else if (selectedProduct === "platformer") {
          options = ["2D Traditional", "Puzzle Platformer", "Run and Gun"];
        } else if (selectedProduct === "puzzle") {
          options = ["Physics Puzzles", "Matching Puzzles", "Logic Puzzles"];
        } else if (selectedProduct === "racing") {
          options = ["Simulation", "Arcade", "Futuristic"];
        } else if (selectedProduct === "realtime-strategy") {
          options = ["Traditional RTS", "MOBA", "Tower Defense"];
        } else if (selectedProduct === "roleplaying") {
          options = ["Action RPG", "Japanese RPG", "Open World RPG"];
        } else if (selectedProduct === "shooter") {
          options = ["Run and Gun", "Bullet Hell", "Third-Person Shooter", "Looter Shooter"];
        } else if (selectedProduct === "simulation") {
          options = ["Flight Sims", "Business and City", "Vehicle Driving", "Life Sims"];
        } else if (selectedProduct === "sports") {
          options = ["Simulation", "Arcade", "Management"];
        } else if (selectedProduct === "stealth") {
          options = ["Tactical Espionage", "Immersive Sims", "Stealth Horror"];
        } else if (selectedProduct === "strategy") {
          options = ["4X", "Real-time Tactics", "Grand Strategy"];
        } else if (selectedProduct === "survival") {
          options = ["Survival Sandbox", "Survival Horror", "Survival Simulation"];
        } else if (selectedProduct === "tactical") {
          options = ["Japanese TRPGs", "Strategy-JRPG Hybrids"];
        }
        // Populate the choices dropdown
        options.forEach(option => {
          const newOption = document.createElement("option");
          newOption.value = option.toLowerCase();
          newOption.textContent = option;
          choiceSelect.appendChild(newOption);
        });
      }
    </script>

    <label for="product">Select Genre:</label>
    <select id="product" name="Which genre best fits this title? Choose only ONE" onchange="updateChoices()">
      <option value="">--Select--</option>
      <option value="action">Action</option>
      <option value="action-adventure">Action-Adventure</option>
      <option value="action-rpg">Action RPG</option>
      <option value="adventure">Adventure</option>
      <option value="battle-royale">Battle Royale</option>
      <option value="casual">Casual</option>
      <option value="fighting">Fighting</option>
      <option value="firstperson-shooter">First Person Shooter</option>
      <option value="mmorpg">MMORPG</option>
      <option value="party">Party</option>
      <option value="platformer">Platformer</option>
      <option value="puzzle">Puzzle</option>
      <option value="racing">Racing</option>
      <option value="realtime-strategy">Real-Time Strategy</option>
      <option value="roleplaying">Role-Playing Game</option>
      <option value="shooter">Shooter</option>
      <option value="simulation">Simulation</option>
      <option value="sports">Sports</option>
      <option value="stealth">Stealth</option>
      <option value="strategy">Strategy</option>
      <option value="survival">Survival</option>
      <option value="tactical">Tactical Role-Playing</option>
    </select>

    <label for="choices">Select Subgenre:</label>
    <select id="choices" name="subgenre">
      <option value="">--Select a genre first.--</option>
    </select>

    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />

  </fieldset>

  <fieldset>
    <h2 class="fs-title">Social Link Tree</h2>
    <h3 class="fs-subtitle">Some projects have more social links than others.</h3>
    <input type="text" name="Twitter (X) link" placeholder="Twitter, X link" />
    <input type="text" name="Discord Server Link" placeholder="Discord Server link" />
    <input type="text" name="Telegram Channel Link" placeholder="Telegram Channel link" />
    <input type="text" name="Medium Link" placeholder="Medium link" />
    <input type="text" name="LinkedIn" placeholder="LinkedIn" />
    <input type="text" name="Youtube Channel" placeholder="YouTube Channel" />
    <input type="text" name="Reddit - Subreddit or User" placeholder="Reddit Subreddit" />
    <input type="text" name="Facebook Page" placeholder="Facebook Page" />
    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />

  </fieldset>

  <fieldset>
    <h2 class="fs-title">Tokenomics</h2>
    <h3 class="fs-subtitle">All info regarding tokens, on- or off-chain go here. </h3>
    <input type="text" name="Token Schedule Link" placeholder="Official Token Schedule link." />
    <input type="text" name="Token 1 - Symbol" placeholder="Token 1 Symbol" />
    <input type="text" name="Token 1 - Token Contract Address" placeholder="Token 1 Contract Address" />
    <input type="text" name="Token 1 Blockchain Scanner link" placeholder="Token 1 Blockchain scanner link" />
    <input type="text" name="Token 1 Link to CoinGecko Listing" placeholder="Token 1 link to CoinGecko" />
    <input type="text" name="Token 1 Total Supply" placeholder="Token 1 Total Supply" />

    <label for="checkbox55">Utility</label>
    <input type="checkbox" id="utility1" name="Token 1 - Type" value="Utility">
    <label for="checkbox56">Governance</label>
    <input type="checkbox" id="governance1" name="Token 1 - Type" value="Governance">
    <label for="checkbox57">NFT mint</label>
    <input type="checkbox" id="nftmint1" name="Token 1 - Type" value="NFT mint">
    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />
  </fieldset>
  <fieldset>
    <h2 class="fs-title">Tokenomics cont'd.</h2>
    <h3 class="fs-subtitle">Continuation & Additional Tokens</h3>
    <input type="text" name="Token 2 - Symbol" placeholder="Token 2 Symbol" />
    <input type="text" name="Token 2 - Token Contract Address" placeholder="Token 2 Contract Address" />
    <input type="text" name="Token 2 - Blockchain Scanner Link" placeholder="Token 2 Blockchain scanner link" />
    <input type="text" name="Token 2 Link to CoinGecko Listing" placeholder="Token 2 link to CoinGecko" />
    <input type="text" name="Token 2 Total Supply" placeholder="Token 2 Total Supply" />

    <label for="checkbox58">Utility</label>
    <input type="checkbox" id="utility2" name="Token 2 - Type" value="Utility">
    <label for="checkbox59">Governance</label>
    <input type="checkbox" id="governance2" name="Token 2 - Type" value="Governance">
    <label for="checkbox60">NFT mint</label>
    <input type="checkbox" id="nftmint2" name="Token 2 - Type" value="NFT mint">

    <input type="text" name="Additional Tokens" placeholder="Additional Tokens (comma separated)" />
    <input type="text" name="Notes, Additional Tokens" placeholder="Notes about additional tokens" />
    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />
  </fieldset>

  <fieldset>
    <h2 class="fs-title">Non-Fungible Tokens</h2>
    <h3 class="fs-subtitle">Does this project involve NFTs?</h3>
    <label><input type="radio" name="Does this project involve NFTs?" value="Yes" checked>Yes</label><br>
    <label><input type="radio" name="Does this project involve NFTs?" value="No">No</label><br>

    <input type="text" name="NFT name" placeholder="NFT name" />
    <input type="text" name="What is the NFT Contract Address?" placeholder="NFT contract address" />
    <input type="text" name="Blockchain scanner link of NFT Contract Address" placeholder="Blockchain scanner link" />
    <input type="text" name="Marketplace link for NFTs for this game" placeholder="NFT marketplace link" />
    <input type="text" name="How many wallets are holding this NFT?" placeholder="Number of wallets holding" />
    <input type="text" name="Source for number of NFT-holding wallets" placeholder="Source for number of wallets holding" />
    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />

  </fieldset>
  <fieldset>
    <h2 class="fs-title">NFT Function</h2>
    <h3 class="fs-subtitle">Check as many as necessary.</h3>
    <div class="checkbox-container">
      <label for="checkbox61">Player-controlled character or object</label>
      <input type="checkbox" id="playable" name="What are the NFTs used for?" value="Player-controlled character or object">
      <label for="checkbox62">In-Game equippable item</label>
      <input type="checkbox" id="equippable" name="What are the NFTs used for?" value="In-Game equippable item">
      <label for="checkbox63">Trading card</label>
      <input type="checkbox" id="trading-card" name="What are the NFTs used for?" value="Trading Card">
      <label for="checkbox64">Staking</label>
      <input type="checkbox" id="staking" name="What are the NFTs used for?" value="Staking">
      <label for="checkbox65">Breeding</label>
      <input type="checkbox" id="breeding" name="What are the NFTs used for?" value="Breeding">
      <label for="checkbox66">Fusing</label>
      <input type="checkbox" id="fusing" name="What are the NFTs used for?" value="Fusing">
      <label for="checkbox67">Requirement for participation</label>
      <input type="checkbox" id="req-part" name="What are the NFTs used for?" value="Requirement for participation">
      <label for="checkbox68">External use</label>
      <input type="checkbox" id="external-use" name="What are the NFTs used for?" value="External use">
    </div>
    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />
  </fieldset>

  <fieldset>
    <h2 class="fs-title">User Base</h2>
    <h3 class="fs-subtitle">How many players are in this game currently?</h3>
    <input type="text" name="On the average, how many daily active users (DAU) in the past month?" placeholder="How many daily active users in the past month?" />
    <input type="text" name="Link to Source of DAU count" placeholder="Link to DAU source" />
    <input type="text" name="On the average, how many daily unique active wallets (UAW) in the past month?" placeholder="How many unique active wallets in the past month?" />
    <input type="text" name="Link to Source of UAW count" placeholder="Link to UAW source" />
    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />

  </fieldset>

  <fieldset>
    <h2 class="fs-title">Auto-Clicker & Telegram Bot</h2>
    <h3 class="fs-subtitle">For Telegram-based games and auto-clickers</h3>

    <input type="text" name="Auto-Clicker Telegram Invite Link" placeholder="Auto-Clicker Telegram Invite Link" />
    <input type="text" name="Auto-Clicker Telegram Community Channel Link" placeholder="Auto-Clicker Telegram Community Channel Link" />
    <input type="text" name="Auto-Clicker Telegram Bot Address" placeholder="Auto-Clicker Telegram Bot Address" />
    <input type="text" name="Auto-Clicker TG Membership Population" placeholder="Auto-Clicker TG Membership Population" />
    <input type="text" name="Auto-Clicker Twitter (X) Page" placeholder="Auto-Clicker Twitter (X) Page" />
    <input type="text" name="Auto-Clicker Discord invite link" placeholder="Auto-Clicker Discord invite link" />
    <input type="text" name="Auto-Clicker Medium Link" placeholder="Auto-Clicker Medium Link" />
    <input type="text" name="Auto-Clicker YouTube Link" placeholder="Auto-Clicker YouTube Link" />

    <label><input type="radio" name="Does this Auto-Clicker have an official Token?" value="Yes">Auto-Clicker has official Token</label><br>
    <label><input type="radio" name="Does this Auto-Clicker have an official Token?" value="No" checked>No official Token</label><br>

    <input type="text" name="Notes and Comments" placeholder="Notes and Comments" />
    <input type="text" name="Additional Notes and Comments" placeholder="Additional Notes and Comments" />

    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="next" class="next action-button" value="Next" />

  </fieldset>

  <fieldset>
    <h2 class="fs-title">Final Notes</h2>
    <h3 class="fs-subtitle">Additional notes and comments.</h3>
    <textarea name="notes" placeholder="Insert notes here."></textarea>
    <input type="button" name="previous" class="previous action-button" value="Previous" />
    <input type="button" name="submit" class="submit action-button" value="Submit" target="_top">
  </fieldset>

</form>