"""
Memory management optimization service for Web3 Gaming News Tracker
Handles memory-intensive operations, garbage collection, and resource monitoring
"""
import gc
import psutil
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from contextlib import asynccontextmanager
import weakref
from sqlalchemy.orm import Session
from sqlalchemy import text

from models.base import get_db_sync, db_manager
from services.redis_cache import gaming_cache
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class MemoryMetrics:
    """Memory usage metrics"""
    total_memory: float
    available_memory: float
    used_memory: float
    memory_percent: float
    process_memory: float
    gc_collections: Dict[int, int]
    active_connections: int
    cached_objects: int


class MemoryMonitor:
    """Monitor system and application memory usage"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.memory_history = []
        self.alert_threshold = 85.0  # Alert when memory usage > 85%
        self.cleanup_threshold = 90.0  # Force cleanup when > 90%
        
    def get_memory_metrics(self) -> MemoryMetrics:
        """Get current memory usage metrics"""
        # System memory
        memory = psutil.virtual_memory()
        
        # Process memory
        process_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        # Garbage collection stats
        gc_stats = {}
        for i in range(3):
            gc_stats[i] = gc.get_count()[i]
        
        # Database connections
        pool_status = db_manager.get_connection_pool_status()
        active_connections = pool_status.get('checked_out', 0)
        
        # Cache object count (estimate)
        try:
            cache_info = gaming_cache.redis_client.info('memory')
            cached_objects = cache_info.get('used_memory', 0) // 1024  # Rough estimate
        except:
            cached_objects = 0
        
        return MemoryMetrics(
            total_memory=memory.total / 1024 / 1024 / 1024,  # GB
            available_memory=memory.available / 1024 / 1024 / 1024,  # GB
            used_memory=memory.used / 1024 / 1024 / 1024,  # GB
            memory_percent=memory.percent,
            process_memory=process_memory,
            gc_collections=gc_stats,
            active_connections=active_connections,
            cached_objects=cached_objects
        )
    
    def record_memory_usage(self):
        """Record current memory usage for trend analysis"""
        metrics = self.get_memory_metrics()
        self.memory_history.append({
            'timestamp': datetime.utcnow(),
            'memory_percent': metrics.memory_percent,
            'process_memory': metrics.process_memory
        })
        
        # Keep only last 100 records
        if len(self.memory_history) > 100:
            self.memory_history = self.memory_history[-100:]
        
        # Check for alerts
        if metrics.memory_percent > self.alert_threshold:
            logger.warning(f"High memory usage: {metrics.memory_percent:.1f}%")
            
        if metrics.memory_percent > self.cleanup_threshold:
            logger.error(f"Critical memory usage: {metrics.memory_percent:.1f}% - forcing cleanup")
            return True
        
        return False
    
    def get_memory_trend(self, minutes: int = 30) -> Dict[str, Any]:
        """Get memory usage trend over specified time period"""
        cutoff = datetime.utcnow() - timedelta(minutes=minutes)
        recent_history = [
            record for record in self.memory_history 
            if record['timestamp'] > cutoff
        ]
        
        if not recent_history:
            return {}
        
        memory_values = [record['memory_percent'] for record in recent_history]
        process_values = [record['process_memory'] for record in recent_history]
        
        return {
            'avg_memory_percent': sum(memory_values) / len(memory_values),
            'max_memory_percent': max(memory_values),
            'avg_process_memory': sum(process_values) / len(process_values),
            'max_process_memory': max(process_values),
            'trend_direction': 'increasing' if memory_values[-1] > memory_values[0] else 'decreasing',
            'sample_count': len(recent_history)
        }


class MemoryOptimizer:
    """Optimize memory usage for gaming data processing"""
    
    def __init__(self):
        self.monitor = MemoryMonitor()
        self.object_pools = {}
        self.weak_references = weakref.WeakSet()
        
    def force_garbage_collection(self) -> Dict[str, int]:
        """Force garbage collection and return collection stats"""
        before_counts = gc.get_count()
        
        # Force collection for all generations
        collected = {}
        for generation in range(3):
            collected[generation] = gc.collect(generation)
        
        after_counts = gc.get_count()
        
        logger.info(f"Garbage collection completed: {collected}")
        return {
            'before': before_counts,
            'after': after_counts,
            'collected': collected
        }
    
    def cleanup_database_connections(self):
        """Clean up idle database connections"""
        try:
            # Force connection pool cleanup
            db_manager.engine.pool.dispose()
            logger.info("Database connection pool disposed")
        except Exception as e:
            logger.error(f"Error cleaning up database connections: {e}")
    
    def cleanup_cache_memory(self):
        """Clean up cache memory by removing old entries"""
        try:
            # Remove expired keys
            gaming_cache.redis_client.flushdb()
            logger.info("Cache memory cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up cache: {e}")
    
    def optimize_large_query_processing(self, query_func: Callable, chunk_size: int = 1000):
        """Process large database queries in chunks to manage memory"""
        @asynccontextmanager
        async def chunked_processor():
            offset = 0
            while True:
                # Check memory before processing chunk
                if self.monitor.record_memory_usage():
                    self.force_garbage_collection()
                
                # Process chunk
                chunk = query_func(limit=chunk_size, offset=offset)
                if not chunk:
                    break
                
                yield chunk
                offset += chunk_size
                
                # Force garbage collection after each chunk
                if offset % (chunk_size * 10) == 0:  # Every 10 chunks
                    gc.collect()
        
        return chunked_processor()
    
    def monitor_blockchain_event_processing(self):
        """Monitor memory usage during blockchain event processing"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                start_memory = self.monitor.get_memory_metrics()
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    end_memory = self.monitor.get_memory_metrics()
                    memory_diff = end_memory.process_memory - start_memory.process_memory
                    
                    if memory_diff > 100:  # More than 100MB increase
                        logger.warning(f"High memory usage in {func.__name__}: +{memory_diff:.1f}MB")
                        self.force_garbage_collection()
            
            return wrapper
        return decorator
    
    def create_object_pool(self, pool_name: str, factory_func: Callable, max_size: int = 100):
        """Create an object pool for reusing expensive objects"""
        if pool_name not in self.object_pools:
            self.object_pools[pool_name] = {
                'objects': [],
                'factory': factory_func,
                'max_size': max_size,
                'created': 0,
                'reused': 0
            }
    
    def get_pooled_object(self, pool_name: str):
        """Get object from pool or create new one"""
        if pool_name not in self.object_pools:
            raise ValueError(f"Object pool '{pool_name}' not found")
        
        pool = self.object_pools[pool_name]
        
        if pool['objects']:
            obj = pool['objects'].pop()
            pool['reused'] += 1
            return obj
        else:
            obj = pool['factory']()
            pool['created'] += 1
            return obj
    
    def return_pooled_object(self, pool_name: str, obj):
        """Return object to pool for reuse"""
        if pool_name not in self.object_pools:
            return
        
        pool = self.object_pools[pool_name]
        
        if len(pool['objects']) < pool['max_size']:
            # Reset object state if needed
            if hasattr(obj, 'reset'):
                obj.reset()
            pool['objects'].append(obj)
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Get object pool statistics"""
        stats = {}
        for pool_name, pool in self.object_pools.items():
            stats[pool_name] = {
                'available': len(pool['objects']),
                'max_size': pool['max_size'],
                'total_created': pool['created'],
                'total_reused': pool['reused'],
                'reuse_rate': pool['reused'] / max(1, pool['created'] + pool['reused'])
            }
        return stats


class WebSocketMemoryManager:
    """Manage memory for WebSocket connections"""
    
    def __init__(self):
        self.active_connections = weakref.WeakSet()
        self.connection_data = {}
        self.max_connections = settings.data_collection.websocket_max_connections
    
    def register_connection(self, connection_id: str, connection):
        """Register new WebSocket connection"""
        if len(self.active_connections) >= self.max_connections:
            logger.warning(f"Maximum WebSocket connections reached: {self.max_connections}")
            return False
        
        self.active_connections.add(connection)
        self.connection_data[connection_id] = {
            'created_at': datetime.utcnow(),
            'last_activity': datetime.utcnow(),
            'message_count': 0
        }
        return True
    
    def update_connection_activity(self, connection_id: str):
        """Update connection activity timestamp"""
        if connection_id in self.connection_data:
            self.connection_data[connection_id]['last_activity'] = datetime.utcnow()
            self.connection_data[connection_id]['message_count'] += 1
    
    def cleanup_inactive_connections(self, timeout_minutes: int = 30):
        """Clean up inactive WebSocket connections"""
        cutoff = datetime.utcnow() - timedelta(minutes=timeout_minutes)
        inactive_connections = []
        
        for conn_id, data in self.connection_data.items():
            if data['last_activity'] < cutoff:
                inactive_connections.append(conn_id)
        
        for conn_id in inactive_connections:
            del self.connection_data[conn_id]
            logger.info(f"Cleaned up inactive WebSocket connection: {conn_id}")
        
        return len(inactive_connections)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics"""
        return {
            'active_connections': len(self.active_connections),
            'max_connections': self.max_connections,
            'total_registered': len(self.connection_data),
            'avg_messages_per_connection': sum(
                data['message_count'] for data in self.connection_data.values()
            ) / max(1, len(self.connection_data))
        }


# Global instances
memory_optimizer = MemoryOptimizer()
websocket_memory_manager = WebSocketMemoryManager()


async def start_memory_monitoring():
    """Start background memory monitoring task"""
    while True:
        try:
            # Record memory usage
            needs_cleanup = memory_optimizer.monitor.record_memory_usage()
            
            if needs_cleanup:
                logger.info("Starting memory cleanup due to high usage")
                memory_optimizer.force_garbage_collection()
                memory_optimizer.cleanup_database_connections()
            
            # Clean up inactive WebSocket connections
            websocket_memory_manager.cleanup_inactive_connections()
            
            # Wait 60 seconds before next check
            await asyncio.sleep(60)
            
        except Exception as e:
            logger.error(f"Error in memory monitoring: {e}")
            await asyncio.sleep(60)


def get_system_performance_report() -> Dict[str, Any]:
    """Get comprehensive system performance report"""
    memory_metrics = memory_optimizer.monitor.get_memory_metrics()
    memory_trend = memory_optimizer.monitor.get_memory_trend()
    pool_stats = memory_optimizer.get_pool_stats()
    websocket_stats = websocket_memory_manager.get_connection_stats()
    
    return {
        'memory_metrics': memory_metrics.__dict__,
        'memory_trend': memory_trend,
        'object_pools': pool_stats,
        'websocket_connections': websocket_stats,
        'database_pool': db_manager.get_connection_pool_status(),
        'cache_stats': gaming_cache.get_cache_stats(),
        'timestamp': datetime.utcnow().isoformat()
    }
