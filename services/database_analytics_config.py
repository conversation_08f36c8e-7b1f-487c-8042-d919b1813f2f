"""
Database-Driven Analytics Configuration Service
Provides dynamic gaming project configuration from database instead of hardcoded data
"""
import logging
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from sqlalchemy.orm import Session
from sqlalchemy import and_

from models.base import SessionLocal
from models.gaming import GamingProject
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class TokenConfig:
    """Token configuration from database"""
    symbol: str
    contract_address: str
    token_type: str
    coingecko_link: str = ""
    blockchain_scanner_link: str = ""
    total_supply: Optional[str] = None


@dataclass
class NFTConfig:
    """NFT configuration from database"""
    contract_address: str
    name: str
    function: str
    marketplace_link: str = ""
    blockchain_scanner_link: str = ""
    holders_count: Optional[int] = None


@dataclass
class ProjectAnalyticsConfig:
    """Complete project configuration for analytics"""
    # Basic info
    project_name: str
    slug: str
    blockchain: str
    status: str
    website: str = ""
    
    # Tokens
    tokens: List[TokenConfig]
    
    # NFTs
    nfts: List[NFTConfig]
    involves_nfts: bool = False
    
    # Analytics data
    daily_active_users: Optional[int] = None
    daily_unique_active_wallets: Optional[int] = None
    
    # Social links
    twitter_link: str = ""
    discord_link: str = ""
    telegram_link: str = ""
    
    # Metadata
    is_active: bool = True
    extra_metadata: Dict[str, Any] = None


class DatabaseAnalyticsConfig:
    """Database-driven analytics configuration manager"""
    
    def __init__(self):
        self._cache = {}
        self._cache_timestamp = None
        
    def get_all_projects(self, active_only: bool = True) -> Dict[str, ProjectAnalyticsConfig]:
        """Get all gaming projects from database"""
        try:
            with SessionLocal() as session:
                query = session.query(GamingProject)
                if active_only:
                    query = query.filter(GamingProject.is_active == True)
                
                projects = query.all()
                
                result = {}
                for project in projects:
                    config = self._project_to_config(project)
                    if config:
                        result[config.slug] = config
                
                logger.info(f"✅ Loaded {len(result)} gaming projects from database")
                return result
                
        except Exception as e:
            logger.error(f"❌ Error loading projects from database: {e}")
            return {}
    
    def get_project(self, slug: str) -> Optional[ProjectAnalyticsConfig]:
        """Get specific project configuration"""
        try:
            with SessionLocal() as session:
                project = session.query(GamingProject).filter(
                    GamingProject.slug == slug
                ).first()
                
                if project:
                    return self._project_to_config(project)
                else:
                    logger.warning(f"⚠️ Project not found: {slug}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error loading project {slug}: {e}")
            return None
    
    def get_projects_by_blockchain(self, blockchain: str) -> Dict[str, ProjectAnalyticsConfig]:
        """Get projects filtered by blockchain"""
        try:
            with SessionLocal() as session:
                projects = session.query(GamingProject).filter(
                    and_(
                        GamingProject.blockchain == blockchain,
                        GamingProject.is_active == True
                    )
                ).all()
                
                result = {}
                for project in projects:
                    config = self._project_to_config(project)
                    if config:
                        result[config.slug] = config
                
                return result
                
        except Exception as e:
            logger.error(f"❌ Error loading {blockchain} projects: {e}")
            return {}
    
    def get_projects_with_tokens(self) -> Dict[str, ProjectAnalyticsConfig]:
        """Get projects that have tokens"""
        projects = self.get_all_projects()
        return {
            slug: config for slug, config in projects.items()
            if config.tokens
        }
    
    def get_projects_with_nfts(self) -> Dict[str, ProjectAnalyticsConfig]:
        """Get projects that have NFTs"""
        projects = self.get_all_projects()
        return {
            slug: config for slug, config in projects.items()
            if config.involves_nfts and config.nfts
        }
    
    def get_all_token_symbols(self) -> List[str]:
        """Get all unique token symbols"""
        projects = self.get_all_projects()
        symbols = set()
        for project in projects.values():
            for token in project.tokens:
                if token.symbol:
                    symbols.add(token.symbol)
        return list(symbols)
    
    def get_all_contract_addresses(self) -> List[str]:
        """Get all unique contract addresses"""
        projects = self.get_all_projects()
        addresses = set()
        for project in projects.values():
            # Token contracts
            for token in project.tokens:
                if token.contract_address:
                    addresses.add(token.contract_address)
            # NFT contracts
            for nft in project.nfts:
                if nft.contract_address:
                    addresses.add(nft.contract_address)
        return list(addresses)

    def _project_to_config(self, project: GamingProject) -> Optional[ProjectAnalyticsConfig]:
        """Convert database project to analytics configuration"""
        try:
            # Generate slug if not present
            slug = project.slug or self._generate_slug(project.project_name)

            # Extract tokens
            tokens = []
            if project.token1_symbol:
                tokens.append(TokenConfig(
                    symbol=project.token1_symbol,
                    contract_address=project.token1_contract_address or "",
                    token_type=project.token1_type or "",
                    coingecko_link=project.token1_coingecko_link or "",
                    blockchain_scanner_link=project.token1_blockchain_scanner_link or "",
                    total_supply=project.token1_total_supply
                ))

            if project.token2_symbol:
                tokens.append(TokenConfig(
                    symbol=project.token2_symbol,
                    contract_address=project.token2_contract_address or "",
                    token_type=project.token2_type or "",
                    coingecko_link=project.token2_coingecko_link or "",
                    blockchain_scanner_link=project.token2_blockchain_scanner_link or "",
                    total_supply=project.token2_total_supply
                ))

            # Extract NFTs
            nfts = []
            if project.involves_nfts and project.nft_contract_address:
                nfts.append(NFTConfig(
                    contract_address=project.nft_contract_address,
                    name=project.nft_name or "",
                    function=project.nft_function or "",
                    marketplace_link=project.nft_marketplace_link or "",
                    blockchain_scanner_link=project.nft_blockchain_scanner_link or "",
                    holders_count=project.nft_holding_wallets_count
                ))

            return ProjectAnalyticsConfig(
                project_name=project.project_name,
                slug=slug,
                blockchain=project.blockchain or "",
                status=project.validated_game_status or "",
                website=project.project_website_link or "",
                tokens=tokens,
                nfts=nfts,
                involves_nfts=project.involves_nfts or False,
                daily_active_users=project.daily_active_users,
                daily_unique_active_wallets=project.daily_unique_active_wallets,
                twitter_link=project.twitter_link or "",
                discord_link=project.discord_link or "",
                telegram_link=project.telegram_link or "",
                is_active=project.is_active or True,
                extra_metadata=project.extra_metadata or {}
            )

        except Exception as e:
            logger.error(f"❌ Error converting project {project.project_name}: {e}")
            return None

    def _generate_slug(self, project_name: str) -> str:
        """Generate URL-friendly slug from project name"""
        import re
        slug = project_name.lower()
        slug = re.sub(r'[^a-z0-9\s-]', '', slug)
        slug = re.sub(r'\s+', '-', slug)
        slug = slug.strip('-')
        return slug

    def get_project_summary(self) -> Dict[str, Any]:
        """Get analytics summary of all projects"""
        projects = self.get_all_projects()

        blockchains = set()
        total_tokens = 0
        total_nfts = 0
        projects_with_dau = 0

        for project in projects.values():
            if project.blockchain:
                blockchains.add(project.blockchain)
            total_tokens += len(project.tokens)
            total_nfts += len(project.nfts)
            if project.daily_active_users:
                projects_with_dau += 1

        return {
            'total_projects': len(projects),
            'active_projects': len([p for p in projects.values() if p.is_active]),
            'blockchains': list(blockchains),
            'projects_with_tokens': len([p for p in projects.values() if p.tokens]),
            'projects_with_nfts': len([p for p in projects.values() if p.involves_nfts]),
            'total_tokens': total_tokens,
            'total_nfts': total_nfts,
            'projects_with_dau': projects_with_dau
        }


# Global instance
database_analytics_config = DatabaseAnalyticsConfig()
