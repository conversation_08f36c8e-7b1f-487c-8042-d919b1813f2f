"""
Gaming Contract Vetting Service
Implements comprehensive vetting process for gaming contract addresses
Based on blockchainScraperDraft.md lines 17-32 guidelines
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import re
import aiohttp

from config.settings import get_settings
from blockchain.multi_chain_client import multi_chain_manager
from blockchain.data_clients.etherscan import etherscan_client
from blockchain.data_clients.solscan import solscan_client

settings = get_settings()
logger = logging.getLogger(__name__)


class VettingResult(Enum):
    """Contract vetting results"""
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_REVIEW = "needs_review"
    INSUFFICIENT_DATA = "insufficient_data"


@dataclass
class ContractVettingReport:
    """Comprehensive vetting report for a contract"""
    contract_address: str
    blockchain: str
    result: VettingResult
    confidence_score: float  # 0.0 to 1.0
    
    # Analysis results
    function_analysis: Dict[str, Any]
    event_analysis: Dict[str, Any]
    metadata_analysis: Dict[str, Any]
    behavioral_analysis: Dict[str, Any]
    
    # Detected patterns
    gaming_patterns: List[str]
    blacklist_patterns: List[str]
    gaming_indicators: List[str]  # Combined gaming indicators from all analysis

    # Additional data
    token_standards: List[str]
    social_links: List[str]
    transaction_patterns: Dict[str, Any]
    
    # Vetting metadata
    analysis_timestamp: datetime
    reviewer_notes: Optional[str] = None
    manual_override: Optional[bool] = None


class ContractVettingService:
    """Service for vetting gaming contract addresses"""
    
    def __init__(self):
        self.gaming_function_patterns = [
            # Core gaming functions
            r'mint.*item', r'craft.*', r'upgrade.*', r'battle.*', r'fight.*',
            r'breed.*', r'hatch.*', r'evolve.*', r'level.*up', r'gain.*exp',
            r'claim.*reward', r'harvest.*', r'stake.*', r'unstake.*',
            r'buy.*item', r'sell.*item', r'trade.*', r'auction.*',
            
            # Game mechanics
            r'start.*game', r'end.*game', r'join.*game', r'leave.*game',
            r'create.*character', r'delete.*character', r'move.*',
            r'attack.*', r'defend.*', r'heal.*', r'cast.*spell',
            
            # Economy functions
            r'deposit.*token', r'withdraw.*token', r'earn.*', r'spend.*',
            r'buy.*land', r'sell.*land', r'rent.*', r'lease.*'
        ]
        
        self.gaming_event_patterns = [
            # Player events
            r'Player.*Joined', r'Player.*Left', r'Player.*Created',
            r'Character.*Created', r'Character.*Deleted', r'Character.*Updated',
            
            # Game events
            r'Game.*Started', r'Game.*Ended', r'Battle.*Started', r'Battle.*Ended',
            r'Level.*Up', r'Experience.*Gained', r'Achievement.*Unlocked',
            
            # Item events
            r'Item.*Crafted', r'Item.*Upgraded', r'Item.*Traded', r'Item.*Sold',
            r'NFT.*Minted', r'NFT.*Burned', r'NFT.*Transferred',
            
            # Economy events
            r'Reward.*Claimed', r'Token.*Earned', r'Token.*Spent',
            r'Land.*Purchased', r'Land.*Sold', r'Rent.*Paid'
        ]
        
        self.gaming_keywords = [
            'game', 'gaming', 'play', 'player', 'character', 'avatar',
            'item', 'weapon', 'armor', 'spell', 'magic', 'battle',
            'fight', 'quest', 'adventure', 'dungeon', 'raid',
            'guild', 'clan', 'team', 'tournament', 'competition',
            'level', 'experience', 'skill', 'ability', 'power',
            'land', 'property', 'building', 'city', 'world',
            'pet', 'monster', 'creature', 'beast', 'dragon',
            'card', 'deck', 'collection', 'trading', 'marketplace'
        ]
        
        self.blacklist_patterns = [
            # DeFi patterns that might be confused with gaming
            r'swap.*', r'liquidity.*', r'pool.*', r'farm.*yield',
            r'borrow.*', r'lend.*', r'flash.*loan', r'arbitrage.*',
            
            # Pure NFT marketplaces
            r'list.*nft', r'delist.*nft', r'bid.*nft', r'auction.*nft',
            
            # Governance tokens
            r'vote.*', r'propose.*', r'delegate.*', r'governance.*'
        ]
    
    async def vet_contract(self, contract_address: str, blockchain: str) -> ContractVettingReport:
        """
        Comprehensive vetting of a gaming contract
        Implements the vetting process from blockchainScraperDraft.md lines 17-32
        """
        try:
            logger.info(f"Starting vetting process for {contract_address} on {blockchain}")

            # Use specialized API clients for enhanced analysis
            if blockchain.lower() in ['ethereum', 'eth']:
                # Use Etherscan for detailed Ethereum analysis
                async with etherscan_client:
                    api_analysis = await etherscan_client.analyze_contract_for_gaming(contract_address)

                # Get blockchain client for additional data
                client = multi_chain_manager.get_client(blockchain)
                if not client:
                    raise ValueError(f"No client available for blockchain: {blockchain}")

                # Combine API analysis with blockchain client analysis
                function_analysis = await self._analyze_contract_functions(client, contract_address)
                event_analysis = await self._analyze_contract_events(client, contract_address)
                metadata_analysis = await self._analyze_contract_metadata(client, contract_address)
                behavioral_analysis = await self._analyze_behavioral_patterns(client, contract_address)

                # Enhance with Etherscan data
                if api_analysis and not api_analysis.get('error'):
                    metadata_analysis['etherscan_data'] = api_analysis
                    metadata_analysis['contract_name'] = api_analysis.get('contract_name', '')
                    metadata_analysis['gaming_indicators'] = api_analysis.get('gaming_indicators', [])

            elif blockchain.lower() in ['solana', 'sol']:
                # Use Solscan for detailed Solana analysis
                async with solscan_client:
                    api_analysis = await solscan_client.analyze_account_for_gaming(contract_address)

                # Get blockchain client for additional data
                client = multi_chain_manager.get_client(blockchain)
                if not client:
                    raise ValueError(f"No client available for blockchain: {blockchain}")

                # Combine API analysis with blockchain client analysis
                function_analysis = await self._analyze_contract_functions(client, contract_address)
                event_analysis = await self._analyze_contract_events(client, contract_address)
                metadata_analysis = await self._analyze_contract_metadata(client, contract_address)
                behavioral_analysis = await self._analyze_behavioral_patterns(client, contract_address)

                # Enhance with Solscan data
                if api_analysis and not api_analysis.get('error'):
                    metadata_analysis['solscan_data'] = api_analysis
                    metadata_analysis['account_exists'] = api_analysis.get('account_exists', False)
                    metadata_analysis['gaming_indicators'] = api_analysis.get('gaming_indicators', [])

            else:
                # Use standard blockchain client for other chains
                client = multi_chain_manager.get_client(blockchain)
                if not client:
                    raise ValueError(f"No client available for blockchain: {blockchain}")

                # Perform standard analysis
                function_analysis = await self._analyze_contract_functions(client, contract_address)
                event_analysis = await self._analyze_contract_events(client, contract_address)
                metadata_analysis = await self._analyze_contract_metadata(client, contract_address)
                behavioral_analysis = await self._analyze_behavioral_patterns(client, contract_address)
            
            # Extract patterns and standards
            gaming_patterns = self._extract_gaming_patterns(function_analysis, event_analysis, metadata_analysis)
            blacklist_patterns = self._extract_blacklist_patterns(function_analysis, event_analysis, metadata_analysis)
            token_standards = self._detect_token_standards(function_analysis)
            social_links = self._extract_social_links(metadata_analysis)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                function_analysis, event_analysis, metadata_analysis, 
                behavioral_analysis, gaming_patterns, blacklist_patterns
            )
            
            # Determine vetting result
            result = self._determine_vetting_result(confidence_score, gaming_patterns, blacklist_patterns)

            # Combine all gaming indicators
            all_gaming_indicators = gaming_patterns.copy()
            if metadata_analysis.get('gaming_indicators'):
                all_gaming_indicators.extend(metadata_analysis['gaming_indicators'])

            return ContractVettingReport(
                contract_address=contract_address,
                blockchain=blockchain,
                result=result,
                confidence_score=confidence_score,
                function_analysis=function_analysis,
                event_analysis=event_analysis,
                metadata_analysis=metadata_analysis,
                behavioral_analysis=behavioral_analysis,
                gaming_patterns=gaming_patterns,
                blacklist_patterns=blacklist_patterns,
                gaming_indicators=all_gaming_indicators,
                token_standards=token_standards,
                social_links=social_links,
                transaction_patterns=behavioral_analysis.get('transaction_patterns', {}),
                analysis_timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Error vetting contract {contract_address}: {e}")
            return ContractVettingReport(
                contract_address=contract_address,
                blockchain=blockchain,
                result=VettingResult.INSUFFICIENT_DATA,
                confidence_score=0.0,
                function_analysis={},
                event_analysis={},
                metadata_analysis={},
                behavioral_analysis={},
                gaming_patterns=[],
                blacklist_patterns=[],
                gaming_indicators=[],
                token_standards=[],
                social_links=[],
                transaction_patterns={},
                analysis_timestamp=datetime.utcnow(),
                reviewer_notes=f"Vetting failed: {str(e)}"
            )
    
    async def _analyze_contract_functions(self, client, contract_address: str) -> Dict[str, Any]:
        """Analyze contract functions for gaming-related patterns"""
        try:
            # Get contract bytecode
            contract_code = await client.get_contract_code(contract_address)
            if not contract_code or contract_code == '0x':
                return {"error": "No contract code found", "functions": []}
            
            # Extract function signatures (simplified - in production would use more sophisticated analysis)
            function_signatures = await self._extract_function_signatures(client, contract_address)
            
            gaming_functions = []
            for func in function_signatures:
                for pattern in self.gaming_function_patterns:
                    if re.search(pattern, func, re.IGNORECASE):
                        gaming_functions.append(func)
                        break
            
            return {
                "total_functions": len(function_signatures),
                "gaming_functions": gaming_functions,
                "gaming_function_count": len(gaming_functions),
                "function_signatures": function_signatures[:20]  # Limit for storage
            }
            
        except Exception as e:
            logger.error(f"Error analyzing contract functions: {e}")
            return {"error": str(e), "functions": []}
    
    async def _analyze_contract_events(self, client, contract_address: str) -> Dict[str, Any]:
        """Analyze contract events for gaming-related patterns"""
        try:
            # Extract event signatures
            event_signatures = await self._extract_event_signatures(client, contract_address)
            
            gaming_events = []
            for event in event_signatures:
                for pattern in self.gaming_event_patterns:
                    if re.search(pattern, event, re.IGNORECASE):
                        gaming_events.append(event)
                        break
            
            return {
                "total_events": len(event_signatures),
                "gaming_events": gaming_events,
                "gaming_event_count": len(gaming_events),
                "event_signatures": event_signatures[:20]  # Limit for storage
            }
            
        except Exception as e:
            logger.error(f"Error analyzing contract events: {e}")
            return {"error": str(e), "events": []}
    
    async def _analyze_contract_metadata(self, client, contract_address: str) -> Dict[str, Any]:
        """Analyze contract metadata for gaming indicators"""
        try:
            metadata = await self._get_contract_metadata(client, contract_address)
            
            gaming_keywords_found = []
            name = metadata.get('name', '').lower()
            symbol = metadata.get('symbol', '').lower()
            
            for keyword in self.gaming_keywords:
                if keyword in name or keyword in symbol:
                    gaming_keywords_found.append(keyword)
            
            return {
                "name": metadata.get('name', ''),
                "symbol": metadata.get('symbol', ''),
                "gaming_keywords_found": gaming_keywords_found,
                "gaming_keyword_count": len(gaming_keywords_found),
                "metadata": metadata
            }
            
        except Exception as e:
            logger.error(f"Error analyzing contract metadata: {e}")
            return {"error": str(e), "metadata": {}}
    
    async def _analyze_behavioral_patterns(self, client, contract_address: str) -> Dict[str, Any]:
        """Analyze transaction patterns and user interactions"""
        try:
            # This would analyze recent transactions, user patterns, etc.
            # For now, return basic structure
            return {
                "transaction_frequency": "unknown",
                "user_interaction_patterns": "unknown",
                "multi_contract_interactions": False,
                "token_economics_detected": False
            }
            
        except Exception as e:
            logger.error(f"Error analyzing behavioral patterns: {e}")
            return {"error": str(e)}

    def _extract_gaming_patterns(self, function_analysis: Dict, event_analysis: Dict, metadata_analysis: Dict) -> List[str]:
        """Extract detected gaming patterns"""
        patterns = []

        # Function patterns
        if function_analysis.get('gaming_function_count', 0) > 0:
            patterns.extend([f"gaming_function:{func}" for func in function_analysis.get('gaming_functions', [])])

        # Event patterns
        if event_analysis.get('gaming_event_count', 0) > 0:
            patterns.extend([f"gaming_event:{event}" for event in event_analysis.get('gaming_events', [])])

        # Metadata patterns
        if metadata_analysis.get('gaming_keyword_count', 0) > 0:
            patterns.extend([f"gaming_keyword:{keyword}" for keyword in metadata_analysis.get('gaming_keywords_found', [])])

        return patterns

    def _extract_blacklist_patterns(self, function_analysis: Dict, event_analysis: Dict, metadata_analysis: Dict) -> List[str]:
        """Extract detected blacklist patterns that indicate non-gaming contracts"""
        patterns = []

        # Check function signatures against blacklist
        for func in function_analysis.get('function_signatures', []):
            for pattern in self.blacklist_patterns:
                if re.search(pattern, func, re.IGNORECASE):
                    patterns.append(f"blacklist_function:{func}")

        return patterns

    def _detect_token_standards(self, function_analysis: Dict) -> List[str]:
        """Detect token standards used by the contract"""
        standards = []
        functions = function_analysis.get('function_signatures', [])

        # ERC-721 (NFT) detection
        erc721_functions = ['ownerOf', 'transferFrom', 'approve', 'tokenURI']
        if all(any(func in f for f in functions) for func in erc721_functions):
            standards.append('ERC-721')

        # ERC-1155 (Multi-token) detection
        erc1155_functions = ['balanceOf', 'balanceOfBatch', 'safeTransferFrom', 'safeBatchTransferFrom']
        if all(any(func in f for f in functions) for func in erc1155_functions):
            standards.append('ERC-1155')

        # ERC-20 detection
        erc20_functions = ['transfer', 'transferFrom', 'approve', 'balanceOf']
        if all(any(func in f for f in functions) for func in erc20_functions):
            standards.append('ERC-20')

        return standards

    def _extract_social_links(self, metadata_analysis: Dict) -> List[str]:
        """Extract social media links from contract metadata"""
        # This would analyze contract metadata, IPFS data, etc. for social links
        # For now, return empty list - would be implemented with actual metadata parsing
        return []

    def _calculate_confidence_score(
        self,
        function_analysis: Dict,
        event_analysis: Dict,
        metadata_analysis: Dict,
        behavioral_analysis: Dict,
        gaming_patterns: List[str],
        blacklist_patterns: List[str]
    ) -> float:
        """Calculate confidence score for gaming classification"""
        score = 0.0

        # Function analysis (40% weight)
        gaming_func_count = function_analysis.get('gaming_function_count', 0)
        total_func_count = function_analysis.get('total_functions', 1)
        function_score = min(gaming_func_count / max(total_func_count, 1) * 2, 0.4)
        score += function_score

        # Event analysis (30% weight)
        gaming_event_count = event_analysis.get('gaming_event_count', 0)
        total_event_count = event_analysis.get('total_events', 1)
        event_score = min(gaming_event_count / max(total_event_count, 1) * 2, 0.3)
        score += event_score

        # Metadata analysis (20% weight)
        gaming_keyword_count = metadata_analysis.get('gaming_keyword_count', 0)
        metadata_score = min(gaming_keyword_count * 0.05, 0.2)
        score += metadata_score

        # Token standards bonus (10% weight)
        if 'ERC-721' in self._detect_token_standards(function_analysis):
            score += 0.05  # NFTs are common in games
        if 'ERC-1155' in self._detect_token_standards(function_analysis):
            score += 0.05  # Multi-tokens are common in games

        # Blacklist penalty
        blacklist_penalty = len(blacklist_patterns) * 0.1
        score = max(0.0, score - blacklist_penalty)

        return min(1.0, score)

    def _determine_vetting_result(self, confidence_score: float, gaming_patterns: List[str], blacklist_patterns: List[str]) -> VettingResult:
        """Determine final vetting result based on analysis"""

        # Strong blacklist indicators
        if len(blacklist_patterns) > 3:
            return VettingResult.REJECTED

        # High confidence gaming contract
        if confidence_score >= 0.7 and len(gaming_patterns) >= 3:
            return VettingResult.APPROVED

        # Medium confidence - needs manual review
        if confidence_score >= 0.4 and len(gaming_patterns) >= 1:
            return VettingResult.NEEDS_REVIEW

        # Low confidence or insufficient data
        if confidence_score < 0.4:
            return VettingResult.REJECTED if len(blacklist_patterns) > 0 else VettingResult.INSUFFICIENT_DATA

        return VettingResult.NEEDS_REVIEW

    async def _extract_function_signatures(self, client, contract_address: str) -> List[str]:
        """Extract function signatures from contract (simplified implementation)"""
        try:
            # This would use more sophisticated bytecode analysis in production
            # For now, return common gaming function signatures as example
            return [
                'mint(address,uint256)', 'transfer(address,uint256)', 'approve(address,uint256)',
                'ownerOf(uint256)', 'tokenURI(uint256)', 'balanceOf(address)',
                'craftItem(uint256,uint256)', 'upgradeItem(uint256)', 'battleStart(uint256,uint256)',
                'claimReward(uint256)', 'stakeToken(uint256)', 'harvestRewards()'
            ]
        except Exception as e:
            logger.error(f"Error extracting function signatures: {e}")
            return []

    async def _extract_event_signatures(self, client, contract_address: str) -> List[str]:
        """Extract event signatures from contract"""
        try:
            # This would analyze contract ABI or bytecode for events
            # For now, return common gaming event signatures as example
            return [
                'Transfer(address,address,uint256)', 'Approval(address,address,uint256)',
                'ItemCrafted(address,uint256,uint256)', 'PlayerJoined(address,uint256)',
                'BattleStarted(uint256,address,address)', 'RewardClaimed(address,uint256)',
                'LevelUp(address,uint256,uint256)', 'TokenEarned(address,uint256)'
            ]
        except Exception as e:
            logger.error(f"Error extracting event signatures: {e}")
            return []

    async def _get_contract_metadata(self, client, contract_address: str) -> Dict[str, Any]:
        """Get contract metadata including name, symbol, etc."""
        try:
            # This would call contract methods to get metadata
            # For now, return example metadata
            return {
                'name': 'Example Gaming Token',
                'symbol': 'EGT',
                'decimals': 18,
                'totalSupply': 1000000
            }
        except Exception as e:
            logger.error(f"Error getting contract metadata: {e}")
            return {}


# Global instance
contract_vetting_service = ContractVettingService()
