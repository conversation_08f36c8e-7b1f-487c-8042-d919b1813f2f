"""
Gaming Project Creator Service
Automatically creates gaming project entries from blockchain contract detection
"""
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from models.base import get_db
from models.gaming import GamingProject, GamingContract
from scrapers.blockchain.contract_detector import ContractAnalysisResult, ContractConfidence
from config.gaming_config import gaming_project_manager
from services.notification_service import notification_service

logger = logging.getLogger(__name__)


class GamingProjectCreator:
    """Creates gaming projects from blockchain contract detection"""
    
    def __init__(self):
        self.pending_projects = {}  # Cache for projects pending review
        
    async def create_project_from_contract(
        self, 
        analysis_result: ContractAnalysisResult,
        db: Session = None
    ) -> Optional[GamingProject]:
        """
        Create a gaming project from blockchain contract analysis
        
        Args:
            analysis_result: Result from contract analysis
            db: Database session
            
        Returns:
            Created GamingProject or None if creation failed
        """
        if not db:
            db = next(get_db())
            
        try:
            # Only create projects for high-confidence contracts
            if analysis_result.confidence not in [ContractConfidence.HIGH, ContractConfidence.MEDIUM]:
                logger.debug(f"Skipping project creation for low-confidence contract: {analysis_result.contract_address}")
                return None
                
            # Check if project already exists
            existing_project = self._find_existing_project(db, analysis_result)
            if existing_project:
                logger.info(f"Project already exists for contract {analysis_result.contract_address}")
                return existing_project
                
            # Extract project information from contract analysis
            project_data = self._extract_project_data(analysis_result)
            
            # Create new gaming project
            gaming_project = GamingProject(**project_data)
            
            db.add(gaming_project)
            db.commit()
            db.refresh(gaming_project)
            
            logger.info(f"✅ Created new gaming project: {gaming_project.project_name} from contract {analysis_result.contract_address}")

            # Send notification for new gaming project
            await notification_service.notify_new_gaming_project(
                project_name=gaming_project.project_name,
                contract_address=analysis_result.contract_address,
                blockchain=analysis_result.blockchain,
                confidence=analysis_result.confidence.value,
                project_id=gaming_project.id
            )

            # Add to pending review if metadata is incomplete
            if self._needs_admin_attention(project_data):
                await self._mark_for_admin_review(gaming_project.id, analysis_result)

                # Send additional notification for admin review
                await notification_service.notify_project_needs_review(
                    project_name=gaming_project.project_name,
                    project_id=gaming_project.id,
                    reason="Incomplete metadata detected from blockchain analysis"
                )

            return gaming_project
            
        except IntegrityError as e:
            db.rollback()
            logger.warning(f"Project already exists or constraint violation: {e}")
            return None
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating gaming project from contract {analysis_result.contract_address}: {e}")
            return None
            
    def _find_existing_project(self, db: Session, analysis_result: ContractAnalysisResult) -> Optional[GamingProject]:
        """Check if a project already exists for this contract"""
        # Check by contract address in token fields
        existing = db.query(GamingProject).filter(
            (GamingProject.token_1_contract_address == analysis_result.contract_address.lower()) |
            (GamingProject.token_2_contract_address == analysis_result.contract_address.lower())
        ).first()
        
        return existing
        
    def _extract_project_data(self, analysis_result: ContractAnalysisResult) -> Dict[str, Any]:
        """Extract gaming project data from contract analysis"""
        
        # Generate project name from contract metadata or use placeholder
        project_name = self._generate_project_name(analysis_result)
        
        # Extract token symbol if available
        token_symbol = self._extract_token_symbol(analysis_result)
        
        # Determine blockchain network
        blockchain = self._normalize_blockchain_name(analysis_result.blockchain)
        
        # Create project data with minimum required fields
        project_data = {
            'project_name': project_name,
            'blockchain': blockchain,
            'token_1_contract_address': analysis_result.contract_address.lower(),
            'token_1_symbol': token_symbol,
            'validated_game_status': 'Live' if analysis_result.confidence == ContractConfidence.HIGH else 'Development',
            'primary_genre': 'Strategy games emphasizing planning, resource management and decision-making',  # Default
            'subcategory': 'Blockchain gaming project detected via smart contract analysis',
            'is_active': True,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow(),
            
            # Metadata from blockchain analysis
            'game_status_notes': f"Auto-detected from blockchain contract analysis. Confidence: {analysis_result.confidence.value}",
            'additional_notes': f"Contract patterns detected: {', '.join(analysis_result.detected_patterns)}",
            
            # Mark for admin attention if needed
            'admin_notes': self._generate_admin_notes(analysis_result),
        }
        
        # Add additional metadata if available
        if analysis_result.metadata:
            project_data.update(self._extract_additional_metadata(analysis_result.metadata))
            
        return project_data
        
    def _generate_project_name(self, analysis_result: ContractAnalysisResult) -> str:
        """Generate a project name from contract analysis"""
        
        # Try to extract name from metadata
        if analysis_result.metadata:
            name = analysis_result.metadata.get('name') or analysis_result.metadata.get('symbol')
            if name and len(name) > 2:
                return f"{name} (New Gaming Contract)"
                
        # Use contract address as fallback
        short_address = analysis_result.contract_address[:8] + "..." + analysis_result.contract_address[-6:]
        return f"Gaming Contract {short_address}"
        
    def _extract_token_symbol(self, analysis_result: ContractAnalysisResult) -> Optional[str]:
        """Extract token symbol from contract analysis"""
        if analysis_result.metadata:
            symbol = analysis_result.metadata.get('symbol')
            if symbol and len(symbol) <= 10:  # Reasonable symbol length
                return symbol.upper()
        return None
        
    def _normalize_blockchain_name(self, blockchain: str) -> str:
        """Normalize blockchain name to match database values"""
        blockchain_mapping = {
            'ethereum': 'Ethereum',
            'polygon': 'Polygon', 
            'bsc': 'BSC',
            'binance': 'BSC',
            'arbitrum': 'Arbitrum',
            'optimism': 'Optimism',
            'avalanche': 'Avalanche',
            'solana': 'Solana',
            'ronin': 'Ronin',
            'base': 'Base'
        }
        
        return blockchain_mapping.get(blockchain.lower(), blockchain.title())
        
    def _needs_admin_attention(self, project_data: Dict[str, Any]) -> bool:
        """Check if project needs admin attention"""
        # Mark for admin attention if critical data is missing
        needs_attention = (
            not project_data.get('token_1_symbol') or
            'Gaming Contract' in project_data.get('project_name', '') or
            not project_data.get('project_website_link')
        )
        
        return needs_attention
        
    def _generate_admin_notes(self, analysis_result: ContractAnalysisResult) -> str:
        """Generate admin notes for the project"""
        notes = []
        
        if analysis_result.confidence == ContractConfidence.MEDIUM:
            notes.append("ATTN: Admin - Medium confidence contract detection")
            
        if not analysis_result.metadata.get('name') and not analysis_result.metadata.get('symbol'):
            notes.append("ATTN: Admin - No token name/symbol found")
            
        if len(analysis_result.detected_patterns) < 3:
            notes.append("ATTN: Admin - Limited gaming patterns detected")
            
        return "; ".join(notes) if notes else ""
        
    def _extract_additional_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Extract additional project data from contract metadata"""
        additional_data = {}
        
        # Extract website if available
        if 'website' in metadata:
            additional_data['project_website_link'] = metadata['website']
            
        # Extract description if available
        if 'description' in metadata:
            additional_data['game_description'] = metadata['description'][:500]  # Limit length
            
        # Extract total supply for token info
        if 'totalSupply' in metadata:
            additional_data['token_1_total_supply'] = str(metadata['totalSupply'])
            
        return additional_data
        
    async def _mark_for_admin_review(self, project_id: int, analysis_result: ContractAnalysisResult):
        """Mark project for admin review"""
        self.pending_projects[project_id] = {
            'contract_address': analysis_result.contract_address,
            'blockchain': analysis_result.blockchain,
            'confidence': analysis_result.confidence.value,
            'detected_patterns': analysis_result.detected_patterns,
            'created_at': datetime.utcnow().isoformat(),
            'needs_review': True
        }
        
        logger.info(f"🔍 Project {project_id} marked for admin review")
        
    async def get_pending_projects(self) -> Dict[int, Dict[str, Any]]:
        """Get projects pending admin review"""
        return self.pending_projects.copy()
        
    async def approve_project(self, project_id: int, db: Session = None):
        """Approve a pending project"""
        if project_id in self.pending_projects:
            del self.pending_projects[project_id]
            logger.info(f"✅ Project {project_id} approved and removed from pending review")
            
    async def reject_project(self, project_id: int, db: Session = None):
        """Reject a pending project"""
        if not db:
            db = next(get_db())
            
        try:
            # Mark project as inactive instead of deleting
            project = db.query(GamingProject).filter(GamingProject.id == project_id).first()
            if project:
                project.is_active = False
                project.admin_notes = f"{project.admin_notes}; REJECTED by admin"
                db.commit()
                
            if project_id in self.pending_projects:
                del self.pending_projects[project_id]
                
            logger.info(f"❌ Project {project_id} rejected and marked inactive")
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error rejecting project {project_id}: {e}")


# Global instance
gaming_project_creator = GamingProjectCreator()
