"""
API response optimization service for Web3 Gaming News Tracker
Implements caching, compression, and async processing for dashboard endpoints
"""
import asyncio
import gzip
import json
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable
from functools import wraps
import logging
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import text

from services.redis_cache import gaming_cache
from models.base import get_db_sync, db_manager
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class APIPerformanceMonitor:
    """Monitor and track API performance metrics"""
    
    def __init__(self):
        self.request_times = {}
        self.slow_queries = []
        self.cache_stats = {"hits": 0, "misses": 0}
    
    def record_request_time(self, endpoint: str, duration: float):
        """Record request processing time"""
        if endpoint not in self.request_times:
            self.request_times[endpoint] = []
        
        self.request_times[endpoint].append({
            "duration": duration,
            "timestamp": datetime.utcnow()
        })
        
        # Keep only last 100 requests per endpoint
        if len(self.request_times[endpoint]) > 100:
            self.request_times[endpoint] = self.request_times[endpoint][-100:]
        
        # Log slow requests
        if duration > 1.0:  # Requests taking more than 1 second
            logger.warning(f"Slow API request: {endpoint} took {duration:.2f}s")
    
    def record_cache_hit(self):
        """Record cache hit"""
        self.cache_stats["hits"] += 1
    
    def record_cache_miss(self):
        """Record cache miss"""
        self.cache_stats["misses"] += 1
    
    def get_endpoint_stats(self, endpoint: str) -> Dict[str, Any]:
        """Get performance stats for specific endpoint"""
        if endpoint not in self.request_times:
            return {}
        
        times = [req["duration"] for req in self.request_times[endpoint]]
        return {
            "avg_response_time": sum(times) / len(times),
            "max_response_time": max(times),
            "min_response_time": min(times),
            "total_requests": len(times),
            "slow_requests": len([t for t in times if t > 1.0])
        }
    
    def get_overall_stats(self) -> Dict[str, Any]:
        """Get overall API performance statistics"""
        total_hits = self.cache_stats["hits"]
        total_misses = self.cache_stats["misses"]
        total_requests = total_hits + total_misses
        
        return {
            "cache_hit_rate": total_hits / max(1, total_requests),
            "total_cache_requests": total_requests,
            "endpoints_monitored": len(self.request_times),
            "connection_pool_status": db_manager.get_connection_pool_status()
        }


# Global performance monitor
perf_monitor = APIPerformanceMonitor()


def optimize_response(
    cache_ttl: int = 300,
    compress: bool = True,
    cache_key_prefix: str = "",
    enable_cache: bool = True
):
    """Decorator for optimizing API responses with caching and compression"""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            endpoint_name = func.__name__
            
            # Extract request parameters for cache key generation
            cache_key = None
            if enable_cache:
                cache_key = gaming_cache._generate_key(
                    cache_key_prefix or f"api:{endpoint_name}",
                    *args,
                    **kwargs
                )
                
                # Try to get from cache
                cached_result = gaming_cache.get(cache_key)
                if cached_result is not None:
                    perf_monitor.record_cache_hit()
                    duration = time.time() - start_time
                    perf_monitor.record_request_time(endpoint_name, duration)
                    
                    # Return cached response with compression if enabled
                    if compress and len(json.dumps(cached_result)) > 1024:  # Compress if > 1KB
                        return create_compressed_response(cached_result)
                    return JSONResponse(content=cached_result)
            
            # Cache miss - execute function
            perf_monitor.record_cache_miss()
            
            try:
                # Execute the original function
                result = await func(*args, **kwargs)
                
                # Cache the result if caching is enabled
                if enable_cache and cache_key:
                    gaming_cache.set(cache_key, result, cache_ttl)
                
                # Record performance metrics
                duration = time.time() - start_time
                perf_monitor.record_request_time(endpoint_name, duration)
                
                # Apply compression if enabled and response is large
                if compress and len(json.dumps(result)) > 1024:
                    return create_compressed_response(result)
                
                return JSONResponse(content=result)
                
            except Exception as e:
                duration = time.time() - start_time
                perf_monitor.record_request_time(endpoint_name, duration)
                logger.error(f"Error in optimized endpoint {endpoint_name}: {e}")
                raise
        
        return wrapper
    return decorator


def create_compressed_response(data: Any) -> Response:
    """Create gzip-compressed JSON response"""
    json_str = json.dumps(data, default=str)
    compressed_data = gzip.compress(json_str.encode('utf-8'))
    
    return Response(
        content=compressed_data,
        media_type="application/json",
        headers={
            "Content-Encoding": "gzip",
            "Content-Length": str(len(compressed_data))
        }
    )


class DatabaseQueryOptimizer:
    """Optimize database queries for gaming data"""
    
    @staticmethod
    def optimize_article_queries(db: Session, filters: Dict[str, Any]) -> str:
        """Generate optimized SQL for article queries"""
        base_query = """
        SELECT a.*, s.name as source_name, s.reliability_score
        FROM articles a
        JOIN sources s ON a.source_id = s.id
        WHERE 1=1
        """
        
        conditions = []
        params = {}
        
        # Add time-based filtering (most common)
        if 'hours' in filters:
            conditions.append("a.published_at >= NOW() - INTERVAL '%s hours'")
            params['hours'] = filters['hours']
        
        # Add gaming category filtering
        if 'gaming_category' in filters:
            conditions.append("a.gaming_category = %(gaming_category)s")
            params['gaming_category'] = filters['gaming_category']
        
        # Add blockchain network filtering
        if 'blockchain_network' in filters:
            conditions.append("a.blockchain_network = %(blockchain_network)s")
            params['blockchain_network'] = filters['blockchain_network']
        
        # Add relevance threshold
        if 'min_relevance' in filters:
            conditions.append("a.relevance_score >= %(min_relevance)s")
            params['min_relevance'] = filters['min_relevance']
        
        # Combine conditions
        if conditions:
            base_query += " AND " + " AND ".join(conditions)
        
        # Add optimized ordering
        base_query += """
        ORDER BY 
            a.relevance_score DESC NULLS LAST,
            a.published_at DESC
        LIMIT %(limit)s OFFSET %(offset)s
        """
        
        params.update({
            'limit': filters.get('limit', 50),
            'offset': filters.get('offset', 0)
        })
        
        return text(base_query), params
    
    @staticmethod
    def optimize_gaming_project_queries(db: Session, filters: Dict[str, Any]) -> str:
        """Generate optimized SQL for gaming project queries"""
        base_query = """
        SELECT gp.*, 
               COUNT(a.id) as article_count,
               AVG(a.sentiment_score) as avg_sentiment
        FROM gaming_projects gp
        LEFT JOIN articles a ON a.gaming_projects ? gp.project_name
        WHERE gp.is_active = true
        """
        
        conditions = []
        params = {}
        
        if 'blockchain' in filters:
            conditions.append("gp.blockchain = %(blockchain)s")
            params['blockchain'] = filters['blockchain']
        
        if 'category' in filters:
            conditions.append("gp.category = %(category)s")
            params['category'] = filters['category']
        
        if conditions:
            base_query += " AND " + " AND ".join(conditions)
        
        base_query += """
        GROUP BY gp.id
        ORDER BY 
            gp.daily_active_users DESC NULLS LAST,
            gp.total_value_locked DESC NULLS LAST,
            article_count DESC
        LIMIT %(limit)s
        """
        
        params['limit'] = filters.get('limit', 100)
        
        return text(base_query), params


class AsyncDataProcessor:
    """Handle heavy data processing asynchronously"""
    
    def __init__(self):
        self.processing_queue = asyncio.Queue()
        self.is_processing = False
    
    async def start_background_processing(self):
        """Start background task processing"""
        if not self.is_processing:
            self.is_processing = True
            asyncio.create_task(self._process_queue())
    
    async def _process_queue(self):
        """Process queued tasks in background"""
        while self.is_processing:
            try:
                task = await asyncio.wait_for(self.processing_queue.get(), timeout=1.0)
                await task()
                self.processing_queue.task_done()
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing background task: {e}")
    
    async def queue_cache_refresh(self, cache_key: str, refresh_func: Callable, *args, **kwargs):
        """Queue cache refresh operation"""
        async def refresh_task():
            try:
                result = await refresh_func(*args, **kwargs)
                gaming_cache.set(cache_key, result, ttl=1800)  # 30 minutes
                logger.info(f"Cache refreshed for key: {cache_key}")
            except Exception as e:
                logger.error(f"Failed to refresh cache for {cache_key}: {e}")
        
        await self.processing_queue.put(refresh_task)
    
    def stop_processing(self):
        """Stop background processing"""
        self.is_processing = False


# Global async processor
async_processor = AsyncDataProcessor()


def preload_critical_data():
    """Preload critical gaming data into cache"""
    try:
        db = get_db_sync()
        
        # Preload gaming projects
        projects_query = text("""
            SELECT project_name, blockchain, category, is_active, token_symbol
            FROM gaming_projects 
            WHERE is_active = true
            ORDER BY daily_active_users DESC NULLS LAST
            LIMIT 100
        """)
        
        projects = db.execute(projects_query).fetchall()
        projects_data = [dict(row) for row in projects]
        gaming_cache.cache_gaming_projects(projects_data)
        
        # Preload recent articles summary
        articles_query = text("""
            SELECT gaming_category, COUNT(*) as count
            FROM articles 
            WHERE published_at >= NOW() - INTERVAL '24 hours'
            GROUP BY gaming_category
        """)
        
        articles_summary = db.execute(articles_query).fetchall()
        summary_data = [dict(row) for row in articles_summary]
        gaming_cache.set("articles:summary:24h", summary_data, ttl=600)
        
        logger.info("Critical gaming data preloaded into cache")
        
    except Exception as e:
        logger.error(f"Failed to preload critical data: {e}")
    finally:
        if 'db' in locals():
            db.close()


# Background processing will be started when needed
_background_task = None

async def start_background_processing():
    """Start background processing if not already running"""
    global _background_task
    if _background_task is None or _background_task.done():
        _background_task = asyncio.create_task(async_processor.start_background_processing())
    return _background_task
