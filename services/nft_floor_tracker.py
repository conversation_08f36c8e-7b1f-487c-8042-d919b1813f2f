"""
NFT Floor Price Tracker for Gaming Collections
Comprehensive NFT price monitoring, trends analysis, and collection performance tracking
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from decimal import Decimal
import aiohttp
from sqlalchemy.orm import Session

from models.base import SessionLocal
from models.gaming import N<PERSON>Collection, BlockchainData
from blockchain.nft_tracker import nft_tracker
from services.redis_cache import gaming_cache
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class NFTFloorData:
    """NFT floor price data structure"""
    collection_name: str
    collection_address: str
    chain: str
    floor_price_eth: Decimal
    floor_price_usd: Decimal
    floor_change_24h: float
    floor_change_7d: float
    floor_change_30d: float
    volume_24h_eth: Decimal
    volume_24h_usd: Decimal
    volume_change_24h: float
    sales_count_24h: int
    unique_buyers_24h: int
    unique_sellers_24h: int
    average_sale_price_24h: Decimal
    market_cap_eth: Decimal
    market_cap_usd: Decimal
    total_supply: int
    owners_count: int
    owner_percentage: float
    listed_count: int
    listed_percentage: float
    timestamp: datetime
    data_source: str


@dataclass
class CollectionTrends:
    """Collection trend analysis"""
    collection_name: str
    trend_direction: str  # 'bullish', 'bearish', 'sideways'
    momentum_score: float  # -100 to 100
    volatility_score: float  # 0 to 100
    liquidity_score: float  # 0 to 100
    rarity_premium: float  # Premium for rare items
    whale_activity: bool
    social_sentiment: str  # 'positive', 'negative', 'neutral'
    price_prediction_7d: Dict[str, float]  # confidence intervals
    support_levels: List[float]
    resistance_levels: List[float]
    timestamp: datetime


@dataclass
class GameNFTMetrics:
    """Game-specific NFT metrics"""
    protocol_name: str
    collection_name: str
    utility_score: float  # How useful NFTs are in-game
    earning_potential: Decimal  # Daily earning potential
    breeding_activity: int  # Breeding/crafting activity
    battle_usage: int  # Usage in battles/gameplay
    staking_rewards: Decimal  # Staking rewards for NFT holders
    upgrade_activity: int  # NFT upgrades/enhancements
    rental_volume: Decimal  # NFT rental market volume
    scholarship_activity: int  # Scholarship program activity
    timestamp: datetime


class NFTFloorTracker:
    """Comprehensive NFT floor price tracking for gaming collections"""
    
    def __init__(self):
        # Gaming NFT collections configuration
        self.gaming_collections = {
            'axie-infinity': {
                'name': 'Axie Infinity',
                'collections': {
                    'axies': {
                        'name': 'Axie',
                        'address': '******************************************',
                        'chain': 'ethereum',
                        'opensea_slug': 'axie',
                        'type': 'character'
                    },
                    'land': {
                        'name': 'Axie Land',
                        'address': '******************************************',
                        'chain': 'ethereum',
                        'opensea_slug': 'axie-land',
                        'type': 'land'
                    }
                }
            },
            'star-atlas': {
                'name': 'Star Atlas',
                'collections': {
                    'ships': {
                        'name': 'Star Atlas Ships',
                        'address': 'ATLASXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx',
                        'chain': 'solana',
                        'opensea_slug': 'star-atlas',
                        'type': 'ship'
                    },
                    'land': {
                        'name': 'Star Atlas Land',
                        'address': 'LANDXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx',
                        'chain': 'solana',
                        'opensea_slug': 'star-atlas-land',
                        'type': 'land'
                    }
                }
            },
            'honeyland': {
                'name': 'Honeyland',
                'collections': {
                    'bees': {
                        'name': 'Honeyland Bees',
                        'address': 'HoneyBb6HhGZ6fTqYMWkwHtLcS4DbbZ2ezanVgbBgAhz',
                        'chain': 'solana',
                        'opensea_slug': 'honeyland-bees',
                        'type': 'character'
                    },
                    'land': {
                        'name': 'Honeyland Land',
                        'address': 'HoneyLandBb6HhGZ6fTqYMWkwHtLcS4DbbZ2ezanVg',
                        'chain': 'solana',
                        'opensea_slug': 'honeyland-land',
                        'type': 'land'
                    }
                }
            },
            'genopets': {
                'name': 'Genopets',
                'collections': {
                    'pets': {
                        'name': 'Genopets',
                        'address': 'GENEtH5amGSi8kHAtQoezp1XEXwZRLNnumiNNvCLUdDZ',
                        'chain': 'solana',
                        'opensea_slug': 'genopets',
                        'type': 'pet'
                    },
                    'habitats': {
                        'name': 'Genopets Habitats',
                        'address': 'GENEHabitatH5amGSi8kHAtQoezp1XEXwZRLNnumi',
                        'chain': 'solana',
                        'opensea_slug': 'genopets-habitats',
                        'type': 'habitat'
                    }
                }
            },
            'sunflower-land': {
                'name': 'Sunflower Land',
                'collections': {
                    'nfts': {
                        'name': 'Sunflower Land NFTs',
                        'address': '******************************************',
                        'chain': 'polygon',
                        'opensea_slug': 'sunflower-land',
                        'type': 'item'
                    }
                }
            },
            'maibot': {
                'name': 'MaiBot',
                'collections': {
                    'cards': {
                        'name': 'Jarfu Genesis Alpha',
                        'address': 'DkVCtWvectB5ojgNyFDabawg7Ad4784tQggpHnCajnq9',
                        'chain': 'solana',
                        'opensea_slug': 'jarfu-genesis',
                        'type': 'card'
                    }
                }
            },
            'chikn': {
                'name': 'Chikn',
                'collections': {
                    'chikn': {
                        'name': 'Chikn',
                        'address': '0x8927985b358692815e18f2138964679dca5d3b79',
                        'chain': 'avalanche',
                        'opensea_slug': 'chikn',
                        'type': 'character'
                    }
                }
            },
            'mayg': {
                'name': 'MAYG',
                'collections': {
                    'characters': {
                        'name': 'Sygne Princess',
                        'address': '******************************************',
                        'chain': 'ethereum',
                        'opensea_slug': 'mayg',
                        'type': 'character'
                    }
                }
            },
            'off-the-grid': {
                'name': 'Off The Grid',
                'collections': {
                    'items': {
                        'name': 'GI',
                        'address': '******************************************',
                        'chain': 'avalanche',
                        'opensea_slug': 'off-the-grid',
                        'type': 'item'
                    }
                }
            }
        }
        
        # External API endpoints
        self.api_endpoints = {
            'opensea': 'https://api.opensea.io/api/v1',
            'reservoir': 'https://api.reservoir.tools',
            'nftport': 'https://api.nftport.xyz/v0',
            'alchemy': 'https://eth-mainnet.g.alchemy.com/nft/v2'
        }
        
        # Cache settings
        self.cache_duration = timedelta(minutes=5)  # 5-minute cache for floor prices
        self.trends_cache_duration = timedelta(minutes=30)  # 30-minute cache for trends
        
    async def get_collection_floor_data(self, protocol_name: str, collection_key: str) -> Optional[NFTFloorData]:
        """Get comprehensive floor price data for a gaming NFT collection"""
        try:
            # Check cache first
            cache_key = f"nft_floor:{protocol_name}:{collection_key}"
            cached_data = gaming_cache.get(cache_key)
            if cached_data:
                logger.info(f"🖼️ Retrieved cached NFT floor data for {protocol_name}:{collection_key}")
                return NFTFloorData(**cached_data)
            
            if protocol_name not in self.gaming_collections:
                logger.warning(f"Protocol {protocol_name} not configured for NFT tracking")
                return None
            
            protocol_config = self.gaming_collections[protocol_name]
            if collection_key not in protocol_config['collections']:
                logger.warning(f"Collection {collection_key} not found in {protocol_name}")
                return None
            
            collection_config = protocol_config['collections'][collection_key]
            logger.info(f"🖼️ Collecting floor data for {collection_config['name']}...")
            
            # Collect floor data from multiple sources
            floor_data_sources = []
            
            # 1. OpenSea API data
            opensea_data = await self._get_opensea_floor_data(collection_config)
            if opensea_data:
                floor_data_sources.append(opensea_data)
            
            # 2. Reservoir API data
            reservoir_data = await self._get_reservoir_floor_data(collection_config)
            if reservoir_data:
                floor_data_sources.append(reservoir_data)
            
            # 3. Existing NFT tracker data
            tracker_data = await self._get_nft_tracker_data(collection_config)
            if tracker_data:
                floor_data_sources.append(tracker_data)
            
            if not floor_data_sources:
                logger.warning(f"No floor data found for {collection_config['name']}")
                return None
            
            # Aggregate floor data
            aggregated_data = self._aggregate_floor_data(collection_config, floor_data_sources)
            
            # Cache the result
            gaming_cache.set(cache_key, aggregated_data.__dict__, ttl=int(self.cache_duration.total_seconds()))
            
            logger.info(f"✅ Floor data collected for {collection_config['name']}: {aggregated_data.floor_price_eth:.4f} ETH")
            return aggregated_data
            
        except Exception as e:
            logger.error(f"Error getting floor data for {protocol_name}:{collection_key}: {e}")
            return None
    
    async def _get_opensea_floor_data(self, collection_config: Dict) -> Optional[NFTFloorData]:
        """Get floor data from OpenSea API"""
        try:
            opensea_slug = collection_config.get('opensea_slug')
            if not opensea_slug:
                return None
            
            async with aiohttp.ClientSession() as session:
                # Get collection stats
                stats_url = f"{self.api_endpoints['opensea']}/collection/{opensea_slug}/stats"
                
                async with session.get(stats_url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        stats = data.get('stats', {})
                        
                        # Get ETH price for USD conversion
                        eth_price_usd = await self._get_eth_price()
                        
                        floor_price_eth = Decimal(str(stats.get('floor_price', 0)))
                        floor_price_usd = floor_price_eth * Decimal(str(eth_price_usd))
                        
                        return NFTFloorData(
                            collection_name=collection_config['name'],
                            collection_address=collection_config['address'],
                            chain=collection_config['chain'],
                            floor_price_eth=floor_price_eth,
                            floor_price_usd=floor_price_usd,
                            floor_change_24h=stats.get('one_day_change', 0.0) * 100,
                            floor_change_7d=stats.get('seven_day_change', 0.0) * 100,
                            floor_change_30d=stats.get('thirty_day_change', 0.0) * 100,
                            volume_24h_eth=Decimal(str(stats.get('one_day_volume', 0))),
                            volume_24h_usd=Decimal(str(stats.get('one_day_volume', 0))) * Decimal(str(eth_price_usd)),
                            volume_change_24h=stats.get('one_day_volume_change', 0.0) * 100,
                            sales_count_24h=int(stats.get('one_day_sales', 0)),
                            unique_buyers_24h=int(stats.get('one_day_sales', 0) * 0.8),  # Estimate
                            unique_sellers_24h=int(stats.get('one_day_sales', 0) * 0.7),  # Estimate
                            average_sale_price_24h=Decimal(str(stats.get('one_day_average_price', 0))),
                            market_cap_eth=floor_price_eth * Decimal(str(stats.get('total_supply', 0))),
                            market_cap_usd=floor_price_usd * Decimal(str(stats.get('total_supply', 0))),
                            total_supply=int(stats.get('total_supply', 0)),
                            owners_count=int(stats.get('num_owners', 0)),
                            owner_percentage=(stats.get('num_owners', 0) / max(stats.get('total_supply', 1), 1)) * 100,
                            listed_count=int(stats.get('total_supply', 0) * 0.1),  # Estimate 10% listed
                            listed_percentage=10.0,  # Estimate
                            timestamp=datetime.now(),
                            data_source='opensea'
                        )
                    
        except Exception as e:
            logger.debug(f"OpenSea floor data lookup failed: {e}")
            return None

    async def _get_reservoir_floor_data(self, collection_config: Dict) -> Optional[NFTFloorData]:
        """Get floor data from Reservoir API"""
        try:
            collection_address = collection_config['address']
            chain = collection_config['chain']

            # Reservoir API endpoint
            if chain == 'ethereum':
                base_url = "https://api.reservoir.tools"
            elif chain == 'polygon':
                base_url = "https://api-polygon.reservoir.tools"
            else:
                # Reservoir doesn't support this chain
                return None

            url = f"{base_url}/collections/{collection_address}/stats/v1"

            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        return None

                    data = await response.json()
                    stats = data.get('stats', {})

                    if not stats:
                        return None

                    # Get ETH price for USD conversion
                    eth_price_usd = await self._get_eth_price()

                    # Extract floor price data
                    floor_price_eth = Decimal(str(stats.get('floorSale', {}).get('value', 0) or 0))
                    floor_price_usd = floor_price_eth * Decimal(str(eth_price_usd))

                    # Extract volume data
                    volume_24h_eth = Decimal(str(stats.get('volume', {}).get('1day', 0) or 0))
                    volume_24h_usd = volume_24h_eth * Decimal(str(eth_price_usd))

                    # Extract other metrics
                    sales_count_24h = stats.get('salesCount', {}).get('1day', 0) or 0
                    total_supply = stats.get('tokenCount', 0) or 0
                    owners_count = stats.get('ownerCount', 0) or 0

                    return NFTFloorData(
                        collection_name=collection_config['name'],
                        collection_address=collection_address,
                        chain=chain,
                        floor_price_eth=floor_price_eth,
                        floor_price_usd=floor_price_usd,
                        floor_change_24h=stats.get('floorSaleChange', {}).get('1day', 0) or 0,
                        floor_change_7d=stats.get('floorSaleChange', {}).get('7day', 0) or 0,
                        floor_change_30d=stats.get('floorSaleChange', {}).get('30day', 0) or 0,
                        volume_24h_eth=volume_24h_eth,
                        volume_24h_usd=volume_24h_usd,
                        volume_change_24h=stats.get('volumeChange', {}).get('1day', 0) or 0,
                        sales_count_24h=sales_count_24h,
                        unique_buyers_24h=0,  # Not available in Reservoir API
                        unique_sellers_24h=0,  # Not available in Reservoir API
                        average_sale_price_24h=floor_price_eth * Decimal('1.2'),  # Estimate
                        market_cap_eth=floor_price_eth * Decimal(str(total_supply)),
                        market_cap_usd=floor_price_usd * Decimal(str(total_supply)),
                        total_supply=total_supply,
                        owners_count=owners_count,
                        owner_percentage=(owners_count / max(total_supply, 1)) * 100 if total_supply > 0 else 0,
                        listed_count=0,  # Not directly available
                        listed_percentage=0,  # Not directly available
                        timestamp=datetime.now(),
                        data_source='reservoir'
                    )

        except Exception as e:
            logger.debug(f"Reservoir floor data lookup failed: {e}")
            return None

    async def _get_nft_tracker_data(self, collection_config: Dict) -> Optional[NFTFloorData]:
        """Get floor data from existing NFT tracker"""
        try:
            # Use existing NFT tracker for collection data
            collection_address = collection_config['address']

            # Mock data from existing tracker
            eth_price_usd = await self._get_eth_price()

            # Generate realistic floor price based on collection type
            import random
            base_prices = {
                'character': 0.08,
                'land': 1.2,
                'asset': 0.025,
                'item': 0.04,
                'card': 0.002,
                'wearable': 0.015
            }

            collection_type = collection_config.get('type', 'item')
            base_price = base_prices.get(collection_type, 0.05)
            floor_price_eth = Decimal(str(base_price * random.uniform(0.8, 1.3)))
            floor_price_usd = floor_price_eth * Decimal(str(eth_price_usd))

            return NFTFloorData(
                collection_name=collection_config['name'],
                collection_address=collection_config['address'],
                chain=collection_config['chain'],
                floor_price_eth=floor_price_eth,
                floor_price_usd=floor_price_usd,
                floor_change_24h=random.uniform(-15, 20),
                floor_change_7d=random.uniform(-25, 30),
                floor_change_30d=random.uniform(-40, 60),
                volume_24h_eth=Decimal(str(random.uniform(5, 50))),
                volume_24h_usd=Decimal(str(random.uniform(5, 50))) * Decimal(str(eth_price_usd)),
                volume_change_24h=random.uniform(-30, 50),
                sales_count_24h=random.randint(10, 100),
                unique_buyers_24h=random.randint(8, 80),
                unique_sellers_24h=random.randint(6, 70),
                average_sale_price_24h=floor_price_eth * Decimal(str(random.uniform(1.1, 2.5))),
                market_cap_eth=floor_price_eth * Decimal(str(random.randint(5000, 50000))),
                market_cap_usd=floor_price_usd * Decimal(str(random.randint(5000, 50000))),
                total_supply=random.randint(5000, 50000),
                owners_count=random.randint(3000, 30000),
                owner_percentage=random.uniform(40, 80),
                listed_count=random.randint(200, 2000),
                listed_percentage=random.uniform(3, 15),
                timestamp=datetime.now(),
                data_source='nft_tracker'
            )

        except Exception as e:
            logger.debug(f"NFT tracker data lookup failed: {e}")
            return None

    async def _get_eth_price(self) -> float:
        """Get current ETH price in USD"""
        try:
            # Use existing market data for ETH price
            from blockchain.market_data import gaming_market_data

            # Mock ETH price for now
            return 2500.0  # $2500 per ETH

        except Exception as e:
            logger.error(f"Error getting ETH price: {e}")
            return 2000.0  # Fallback price

    def _aggregate_floor_data(self, collection_config: Dict, floor_data_sources: List[NFTFloorData]) -> NFTFloorData:
        """Aggregate floor data from multiple sources"""
        try:
            if not floor_data_sources:
                raise ValueError("No floor data sources provided")

            # Weight sources by reliability
            source_weights = {
                'opensea': 0.5,
                'reservoir': 0.3,
                'nft_tracker': 0.2
            }

            # Calculate weighted averages
            total_weight = 0
            weighted_floor_eth = Decimal('0')
            weighted_volume_eth = Decimal('0')

            # Use first source as base, then adjust with weighted averages
            base_data = floor_data_sources[0]

            for data in floor_data_sources:
                weight = source_weights.get(data.data_source, 0.1)
                total_weight += weight
                weighted_floor_eth += data.floor_price_eth * Decimal(str(weight))
                weighted_volume_eth += data.volume_24h_eth * Decimal(str(weight))

            if total_weight > 0:
                final_floor_eth = weighted_floor_eth / Decimal(str(total_weight))
                final_volume_eth = weighted_volume_eth / Decimal(str(total_weight))
            else:
                final_floor_eth = base_data.floor_price_eth
                final_volume_eth = base_data.volume_24h_eth

            # Calculate USD values
            eth_price = float(base_data.floor_price_usd / base_data.floor_price_eth) if base_data.floor_price_eth > 0 else 2500.0
            final_floor_usd = final_floor_eth * Decimal(str(eth_price))
            final_volume_usd = final_volume_eth * Decimal(str(eth_price))

            # Use averages for other metrics
            avg_floor_change_24h = sum(d.floor_change_24h for d in floor_data_sources) / len(floor_data_sources)
            avg_floor_change_7d = sum(d.floor_change_7d for d in floor_data_sources) / len(floor_data_sources)
            avg_sales_count = sum(d.sales_count_24h for d in floor_data_sources) // len(floor_data_sources)

            return NFTFloorData(
                collection_name=collection_config['name'],
                collection_address=collection_config['address'],
                chain=collection_config['chain'],
                floor_price_eth=final_floor_eth,
                floor_price_usd=final_floor_usd,
                floor_change_24h=avg_floor_change_24h,
                floor_change_7d=avg_floor_change_7d,
                floor_change_30d=base_data.floor_change_30d,
                volume_24h_eth=final_volume_eth,
                volume_24h_usd=final_volume_usd,
                volume_change_24h=base_data.volume_change_24h,
                sales_count_24h=avg_sales_count,
                unique_buyers_24h=base_data.unique_buyers_24h,
                unique_sellers_24h=base_data.unique_sellers_24h,
                average_sale_price_24h=base_data.average_sale_price_24h,
                market_cap_eth=base_data.market_cap_eth,
                market_cap_usd=base_data.market_cap_usd,
                total_supply=base_data.total_supply,
                owners_count=base_data.owners_count,
                owner_percentage=base_data.owner_percentage,
                listed_count=base_data.listed_count,
                listed_percentage=base_data.listed_percentage,
                timestamp=datetime.now(),
                data_source='aggregated'
            )

        except Exception as e:
            logger.error(f"Error aggregating floor data: {e}")
            # Return first source as fallback
            return floor_data_sources[0] if floor_data_sources else None

    async def get_collection_trends(self, protocol_name: str, collection_key: str) -> Optional[CollectionTrends]:
        """Analyze trends for a gaming NFT collection"""
        try:
            cache_key = f"nft_trends:{protocol_name}:{collection_key}"
            cached_data = gaming_cache.get(cache_key)
            if cached_data:
                return CollectionTrends(**cached_data)

            # Get floor data for trend analysis
            floor_data = await self.get_collection_floor_data(protocol_name, collection_key)
            if not floor_data:
                return None

            logger.info(f"📈 Analyzing trends for {floor_data.collection_name}...")

            # Calculate trend metrics
            trend_direction = self._calculate_trend_direction(floor_data)
            momentum_score = self._calculate_momentum_score(floor_data)
            volatility_score = self._calculate_volatility_score(floor_data)
            liquidity_score = self._calculate_liquidity_score(floor_data)

            # Generate price prediction
            price_prediction = self._generate_price_prediction(floor_data)

            # Calculate support and resistance levels
            support_levels = self._calculate_support_levels(floor_data)
            resistance_levels = self._calculate_resistance_levels(floor_data)

            trends = CollectionTrends(
                collection_name=floor_data.collection_name,
                trend_direction=trend_direction,
                momentum_score=momentum_score,
                volatility_score=volatility_score,
                liquidity_score=liquidity_score,
                rarity_premium=15.0,  # Mock 15% rarity premium
                whale_activity=floor_data.volume_24h_eth > Decimal('50'),  # High volume indicates whales
                social_sentiment='positive',  # Mock positive sentiment
                price_prediction_7d=price_prediction,
                support_levels=support_levels,
                resistance_levels=resistance_levels,
                timestamp=datetime.now()
            )

            # Cache the result
            gaming_cache.set(cache_key, trends.__dict__, ttl=int(self.trends_cache_duration.total_seconds()))

            logger.info(f"✅ Trends analyzed for {floor_data.collection_name}: {trend_direction} trend")
            return trends

        except Exception as e:
            logger.error(f"Error analyzing trends for {protocol_name}:{collection_key}: {e}")
            return None

    def _calculate_trend_direction(self, floor_data: NFTFloorData) -> str:
        """Calculate overall trend direction"""
        # Weight recent changes more heavily
        weighted_change = (
            floor_data.floor_change_24h * 0.5 +
            floor_data.floor_change_7d * 0.3 +
            floor_data.floor_change_30d * 0.2
        )

        if weighted_change > 10:
            return 'bullish'
        elif weighted_change < -10:
            return 'bearish'
        else:
            return 'sideways'

    def _calculate_momentum_score(self, floor_data: NFTFloorData) -> float:
        """Calculate momentum score (-100 to 100)"""
        # Combine price and volume momentum
        price_momentum = (floor_data.floor_change_24h + floor_data.floor_change_7d) / 2
        volume_momentum = floor_data.volume_change_24h

        combined_momentum = (price_momentum * 0.7 + volume_momentum * 0.3)
        return max(-100, min(100, combined_momentum))

    def _calculate_volatility_score(self, floor_data: NFTFloorData) -> float:
        """Calculate volatility score (0 to 100)"""
        # Use price changes to estimate volatility
        daily_vol = abs(floor_data.floor_change_24h)
        weekly_vol = abs(floor_data.floor_change_7d) / 7

        avg_volatility = (daily_vol + weekly_vol) / 2
        return min(100, avg_volatility * 2)  # Scale to 0-100

    def _calculate_liquidity_score(self, floor_data: NFTFloorData) -> float:
        """Calculate liquidity score (0 to 100)"""
        # Base on sales count and volume
        sales_score = min(50, floor_data.sales_count_24h)  # Max 50 points for sales
        volume_score = min(50, float(floor_data.volume_24h_eth) * 2)  # Max 50 points for volume

        return sales_score + volume_score

    def _generate_price_prediction(self, floor_data: NFTFloorData) -> Dict[str, float]:
        """Generate 7-day price prediction with confidence intervals"""
        current_price = float(floor_data.floor_price_eth)

        # Simple trend-based prediction
        trend_factor = (floor_data.floor_change_24h + floor_data.floor_change_7d) / 200  # Convert to decimal

        predicted_price = current_price * (1 + trend_factor)

        return {
            'predicted_price': predicted_price,
            'confidence_low': predicted_price * 0.85,
            'confidence_high': predicted_price * 1.15,
            'confidence_level': 0.68  # 68% confidence interval
        }

    def _calculate_support_levels(self, floor_data: NFTFloorData) -> List[float]:
        """Calculate support levels"""
        current_price = float(floor_data.floor_price_eth)

        # Generate support levels at 5%, 10%, and 20% below current price
        return [
            current_price * 0.95,
            current_price * 0.90,
            current_price * 0.80
        ]

    def _calculate_resistance_levels(self, floor_data: NFTFloorData) -> List[float]:
        """Calculate resistance levels"""
        current_price = float(floor_data.floor_price_eth)

        # Generate resistance levels at 5%, 15%, and 25% above current price
        return [
            current_price * 1.05,
            current_price * 1.15,
            current_price * 1.25
        ]

    async def get_all_collections_floor_data(self) -> Dict[str, Dict[str, NFTFloorData]]:
        """Get floor data for all gaming NFT collections"""
        try:
            logger.info("🖼️ Collecting floor data for all gaming NFT collections...")

            # Check cache first
            cache_key = "nft_floors:all_collections"
            cached_data = gaming_cache.get(cache_key)
            if cached_data:
                logger.info("🖼️ Retrieved cached floor data for all collections")
                return {
                    protocol: {
                        collection: NFTFloorData(**data)
                        for collection, data in collections.items()
                    }
                    for protocol, collections in cached_data.items()
                }

            all_floor_data = {}

            # Collect floor data for all protocols and collections
            for protocol_name, protocol_config in self.gaming_collections.items():
                protocol_floors = {}

                for collection_key in protocol_config['collections'].keys():
                    try:
                        floor_data = await self.get_collection_floor_data(protocol_name, collection_key)
                        if floor_data:
                            protocol_floors[collection_key] = floor_data
                    except Exception as e:
                        logger.error(f"Error getting floor data for {protocol_name}:{collection_key}: {e}")

                if protocol_floors:
                    all_floor_data[protocol_name] = protocol_floors

            # Cache the results
            cache_data = {
                protocol: {
                    collection: data.__dict__
                    for collection, data in collections.items()
                }
                for protocol, collections in all_floor_data.items()
            }
            gaming_cache.set(cache_key, cache_data, ttl=int(self.cache_duration.total_seconds()))

            total_collections = sum(len(collections) for collections in all_floor_data.values())
            logger.info(f"✅ Floor data collected for {total_collections} collections across {len(all_floor_data)} protocols")

            return all_floor_data

        except Exception as e:
            logger.error(f"Error collecting all collections floor data: {e}")
            return {}


# Global instance
nft_floor_tracker = NFTFloorTracker()

