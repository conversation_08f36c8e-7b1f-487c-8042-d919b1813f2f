"""
Real-time Notification Service
Handles notifications for new gaming project detections and other events
"""
import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


class NotificationType(Enum):
    """Types of notifications"""
    NEW_GAMING_PROJECT = "new_gaming_project"
    PROJECT_NEEDS_REVIEW = "project_needs_review"
    HIGH_CONFIDENCE_CONTRACT = "high_confidence_contract"
    SYSTEM_ALERT = "system_alert"
    DATA_UPDATE = "data_update"


@dataclass
class Notification:
    """Notification data structure"""
    id: str
    type: NotificationType
    title: str
    message: str
    data: Dict[str, Any]
    timestamp: datetime
    priority: str = "normal"  # low, normal, high, urgent
    read: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert notification to dictionary"""
        return {
            "id": self.id,
            "type": self.type.value,
            "title": self.title,
            "message": self.message,
            "data": self.data,
            "timestamp": self.timestamp.isoformat(),
            "priority": self.priority,
            "read": self.read
        }


class NotificationService:
    """Service for managing real-time notifications"""
    
    def __init__(self):
        self.notifications: List[Notification] = []
        self.subscribers: List[callable] = []
        self.max_notifications = 100  # Keep last 100 notifications
        
    async def send_notification(
        self,
        notification_type: NotificationType,
        title: str,
        message: str,
        data: Dict[str, Any] = None,
        priority: str = "normal"
    ) -> str:
        """Send a new notification"""
        
        notification_id = f"{notification_type.value}_{datetime.now().timestamp()}"
        
        notification = Notification(
            id=notification_id,
            type=notification_type,
            title=title,
            message=message,
            data=data or {},
            timestamp=datetime.now(),
            priority=priority
        )
        
        # Add to notifications list
        self.notifications.insert(0, notification)  # Add to beginning
        
        # Keep only the most recent notifications
        if len(self.notifications) > self.max_notifications:
            self.notifications = self.notifications[:self.max_notifications]
        
        # Notify all subscribers
        await self._notify_subscribers(notification)
        
        logger.info(f"📢 Notification sent: {title}")
        return notification_id
        
    async def notify_new_gaming_project(
        self,
        project_name: str,
        contract_address: str,
        blockchain: str,
        confidence: str,
        project_id: int = None
    ):
        """Send notification for new gaming project detection"""
        
        title = f"New Gaming Project Detected: {project_name}"
        message = f"Discovered {project_name} on {blockchain} with {confidence.lower()} confidence"
        
        data = {
            "project_name": project_name,
            "contract_address": contract_address,
            "blockchain": blockchain,
            "confidence": confidence,
            "project_id": project_id,
            "action_required": confidence == "MEDIUM"
        }
        
        priority = "high" if confidence == "HIGH" else "normal"
        
        await self.send_notification(
            NotificationType.NEW_GAMING_PROJECT,
            title,
            message,
            data,
            priority
        )
        
    async def notify_project_needs_review(
        self,
        project_name: str,
        project_id: int,
        reason: str
    ):
        """Send notification when project needs admin review"""
        
        title = f"Project Needs Review: {project_name}"
        message = f"Admin attention required: {reason}"
        
        data = {
            "project_name": project_name,
            "project_id": project_id,
            "reason": reason,
            "action_required": True
        }
        
        await self.send_notification(
            NotificationType.PROJECT_NEEDS_REVIEW,
            title,
            message,
            data,
            "high"
        )
        
    async def notify_high_confidence_contract(
        self,
        contract_address: str,
        blockchain: str,
        patterns: List[str]
    ):
        """Send notification for high-confidence contract detection"""
        
        title = f"High-Confidence Gaming Contract Detected"
        message = f"Contract {contract_address[:8]}... on {blockchain} shows strong gaming patterns"
        
        data = {
            "contract_address": contract_address,
            "blockchain": blockchain,
            "detected_patterns": patterns,
            "confidence": "HIGH"
        }
        
        await self.send_notification(
            NotificationType.HIGH_CONFIDENCE_CONTRACT,
            title,
            message,
            data,
            "high"
        )
        
    async def notify_system_alert(
        self,
        alert_type: str,
        message: str,
        details: Dict[str, Any] = None
    ):
        """Send system alert notification"""
        
        title = f"System Alert: {alert_type}"
        
        data = {
            "alert_type": alert_type,
            "details": details or {}
        }
        
        await self.send_notification(
            NotificationType.SYSTEM_ALERT,
            title,
            message,
            data,
            "urgent"
        )
        
    async def notify_data_update(
        self,
        update_type: str,
        count: int,
        source: str
    ):
        """Send notification for data updates"""
        
        title = f"Data Updated: {update_type}"
        message = f"Updated {count} {update_type.lower()} from {source}"
        
        data = {
            "update_type": update_type,
            "count": count,
            "source": source
        }
        
        await self.send_notification(
            NotificationType.DATA_UPDATE,
            title,
            message,
            data,
            "low"
        )
        
    def subscribe(self, callback: callable):
        """Subscribe to notifications"""
        self.subscribers.append(callback)
        logger.info(f"New notification subscriber added. Total: {len(self.subscribers)}")
        
    def unsubscribe(self, callback: callable):
        """Unsubscribe from notifications"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
            logger.info(f"Notification subscriber removed. Total: {len(self.subscribers)}")
            
    async def _notify_subscribers(self, notification: Notification):
        """Notify all subscribers of new notification"""
        if not self.subscribers:
            return
            
        notification_dict = notification.to_dict()
        
        # Notify all subscribers concurrently
        tasks = []
        for callback in self.subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    tasks.append(callback(notification_dict))
                else:
                    # Run sync callback in thread pool
                    tasks.append(asyncio.get_event_loop().run_in_executor(None, callback, notification_dict))
            except Exception as e:
                logger.error(f"Error preparing notification callback: {e}")
                
        if tasks:
            try:
                await asyncio.gather(*tasks, return_exceptions=True)
            except Exception as e:
                logger.error(f"Error notifying subscribers: {e}")
                
    def get_notifications(
        self,
        limit: int = 50,
        unread_only: bool = False,
        notification_type: NotificationType = None
    ) -> List[Dict[str, Any]]:
        """Get notifications with optional filtering"""
        
        notifications = self.notifications.copy()
        
        # Filter by read status
        if unread_only:
            notifications = [n for n in notifications if not n.read]
            
        # Filter by type
        if notification_type:
            notifications = [n for n in notifications if n.type == notification_type]
            
        # Apply limit
        notifications = notifications[:limit]
        
        return [n.to_dict() for n in notifications]
        
    def mark_as_read(self, notification_id: str) -> bool:
        """Mark notification as read"""
        for notification in self.notifications:
            if notification.id == notification_id:
                notification.read = True
                return True
        return False
        
    def mark_all_as_read(self) -> int:
        """Mark all notifications as read"""
        count = 0
        for notification in self.notifications:
            if not notification.read:
                notification.read = True
                count += 1
        return count
        
    def get_unread_count(self) -> int:
        """Get count of unread notifications"""
        return sum(1 for n in self.notifications if not n.read)
        
    def clear_old_notifications(self, days: int = 7):
        """Clear notifications older than specified days"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        original_count = len(self.notifications)
        self.notifications = [
            n for n in self.notifications 
            if n.timestamp > cutoff_date
        ]
        
        cleared_count = original_count - len(self.notifications)
        if cleared_count > 0:
            logger.info(f"Cleared {cleared_count} old notifications")
            
        return cleared_count


# Global notification service instance
notification_service = NotificationService()
