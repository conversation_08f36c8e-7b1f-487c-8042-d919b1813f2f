"""
P2E (Play-to-Earn) Token Economics Tracker
Comprehensive analysis of token flow, earning mechanics, and economic sustainability
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from decimal import Decimal
import aiohttp
from sqlalchemy.orm import Session

from models.base import SessionLocal
from models.gaming import GamingProject, BlockchainData
from blockchain.data_clients.manager import BlockchainDataManager
from blockchain.market_data import gaming_market_data
from services.redis_cache import gaming_cache
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class TokenEconomics:
    """Token economics data structure"""
    protocol_name: str
    token_symbol: str
    chain: str
    total_supply: Decimal
    circulating_supply: Decimal
    market_cap_usd: Decimal
    current_price_usd: Decimal
    price_change_24h: float
    price_change_7d: float
    volume_24h_usd: Decimal
    inflation_rate_annual: float
    burn_rate_daily: Decimal
    staking_ratio: float
    reward_rate_apy: float
    timestamp: datetime


@dataclass
class EarningMechanics:
    """P2E earning mechanics analysis"""
    protocol_name: str
    avg_daily_earnings_usd: Decimal
    avg_hourly_earnings_usd: Decimal
    top_earner_daily_usd: Decimal
    median_earnings_usd: Decimal
    earnings_distribution: Dict[str, int]  # earnings_range -> user_count
    active_earning_users: int
    total_rewards_distributed_24h: Decimal
    reward_token_distribution: Dict[str, Decimal]
    earning_activities: Dict[str, Decimal]  # activity_type -> avg_reward
    sustainability_score: float
    timestamp: datetime


@dataclass
class TokenFlow:
    """Token flow analysis"""
    protocol_name: str
    token_symbol: str
    inflow_24h: Decimal  # Tokens entering circulation
    outflow_24h: Decimal  # Tokens leaving circulation
    net_flow_24h: Decimal  # Net change in circulation
    staking_inflow: Decimal
    staking_outflow: Decimal
    marketplace_volume: Decimal
    reward_distribution: Decimal
    burn_amount: Decimal
    mint_amount: Decimal
    treasury_activity: Decimal
    timestamp: datetime


@dataclass
class EconomicSustainability:
    """Economic sustainability metrics"""
    protocol_name: str
    revenue_vs_rewards_ratio: float
    token_velocity: float
    holder_concentration: float  # Gini coefficient
    treasury_runway_days: int
    inflation_sustainability_score: float
    user_acquisition_cost_usd: Decimal
    lifetime_value_per_user_usd: Decimal
    break_even_analysis: Dict[str, Any]
    risk_factors: List[str]
    sustainability_rating: str  # A, B, C, D, F
    timestamp: datetime


class P2EEconomicsTracker:
    """Comprehensive P2E token economics tracking"""
    
    def __init__(self):
        self.blockchain_manager = BlockchainDataManager()
        
        # P2E Gaming protocols with token economics
        self.p2e_protocols = {
            'axie-infinity': {
                'name': 'Axie Infinity',
                'tokens': {
                    'AXS': {
                        'type': 'governance',
                        'contract': '******************************************',
                        'chain': 'ethereum',
                        'coingecko_id': 'axie-infinity'
                    },
                    'SLP': {
                        'type': 'utility',
                        'contract': '******************************************',
                        'chain': 'ethereum',
                        'coingecko_id': 'smooth-love-potion'
                    }
                },
                'earning_activities': {
                    'daily_quest': 50,  # SLP reward
                    'adventure_mode': 100,
                    'arena_battle': 150,
                    'breeding': -300  # SLP cost
                },
                'economic_model': 'dual_token'
            },
            'star-atlas': {
                'name': 'Star Atlas',
                'tokens': {
                    'ATLAS': {
                        'type': 'utility',
                        'contract': 'ATLASXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx',
                        'chain': 'solana',
                        'coingecko_id': 'star-atlas'
                    },
                    'POLIS': {
                        'type': 'governance',
                        'contract': 'POLISXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx',
                        'chain': 'solana',
                        'coingecko_id': 'star-atlas-dao'
                    }
                },
                'earning_activities': {
                    'mining': 100,  # ATLAS per day
                    'exploration': 150,
                    'trading': 50,
                    'staking_rewards': 200  # POLIS per day
                },
                'economic_model': 'dual_token'
            },
            'honeyland': {
                'name': 'Honeyland',
                'tokens': {
                    'HXD': {
                        'type': 'utility',
                        'contract': 'HoneyBb6HhGZ6fTqYMWkwHtLcS4DbbZ2ezanVgbBgAhz',
                        'chain': 'solana',
                        'coingecko_id': 'honeyland'
                    }
                },
                'earning_activities': {
                    'harvesting': 25,  # HXD per day
                    'breeding': 100,
                    'missions': 75,
                    'staking_rewards': 50
                },
                'economic_model': 'single_token'
            },
            'genopets': {
                'name': 'Genopets',
                'tokens': {
                    'GENE': {
                        'type': 'governance',
                        'contract': 'GENEtH5amGSi8kHAtQoezp1XEXwZRLNnumiNNvCLUdDZ',
                        'chain': 'solana',
                        'coingecko_id': 'genopets'
                    },
                    'KI': {
                        'type': 'utility',
                        'contract': 'KIXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx',
                        'chain': 'solana',
                        'coingecko_id': 'genopets-ki'
                    }
                },
                'earning_activities': {
                    'walking': 5,  # KI per day
                    'battling': 50,
                    'evolving': 200,
                    'staking_rewards': 25  # GENE per day
                },
                'economic_model': 'dual_token'
            },
            'sunflower-land': {
                'name': 'Sunflower Land',
                'tokens': {
                    'SFL': {
                        'type': 'utility',
                        'contract': '0x2B4A66557A79263275826AD31a4cDDc2789334bD',
                        'chain': 'polygon',
                        'coingecko_id': 'sunflower-land'
                    }
                },
                'earning_activities': {
                    'farming': 15,  # SFL per day
                    'crafting': 30,
                    'trading': 20,
                    'staking_rewards': 10
                },
                'economic_model': 'single_token'
            }
        }
        
        # Cache settings
        self.cache_duration = timedelta(minutes=15)  # 15-minute cache
        self.historical_cache_duration = timedelta(hours=2)  # 2-hour cache for historical data
    
    async def get_protocol_token_economics(self, protocol_name: str) -> Dict[str, TokenEconomics]:
        """Get comprehensive token economics for a gaming protocol"""
        try:
            # Check cache first
            cache_key = f"token_economics:protocol:{protocol_name}"
            cached_data = gaming_cache.get(cache_key)
            if cached_data:
                logger.info(f"💰 Retrieved cached token economics for {protocol_name}")
                return {token: TokenEconomics(**data) for token, data in cached_data.items()}
            
            if protocol_name not in self.p2e_protocols:
                logger.warning(f"Protocol {protocol_name} not configured for token economics tracking")
                return {}
            
            protocol_config = self.p2e_protocols[protocol_name]
            logger.info(f"💰 Collecting token economics for {protocol_config['name']}...")
            
            token_economics = {}
            
            # Analyze each token in the protocol
            for token_symbol, token_config in protocol_config['tokens'].items():
                try:
                    economics = await self._analyze_token_economics(
                        protocol_name, token_symbol, token_config
                    )
                    if economics:
                        token_economics[token_symbol] = economics
                        
                except Exception as e:
                    logger.error(f"Error analyzing {token_symbol} economics: {e}")
            
            # Cache the results
            cache_data = {token: econ.__dict__ for token, econ in token_economics.items()}
            gaming_cache.set(cache_key, cache_data, ttl=int(self.cache_duration.total_seconds()))
            
            logger.info(f"✅ Token economics collected for {protocol_name}: {len(token_economics)} tokens")
            return token_economics
            
        except Exception as e:
            logger.error(f"Error getting token economics for {protocol_name}: {e}")
            return {}
    
    async def _analyze_token_economics(self, protocol_name: str, token_symbol: str, token_config: Dict) -> Optional[TokenEconomics]:
        """Analyze token economics for a specific token"""
        try:
            # Get token market data
            coingecko_id = token_config.get('coingecko_id')
            if not coingecko_id:
                return None
            
            # Get market data from existing gaming market data manager
            token_data = await gaming_market_data.get_token_price(
                token_config['chain'], 
                token_config['contract'], 
                token_symbol
            )
            
            if not token_data:
                logger.warning(f"No market data found for {token_symbol}")
                return None
            
            # Calculate additional economics metrics
            inflation_rate = await self._calculate_inflation_rate(protocol_name, token_symbol, token_config)
            burn_rate = await self._calculate_burn_rate(protocol_name, token_symbol, token_config)
            staking_ratio = await self._calculate_staking_ratio(protocol_name, token_symbol, token_config)
            reward_apy = await self._calculate_reward_apy(protocol_name, token_symbol, token_config)
            
            return TokenEconomics(
                protocol_name=protocol_name,
                token_symbol=token_symbol,
                chain=token_config['chain'],
                total_supply=Decimal(str(getattr(token_data, 'total_supply', 0) or 0)),
                circulating_supply=Decimal(str(getattr(token_data, 'circulating_supply', 0) or 0)),
                market_cap_usd=Decimal(str(getattr(token_data, 'market_cap', 0) or 0)),
                current_price_usd=Decimal(str(token_data.price_usd)),
                price_change_24h=getattr(token_data, 'price_change_24h', 0.0),
                price_change_7d=getattr(token_data, 'price_change_7d', 0.0),
                volume_24h_usd=Decimal(str(getattr(token_data, 'volume_24h', 0) or 0)),
                inflation_rate_annual=inflation_rate,
                burn_rate_daily=burn_rate,
                staking_ratio=staking_ratio,
                reward_rate_apy=reward_apy,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error analyzing token economics for {token_symbol}: {e}")
            return None
    
    async def _calculate_inflation_rate(self, protocol_name: str, token_symbol: str, token_config: Dict) -> float:
        """Calculate annual inflation rate for a token"""
        try:
            # Mock inflation rates based on token type and protocol
            inflation_rates = {
                'axie-infinity': {'AXS': 2.5, 'SLP': -15.0},  # SLP is deflationary
                'star-atlas': {'ATLAS': 4.0, 'POLIS': 2.0},
                'honeyland': {'HXD': 3.5},
                'genopets': {'GENE': 3.0, 'KI': 5.0},
                'sunflower-land': {'SFL': 6.0}
            }
            
            protocol_rates = inflation_rates.get(protocol_name, {})
            return protocol_rates.get(token_symbol, 3.0)  # Default 3% inflation
            
        except Exception as e:
            logger.error(f"Error calculating inflation rate: {e}")
            return 0.0
    
    async def _calculate_burn_rate(self, protocol_name: str, token_symbol: str, token_config: Dict) -> Decimal:
        """Calculate daily token burn rate"""
        try:
            # Mock burn rates based on protocol activity
            daily_burn_estimates = {
                'axie-infinity': {'AXS': 1000, 'SLP': 50000},
                'star-atlas': {'ATLAS': 8000, 'POLIS': 2000},
                'honeyland': {'HXD': 3000},
                'genopets': {'GENE': 1500, 'KI': 5000},
                'sunflower-land': {'SFL': 4000}
            }
            
            protocol_burns = daily_burn_estimates.get(protocol_name, {})
            return Decimal(str(protocol_burns.get(token_symbol, 0)))
            
        except Exception as e:
            logger.error(f"Error calculating burn rate: {e}")
            return Decimal('0')
    
    async def _calculate_staking_ratio(self, protocol_name: str, token_symbol: str, token_config: Dict) -> float:
        """Calculate percentage of tokens staked"""
        try:
            # Mock staking ratios based on token utility
            staking_ratios = {
                'axie-infinity': {'AXS': 65.0, 'SLP': 0.0},  # Only AXS is stakeable
                'star-atlas': {'ATLAS': 45.0, 'POLIS': 70.0},
                'honeyland': {'HXD': 35.0},
                'genopets': {'GENE': 60.0, 'KI': 0.0},
                'sunflower-land': {'SFL': 25.0}
            }
            
            protocol_ratios = staking_ratios.get(protocol_name, {})
            return protocol_ratios.get(token_symbol, 30.0)  # Default 30% staked
            
        except Exception as e:
            logger.error(f"Error calculating staking ratio: {e}")
            return 0.0
    
    async def _calculate_reward_apy(self, protocol_name: str, token_symbol: str, token_config: Dict) -> float:
        """Calculate staking reward APY"""
        try:
            # Mock APY rates based on protocol and token
            reward_apys = {
                'axie-infinity': {'AXS': 85.0, 'SLP': 0.0},
                'star-atlas': {'ATLAS': 35.0, 'POLIS': 65.0},
                'honeyland': {'HXD': 40.0},
                'genopets': {'GENE': 55.0, 'KI': 0.0},
                'sunflower-land': {'SFL': 30.0}
            }
            
            protocol_apys = reward_apys.get(protocol_name, {})
            return protocol_apys.get(token_symbol, 20.0)  # Default 20% APY
            
        except Exception as e:
            logger.error(f"Error calculating reward APY: {e}")
            return 0.0

    async def get_earning_mechanics(self, protocol_name: str) -> Optional[EarningMechanics]:
        """Analyze P2E earning mechanics for a protocol"""
        try:
            # Check cache first
            cache_key = f"earning_mechanics:{protocol_name}"
            cached_data = gaming_cache.get(cache_key)
            if cached_data:
                return EarningMechanics(**cached_data)

            if protocol_name not in self.p2e_protocols:
                return None

            protocol_config = self.p2e_protocols[protocol_name]
            logger.info(f"🎮 Analyzing earning mechanics for {protocol_config['name']}...")

            # Calculate earning metrics
            earning_activities = protocol_config.get('earning_activities', {})

            # Estimate daily earnings based on activity rewards
            avg_daily_activities = {
                'axie-infinity': 8,  # battles + quests per day
                'star-atlas': 6,    # mining + exploration + trading
                'honeyland': 4,     # harvesting + missions + breeding
                'genopets': 10,     # walking + battling + evolving
                'sunflower-land': 5 # farming + crafting + trading
            }

            daily_activity_count = avg_daily_activities.get(protocol_name, 5)

            # Calculate average daily earnings
            total_daily_reward = sum(
                reward for activity, reward in earning_activities.items()
                if reward > 0  # Only positive rewards
            )
            avg_daily_earnings = total_daily_reward * (daily_activity_count / len(earning_activities))

            # Get token price for USD conversion
            primary_token = list(protocol_config['tokens'].keys())[0]
            token_economics = await self.get_protocol_token_economics(protocol_name)
            token_price = 1.0  # Default $1

            if primary_token in token_economics:
                token_price = float(token_economics[primary_token].current_price_usd)

            avg_daily_earnings_usd = Decimal(str(avg_daily_earnings * token_price))
            avg_hourly_earnings_usd = avg_daily_earnings_usd / 24

            # Estimate user distribution and top earners
            active_earning_users = self._estimate_active_earning_users(protocol_name)
            top_earner_daily_usd = avg_daily_earnings_usd * Decimal('5')  # Top earners make 5x average
            median_earnings_usd = avg_daily_earnings_usd * Decimal('0.7')  # Median is 70% of average

            # Calculate earnings distribution
            earnings_distribution = {
                '$0-10': int(active_earning_users * 0.4),
                '$10-50': int(active_earning_users * 0.3),
                '$50-100': int(active_earning_users * 0.2),
                '$100-500': int(active_earning_users * 0.08),
                '$500+': int(active_earning_users * 0.02)
            }

            # Calculate total rewards distributed
            total_rewards_24h = avg_daily_earnings_usd * active_earning_users

            # Token distribution breakdown
            reward_token_distribution = {}
            for token_symbol in protocol_config['tokens'].keys():
                if token_symbol == primary_token:
                    reward_token_distribution[token_symbol] = total_rewards_24h * Decimal('0.8')
                else:
                    reward_token_distribution[token_symbol] = total_rewards_24h * Decimal('0.2')

            # Activity-specific rewards in USD
            earning_activities_usd = {}
            for activity, reward in earning_activities.items():
                earning_activities_usd[activity] = Decimal(str(reward * token_price))

            # Calculate sustainability score
            sustainability_score = self._calculate_sustainability_score(
                protocol_name, avg_daily_earnings_usd, active_earning_users
            )

            earning_mechanics = EarningMechanics(
                protocol_name=protocol_name,
                avg_daily_earnings_usd=avg_daily_earnings_usd,
                avg_hourly_earnings_usd=avg_hourly_earnings_usd,
                top_earner_daily_usd=top_earner_daily_usd,
                median_earnings_usd=median_earnings_usd,
                earnings_distribution=earnings_distribution,
                active_earning_users=active_earning_users,
                total_rewards_distributed_24h=total_rewards_24h,
                reward_token_distribution=reward_token_distribution,
                earning_activities=earning_activities_usd,
                sustainability_score=sustainability_score,
                timestamp=datetime.now()
            )

            # Cache the result
            gaming_cache.set(cache_key, earning_mechanics.__dict__, ttl=int(self.cache_duration.total_seconds()))

            logger.info(f"✅ Earning mechanics analyzed for {protocol_name}: ${avg_daily_earnings_usd:.2f} avg daily")
            return earning_mechanics

        except Exception as e:
            logger.error(f"Error analyzing earning mechanics for {protocol_name}: {e}")
            return None

    def _estimate_active_earning_users(self, protocol_name: str) -> int:
        """Estimate number of active earning users"""
        # Base estimates from user activity data
        active_user_estimates = {
            'axie-infinity': 45000,
            'star-atlas': 15000,
            'honeyland': 8000,
            'genopets': 12000,
            'sunflower-land': 25000,
            'mayg': 5000,
            'off-the-grid': 18000,
            'chikn': 3000,
            'starmech': 2000
        }
        return active_user_estimates.get(protocol_name, 10000)

    def _calculate_sustainability_score(self, protocol_name: str, avg_daily_earnings: Decimal, active_users: int) -> float:
        """Calculate economic sustainability score (0-100)"""
        try:
            # Factors affecting sustainability
            total_daily_rewards = avg_daily_earnings * active_users

            # Revenue estimates (very rough)
            revenue_estimates = {
                'axie-infinity': 500000,  # $500k daily revenue
                'star-atlas': 200000,
                'honeyland': 75000,
                'genopets': 100000,
                'sunflower-land': 150000,
                'mayg': 50000,
                'off-the-grid': 180000,
                'chikn': 30000,
                'starmech': 25000
            }

            estimated_revenue = revenue_estimates.get(protocol_name, 100000)
            revenue_to_rewards_ratio = estimated_revenue / max(float(total_daily_rewards), 1)

            # Sustainability factors
            if revenue_to_rewards_ratio >= 2.0:
                base_score = 90
            elif revenue_to_rewards_ratio >= 1.5:
                base_score = 80
            elif revenue_to_rewards_ratio >= 1.0:
                base_score = 70
            elif revenue_to_rewards_ratio >= 0.8:
                base_score = 60
            else:
                base_score = 40

            # Adjust for user growth and token economics
            if active_users > 20000:
                base_score += 5
            if avg_daily_earnings < 50:  # Reasonable earning levels
                base_score += 5

            return min(100.0, max(0.0, base_score))

        except Exception as e:
            logger.error(f"Error calculating sustainability score: {e}")
            return 50.0  # Default neutral score

    async def get_token_flow_analysis(self, protocol_name: str, token_symbol: str) -> Optional[TokenFlow]:
        """Analyze token flow for a specific token"""
        try:
            cache_key = f"token_flow:{protocol_name}:{token_symbol}"
            cached_data = gaming_cache.get(cache_key)
            if cached_data:
                return TokenFlow(**cached_data)

            if protocol_name not in self.p2e_protocols:
                return None

            protocol_config = self.p2e_protocols[protocol_name]
            if token_symbol not in protocol_config['tokens']:
                return None

            logger.info(f"💱 Analyzing token flow for {token_symbol} in {protocol_name}...")

            # Mock token flow data based on protocol activity
            # In production, this would analyze actual blockchain transactions

            # Get earning mechanics for context
            earning_mechanics = await self.get_earning_mechanics(protocol_name)

            # Estimate token flows
            if earning_mechanics:
                daily_rewards = earning_mechanics.total_rewards_distributed_24h
                # Convert USD back to tokens
                token_economics = await self.get_protocol_token_economics(protocol_name)
                token_price = 1.0
                if token_symbol in token_economics:
                    token_price = float(token_economics[token_symbol].current_price_usd)

                daily_reward_tokens = daily_rewards / Decimal(str(max(token_price, 0.001)))
            else:
                daily_reward_tokens = Decimal('100000')  # Default estimate

            # Estimate other flows
            staking_inflow = daily_reward_tokens * Decimal('0.3')  # 30% of rewards get staked
            staking_outflow = daily_reward_tokens * Decimal('0.2')  # 20% unstaked
            marketplace_volume = daily_reward_tokens * Decimal('0.4')  # 40% traded
            burn_amount = daily_reward_tokens * Decimal('0.1')  # 10% burned
            mint_amount = daily_reward_tokens * Decimal('1.05')  # 5% inflation
            treasury_activity = daily_reward_tokens * Decimal('0.15')  # 15% treasury

            inflow_24h = mint_amount + staking_outflow
            outflow_24h = burn_amount + staking_inflow
            net_flow_24h = inflow_24h - outflow_24h

            token_flow = TokenFlow(
                protocol_name=protocol_name,
                token_symbol=token_symbol,
                inflow_24h=inflow_24h,
                outflow_24h=outflow_24h,
                net_flow_24h=net_flow_24h,
                staking_inflow=staking_inflow,
                staking_outflow=staking_outflow,
                marketplace_volume=marketplace_volume,
                reward_distribution=daily_reward_tokens,
                burn_amount=burn_amount,
                mint_amount=mint_amount,
                treasury_activity=treasury_activity,
                timestamp=datetime.now()
            )

            # Cache the result
            gaming_cache.set(cache_key, token_flow.__dict__, ttl=int(self.cache_duration.total_seconds()))

            logger.info(f"✅ Token flow analyzed for {token_symbol}: {net_flow_24h:,.0f} net flow")
            return token_flow

        except Exception as e:
            logger.error(f"Error analyzing token flow for {token_symbol}: {e}")
            return None

    async def get_all_protocols_economics(self) -> Dict[str, Dict[str, Any]]:
        """Get comprehensive economics data for all protocols"""
        try:
            logger.info("💰 Collecting P2E economics for all gaming protocols...")

            # Check cache first
            cache_key = "p2e_economics:all_protocols"
            cached_data = gaming_cache.get(cache_key)
            if cached_data:
                logger.info("💰 Retrieved cached P2E economics for all protocols")
                return cached_data

            all_economics = {}

            # Collect data for all protocols concurrently
            tasks = []
            for protocol_name in self.p2e_protocols.keys():
                tasks.extend([
                    self.get_protocol_token_economics(protocol_name),
                    self.get_earning_mechanics(protocol_name)
                ])

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for i, protocol_name in enumerate(self.p2e_protocols.keys()):
                token_economics_result = results[i * 2]
                earning_mechanics_result = results[i * 2 + 1]

                protocol_data = {
                    'token_economics': {},
                    'earning_mechanics': None,
                    'token_flows': {}
                }

                if isinstance(token_economics_result, dict):
                    protocol_data['token_economics'] = {
                        token: econ.__dict__ for token, econ in token_economics_result.items()
                    }

                if isinstance(earning_mechanics_result, EarningMechanics):
                    protocol_data['earning_mechanics'] = earning_mechanics_result.__dict__

                # Get token flows for each token
                for token_symbol in self.p2e_protocols[protocol_name]['tokens'].keys():
                    try:
                        token_flow = await self.get_token_flow_analysis(protocol_name, token_symbol)
                        if token_flow:
                            protocol_data['token_flows'][token_symbol] = token_flow.__dict__
                    except Exception as e:
                        logger.error(f"Error getting token flow for {token_symbol}: {e}")

                all_economics[protocol_name] = protocol_data

            # Cache the results
            gaming_cache.set(cache_key, all_economics, ttl=int(self.cache_duration.total_seconds()))

            logger.info(f"✅ P2E economics collected for {len(all_economics)} protocols")
            return all_economics

        except Exception as e:
            logger.error(f"Error collecting all protocols P2E economics: {e}")
            return {}


# Global instance
p2e_economics_tracker = P2EEconomicsTracker()
