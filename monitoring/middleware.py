"""
FastAPI middleware for automatic Prometheus metrics collection
Tracks HTTP requests, response times, and system performance
"""
import time
import psutil
import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from .prometheus_metrics import (
    record_http_request,
    http_requests_in_progress,
    update_system_metrics,
    database_connections_active,
    redis_connections_active,
    websocket_connections_active
)

logger = logging.getLogger(__name__)


class PrometheusMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting Prometheus metrics from HTTP requests"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.active_requests = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and collect metrics"""
        # Skip metrics collection for the metrics endpoint itself
        if request.url.path == "/metrics":
            return await call_next(request)
        
        # Extract request info
        method = request.method
        endpoint = self._get_endpoint_name(request)
        
        # Track request start
        start_time = time.time()
        request_id = id(request)
        
        # Increment in-progress counter
        http_requests_in_progress.labels(method=method, endpoint=endpoint).inc()
        self.active_requests[request_id] = (method, endpoint, start_time)
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            status_code = response.status_code
            
            # Record metrics
            record_http_request(method, endpoint, status_code, duration)
            
            return response
            
        except Exception as e:
            # Record error metrics
            duration = time.time() - start_time
            record_http_request(method, endpoint, 500, duration)
            raise
            
        finally:
            # Decrement in-progress counter
            if request_id in self.active_requests:
                method, endpoint, _ = self.active_requests.pop(request_id)
                http_requests_in_progress.labels(method=method, endpoint=endpoint).dec()
    
    def _get_endpoint_name(self, request: Request) -> str:
        """Extract meaningful endpoint name from request"""
        path = request.url.path
        
        # Normalize common patterns
        if path.startswith("/api/v1/"):
            # Extract API endpoint pattern
            parts = path.split("/")
            if len(parts) >= 4:
                # /api/v1/articles -> articles
                # /api/v1/gaming/analytics -> gaming_analytics
                endpoint_parts = parts[3:]
                
                # Replace IDs with placeholder
                normalized_parts = []
                for part in endpoint_parts:
                    if part.isdigit():
                        normalized_parts.append("{id}")
                    else:
                        normalized_parts.append(part)
                
                return "_".join(normalized_parts)
        
        # Handle root paths
        if path == "/":
            return "root"
        elif path == "/health":
            return "health"
        elif path == "/stats":
            return "stats"
        elif path.startswith("/docs"):
            return "docs"
        elif path.startswith("/redoc"):
            return "redoc"
        
        # Default: use path with slashes replaced
        return path.replace("/", "_").strip("_") or "unknown"


class SystemMetricsCollector:
    """Collects system-level metrics periodically"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.last_update = 0
        self.update_interval = 30  # Update every 30 seconds
    
    async def collect_metrics(self):
        """Collect and update system metrics"""
        current_time = time.time()
        
        # Only update if enough time has passed
        if current_time - self.last_update < self.update_interval:
            return
        
        try:
            # Memory metrics
            memory_info = self.process.memory_info()
            memory_bytes = memory_info.rss  # Resident Set Size
            
            # CPU metrics
            cpu_percent = self.process.cpu_percent()
            
            # Update Prometheus metrics
            update_system_metrics(memory_bytes, cpu_percent)
            
            # Update database connection metrics (if available)
            await self._update_database_metrics()
            
            # Update Redis connection metrics (if available)
            await self._update_redis_metrics()
            
            self.last_update = current_time
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    async def _update_database_metrics(self):
        """Update database connection metrics"""
        try:
            from models.base import engine
            
            # Get connection pool info
            pool = engine.pool
            active_connections = pool.checkedout()
            database_connections_active.set(active_connections)
            
        except Exception as e:
            logger.debug(f"Could not collect database metrics: {e}")
    
    async def _update_redis_metrics(self):
        """Update Redis connection metrics"""
        try:
            import redis
            from config.settings import get_settings
            
            settings = get_settings()
            r = redis.from_url(settings.redis.url)
            
            # Get Redis info
            info = r.info()
            connected_clients = info.get('connected_clients', 0)
            redis_connections_active.set(connected_clients)
            
        except Exception as e:
            logger.debug(f"Could not collect Redis metrics: {e}")


class WebSocketMetricsTracker:
    """Tracks WebSocket connection metrics"""
    
    def __init__(self):
        self.active_connections = set()
    
    def connection_opened(self, connection_id: str):
        """Track new WebSocket connection"""
        self.active_connections.add(connection_id)
        websocket_connections_active.set(len(self.active_connections))
    
    def connection_closed(self, connection_id: str):
        """Track closed WebSocket connection"""
        self.active_connections.discard(connection_id)
        websocket_connections_active.set(len(self.active_connections))
    
    def message_sent(self, message_type: str):
        """Track WebSocket message sent"""
        from .prometheus_metrics import websocket_messages_sent_total
        websocket_messages_sent_total.labels(message_type=message_type).inc()


# Global instances
system_metrics_collector = SystemMetricsCollector()
websocket_metrics_tracker = WebSocketMetricsTracker()


class MetricsMiddleware(BaseHTTPMiddleware):
    """Combined middleware for all metrics collection"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.prometheus_middleware = PrometheusMiddleware(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Dispatch request through metrics collection"""
        # Collect system metrics periodically
        await system_metrics_collector.collect_metrics()
        
        # Process through Prometheus middleware
        return await self.prometheus_middleware.dispatch(request, call_next)


# Utility functions for manual metrics tracking

def track_gaming_source_metrics():
    """Update gaming source metrics"""
    try:
        from scrapers.news.gaming_sources import GAMING_SCRAPERS
        from .prometheus_metrics import gaming_news_sources_active
        
        active_sources = len(GAMING_SCRAPERS)
        gaming_news_sources_active.set(active_sources)
        
    except Exception as e:
        logger.error(f"Error updating gaming source metrics: {e}")


def track_gaming_project_metrics():
    """Update gaming project metrics"""
    try:
        from scrapers.news.entity_recognition import entity_engine
        from .prometheus_metrics import gaming_projects_tracked, solana_gaming_projects_active
        
        # Get total projects tracked
        total_projects = len(entity_engine.project_registry.projects)
        gaming_projects_tracked.set(total_projects)
        
        # Count Solana projects
        solana_projects = sum(
            1 for project in entity_engine.project_registry.projects.values()
            if 'solana' in project.blockchain_networks
        )
        solana_gaming_projects_active.set(solana_projects)
        
    except Exception as e:
        logger.error(f"Error updating gaming project metrics: {e}")


def track_blockchain_sync_metrics():
    """Update blockchain sync status metrics"""
    try:
        from blockchain.rpc import get_all_rpc_managers
        from .prometheus_metrics import blockchain_sync_status, blockchain_latest_block
        
        rpc_managers = get_all_rpc_managers()
        
        for network, manager in rpc_managers.items():
            try:
                # Check if RPC is healthy
                is_healthy = manager.is_healthy()
                blockchain_sync_status.labels(network=network).set(1 if is_healthy else 0)
                
                # Get latest block (if available)
                if hasattr(manager, 'get_latest_block_number'):
                    latest_block = manager.get_latest_block_number()
                    if latest_block:
                        blockchain_latest_block.labels(network=network).set(latest_block)
                
            except Exception as e:
                logger.debug(f"Could not get sync status for {network}: {e}")
                blockchain_sync_status.labels(network=network).set(0)
        
    except Exception as e:
        logger.error(f"Error updating blockchain sync metrics: {e}")


def track_cross_chain_metrics():
    """Update cross-chain distribution metrics"""
    try:
        from models.base import SessionLocal
        from models.gaming import Article
        from sqlalchemy import func
        from .prometheus_metrics import cross_chain_articles_distribution
        
        db = SessionLocal()
        try:
            # Query article distribution by blockchain network
            # This assumes articles have blockchain classification in extra_metadata
            results = db.query(Article).all()
            
            network_counts = {}
            for article in results:
                if article.extra_metadata:
                    blockchain_classification = article.extra_metadata.get('blockchain_classification', {})
                    networks = blockchain_classification.get('blockchain_networks', [])
                    
                    for network in networks:
                        network_counts[network] = network_counts.get(network, 0) + 1
            
            # Update metrics
            for network, count in network_counts.items():
                cross_chain_articles_distribution.labels(network=network).set(count)
        
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Error updating cross-chain metrics: {e}")


async def update_all_custom_metrics():
    """Update all custom metrics that require database queries"""
    track_gaming_source_metrics()
    track_gaming_project_metrics()
    track_blockchain_sync_metrics()
    track_cross_chain_metrics()
