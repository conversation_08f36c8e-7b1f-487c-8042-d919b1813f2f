"""
Custom metrics collectors for Web3 Gaming News Tracker
Specialized metrics for gaming, blockchain, and news analytics
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from sqlalchemy import func, and_, desc
from sqlalchemy.orm import Session

from models.base import SessionLocal
from models.gaming import Article, Source, GamingProject, NFTCollection, BlockchainData
from .prometheus_metrics import (
    gaming_news_articles_total,
    gaming_projects_mentioned_total,
    gaming_contracts_monitored,
    gaming_contract_events_total,
    cross_chain_gaming_activity_score,
    solana_gaming_articles_total,
    update_token_price,
    record_gaming_project_mention
)

logger = logging.getLogger(__name__)


class GamingMetricsCollector:
    """Collects gaming-specific metrics from database"""
    
    def __init__(self):
        self.last_update = datetime.utcnow()
        self.update_interval = timedelta(minutes=5)  # Update every 5 minutes
    
    async def collect_all_metrics(self):
        """Collect all gaming metrics"""
        current_time = datetime.utcnow()
        
        # Only update if enough time has passed
        if current_time - self.last_update < self.update_interval:
            return
        
        try:
            await self.collect_article_metrics()
            await self.collect_project_metrics()
            await self.collect_blockchain_metrics()
            await self.collect_cross_chain_metrics()
            await self.collect_solana_metrics()
            
            self.last_update = current_time
            logger.info("Gaming metrics updated successfully")
            
        except Exception as e:
            logger.error(f"Error collecting gaming metrics: {e}")
    
    async def collect_article_metrics(self):
        """Collect article-related metrics"""
        db = SessionLocal()
        try:
            # Get articles from last 24 hours
            since = datetime.utcnow() - timedelta(hours=24)
            recent_articles = db.query(Article).filter(
                Article.published_at >= since
            ).all()
            
            # Count by source and blockchain network
            source_network_counts = {}
            category_counts = {}
            
            for article in recent_articles:
                source_name = article.source.name if article.source else "unknown"
                
                # Extract blockchain classification from metadata
                blockchain_networks = []
                gaming_categories = []
                
                if article.extra_metadata:
                    blockchain_classification = article.extra_metadata.get('blockchain_classification', {})
                    blockchain_networks = blockchain_classification.get('blockchain_networks', ['unknown'])
                    gaming_categories = blockchain_classification.get('gaming_categories', ['unknown'])
                
                # Count by source and network
                for network in blockchain_networks:
                    for category in gaming_categories:
                        key = (source_name, network, category)
                        source_network_counts[key] = source_network_counts.get(key, 0) + 1
            
            # Update Prometheus metrics
            # Note: We increment by the count since last update
            for (source, network, category), count in source_network_counts.items():
                gaming_news_articles_total.labels(
                    source=source,
                    blockchain_network=network,
                    gaming_category=category
                ).inc(count)
        
        finally:
            db.close()
    
    async def collect_project_metrics(self):
        """Collect gaming project mention metrics"""
        db = SessionLocal()
        try:
            # Get recent articles with project mentions
            since = datetime.utcnow() - timedelta(hours=24)
            recent_articles = db.query(Article).filter(
                Article.published_at >= since
            ).all()
            
            project_network_counts = {}
            
            for article in recent_articles:
                if article.extra_metadata:
                    # Extract gaming project mentions
                    gaming_analysis = article.extra_metadata.get('gaming_analysis', {})
                    mentioned_projects = gaming_analysis.get('mentioned_projects', [])
                    
                    # Extract blockchain networks
                    blockchain_classification = article.extra_metadata.get('blockchain_classification', {})
                    networks = blockchain_classification.get('blockchain_networks', ['unknown'])
                    
                    # Count project mentions by network
                    for project in mentioned_projects:
                        project_name = project.get('name', 'unknown')
                        for network in networks:
                            key = (project_name, network)
                            project_network_counts[key] = project_network_counts.get(key, 0) + 1
            
            # Update metrics
            for (project, network), count in project_network_counts.items():
                gaming_projects_mentioned_total.labels(
                    project=project,
                    blockchain_network=network
                ).inc(count)
        
        finally:
            db.close()
    
    async def collect_blockchain_metrics(self):
        """Collect blockchain-related metrics"""
        db = SessionLocal()
        try:
            # Count monitored gaming contracts by network
            contracts = db.query(BlockchainData).filter(
                BlockchainData.data_type == 'gaming_contract'
            ).all()
            
            network_contract_counts = {}
            for contract in contracts:
                network = contract.blockchain_network
                network_contract_counts[network] = network_contract_counts.get(network, 0) + 1
            
            # Update contract monitoring metrics
            for network, count in network_contract_counts.items():
                gaming_contracts_monitored.labels(network=network).set(count)
            
            # Count recent contract events
            since = datetime.utcnow() - timedelta(hours=24)
            recent_events = db.query(BlockchainData).filter(
                and_(
                    BlockchainData.data_type == 'contract_event',
                    BlockchainData.timestamp >= since
                )
            ).all()
            
            event_counts = {}
            for event in recent_events:
                network = event.blockchain_network
                contract = event.extra_data.get('contract_address', 'unknown') if event.extra_data else 'unknown'
                event_type = event.extra_data.get('event_type', 'unknown') if event.extra_data else 'unknown'
                
                key = (network, contract, event_type)
                event_counts[key] = event_counts.get(key, 0) + 1
            
            # Update event metrics
            for (network, contract, event_type), count in event_counts.items():
                gaming_contract_events_total.labels(
                    network=network,
                    contract=contract,
                    event_type=event_type
                ).inc(count)
        
        finally:
            db.close()
    
    async def collect_cross_chain_metrics(self):
        """Collect cross-chain activity metrics"""
        db = SessionLocal()
        try:
            # Calculate gaming activity score by network
            since = datetime.utcnow() - timedelta(days=7)  # Last 7 days
            
            # Get articles by network
            articles = db.query(Article).filter(
                Article.published_at >= since
            ).all()
            
            network_scores = {}
            
            for article in articles:
                if article.extra_metadata:
                    blockchain_classification = article.extra_metadata.get('blockchain_classification', {})
                    networks = blockchain_classification.get('blockchain_networks', [])
                    
                    # Calculate score based on article engagement and gaming relevance
                    gaming_analysis = article.extra_metadata.get('gaming_analysis', {})
                    gaming_score = gaming_analysis.get('gaming_relevance_score', 0.5)
                    
                    # Weight by recency (more recent = higher score)
                    days_old = (datetime.utcnow() - article.published_at).days
                    recency_weight = max(0.1, 1.0 - (days_old / 7.0))
                    
                    article_score = gaming_score * recency_weight
                    
                    for network in networks:
                        network_scores[network] = network_scores.get(network, 0) + article_score
            
            # Normalize scores (0-100 scale)
            if network_scores:
                max_score = max(network_scores.values())
                if max_score > 0:
                    for network, score in network_scores.items():
                        normalized_score = (score / max_score) * 100
                        cross_chain_gaming_activity_score.labels(network=network).set(normalized_score)
        
        finally:
            db.close()
    
    async def collect_solana_metrics(self):
        """Collect Solana-specific metrics"""
        db = SessionLocal()
        try:
            # Count Solana gaming articles by source
            since = datetime.utcnow() - timedelta(hours=24)
            
            solana_articles = db.query(Article).filter(
                Article.published_at >= since
            ).all()
            
            solana_source_counts = {}
            
            for article in solana_articles:
                if article.extra_metadata:
                    blockchain_classification = article.extra_metadata.get('blockchain_classification', {})
                    networks = blockchain_classification.get('blockchain_networks', [])
                    
                    if 'solana' in networks:
                        source_name = article.source.name if article.source else "unknown"
                        solana_source_counts[source_name] = solana_source_counts.get(source_name, 0) + 1
            
            # Update Solana metrics
            for source, count in solana_source_counts.items():
                solana_gaming_articles_total.labels(source=source).inc(count)
        
        finally:
            db.close()


class MarketDataMetricsCollector:
    """Collects market data metrics for gaming tokens"""
    
    def __init__(self):
        self.last_update = datetime.utcnow()
        self.update_interval = timedelta(minutes=15)  # Update every 15 minutes
    
    async def collect_token_metrics(self):
        """Collect gaming token price metrics"""
        current_time = datetime.utcnow()
        
        if current_time - self.last_update < self.update_interval:
            return
        
        try:
            from blockchain.market_data import GamingMarketDataManager
            
            market_manager = GamingMarketDataManager()
            
            # Gaming tokens to track
            gaming_tokens = [
                ('AXS', 'ethereum'),
                ('SLP', 'ethereum'),
                ('SAND', 'ethereum'),
                ('MANA', 'ethereum'),
                ('ENJ', 'ethereum'),
                ('GALA', 'ethereum'),
                ('ILV', 'ethereum'),
                ('ALICE', 'ethereum'),
                ('TLM', 'ethereum'),
                ('SKILL', 'ethereum'),
                # Solana gaming tokens
                ('ATLAS', 'solana'),
                ('POLIS', 'solana'),
                ('GMT', 'solana'),
                ('GST', 'solana')
            ]
            
            for symbol, network in gaming_tokens:
                try:
                    # Get token price data
                    price_data = await market_manager.get_token_price(symbol)
                    
                    if price_data:
                        update_token_price(
                            symbol=symbol,
                            network=network,
                            price_usd=price_data.price_usd,
                            change_24h=price_data.change_24h
                        )
                
                except Exception as e:
                    logger.debug(f"Could not get price for {symbol}: {e}")
            
            self.last_update = current_time
            
        except Exception as e:
            logger.error(f"Error collecting token metrics: {e}")


class RealTimeMetricsCollector:
    """Collects real-time monitoring metrics"""
    
    def __init__(self):
        self.last_update = datetime.utcnow()
        self.update_interval = timedelta(minutes=1)  # Update every minute
    
    async def collect_real_time_metrics(self):
        """Collect real-time monitoring metrics"""
        current_time = datetime.utcnow()
        
        if current_time - self.last_update < self.update_interval:
            return
        
        try:
            from scrapers.news.real_time_monitor import real_time_monitor
            from .prometheus_metrics import (
                real_time_monitor_checks_total,
                real_time_monitor_processing_duration_seconds
            )
            
            # Get monitor statistics
            stats = real_time_monitor.get_statistics()
            
            # Update check metrics by source
            for source, source_stats in stats.get('source_stats', {}).items():
                checks = source_stats.get('total_checks', 0)
                real_time_monitor_checks_total.labels(source=source).inc(checks)
            
            # Update processing time
            avg_processing_time = stats.get('average_processing_time', 0)
            if avg_processing_time > 0:
                real_time_monitor_processing_duration_seconds.observe(avg_processing_time)
            
            self.last_update = current_time
            
        except Exception as e:
            logger.error(f"Error collecting real-time metrics: {e}")


# Global collectors
gaming_metrics_collector = GamingMetricsCollector()
market_metrics_collector = MarketDataMetricsCollector()
realtime_metrics_collector = RealTimeMetricsCollector()


async def collect_all_custom_metrics():
    """Collect all custom metrics"""
    await asyncio.gather(
        gaming_metrics_collector.collect_all_metrics(),
        market_metrics_collector.collect_token_metrics(),
        realtime_metrics_collector.collect_real_time_metrics(),
        return_exceptions=True
    )


# Utility functions for immediate metric updates

def record_article_scraped(source: str, blockchain_networks: List[str], 
                          gaming_categories: List[str], count: int = 1):
    """Record article scraping metrics immediately"""
    for network in blockchain_networks or ['unknown']:
        for category in gaming_categories or ['unknown']:
            gaming_news_articles_total.labels(
                source=source,
                blockchain_network=network,
                gaming_category=category
            ).inc(count)


def record_project_mentioned(project_name: str, blockchain_networks: List[str]):
    """Record gaming project mention immediately"""
    for network in blockchain_networks or ['unknown']:
        record_gaming_project_mention(project_name, network)


def record_contract_event(network: str, contract: str, event_type: str):
    """Record gaming contract event immediately"""
    gaming_contract_events_total.labels(
        network=network,
        contract=contract,
        event_type=event_type
    ).inc()


def record_solana_article(source: str):
    """Record Solana gaming article immediately"""
    solana_gaming_articles_total.labels(source=source).inc()
