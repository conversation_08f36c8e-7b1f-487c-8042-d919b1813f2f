"""
Prometheus metrics for Web3 Gaming News Tracker
Comprehensive metrics collection for system monitoring and analytics
"""
import time
import logging
from typing import Dict, Any, Optional
from functools import wraps
from prometheus_client import (
    Counter, Gauge, Histogram, Summary, Info,
    CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
)
from fastapi import Request, Response
from fastapi.responses import PlainTextResponse

logger = logging.getLogger(__name__)

# Create custom registry for our metrics
registry = CollectorRegistry()

# =============================================================================
# API Metrics
# =============================================================================

# HTTP request metrics
http_requests_total = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code'],
    registry=registry
)

http_request_duration_seconds = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint'],
    registry=registry
)

http_requests_in_progress = Gauge(
    'http_requests_in_progress',
    'HTTP requests currently in progress',
    ['method', 'endpoint'],
    registry=registry
)

# =============================================================================
# Gaming News Metrics
# =============================================================================

# News scraping metrics
gaming_news_articles_total = Counter(
    'gaming_news_articles_total',
    'Total gaming news articles scraped',
    ['source', 'blockchain_network', 'gaming_category'],
    registry=registry
)

gaming_news_scraping_duration_seconds = Histogram(
    'gaming_news_scraping_duration_seconds',
    'Time spent scraping gaming news sources',
    ['source'],
    registry=registry
)

gaming_news_scraping_errors_total = Counter(
    'gaming_news_scraping_errors_total',
    'Total gaming news scraping errors',
    ['source', 'error_type'],
    registry=registry
)

gaming_news_sources_active = Gauge(
    'gaming_news_sources_active',
    'Number of active gaming news sources',
    registry=registry
)

# Gaming project metrics
gaming_projects_mentioned_total = Counter(
    'gaming_projects_mentioned_total',
    'Total gaming project mentions in articles',
    ['project', 'blockchain_network'],
    registry=registry
)

gaming_projects_tracked = Gauge(
    'gaming_projects_tracked',
    'Number of gaming projects being tracked',
    registry=registry
)

# =============================================================================
# Blockchain Metrics
# =============================================================================

# Blockchain sync metrics
blockchain_sync_status = Gauge(
    'blockchain_sync_status',
    'Blockchain sync status (1=synced, 0=not synced)',
    ['network'],
    registry=registry
)

blockchain_latest_block = Gauge(
    'blockchain_latest_block',
    'Latest block number for blockchain network',
    ['network'],
    registry=registry
)

blockchain_rpc_requests_total = Counter(
    'blockchain_rpc_requests_total',
    'Total blockchain RPC requests',
    ['network', 'method', 'status'],
    registry=registry
)

blockchain_rpc_duration_seconds = Histogram(
    'blockchain_rpc_duration_seconds',
    'Blockchain RPC request duration',
    ['network', 'method'],
    registry=registry
)

# Gaming contract metrics
gaming_contracts_monitored = Gauge(
    'gaming_contracts_monitored',
    'Number of gaming contracts being monitored',
    ['network'],
    registry=registry
)

gaming_contract_events_total = Counter(
    'gaming_contract_events_total',
    'Total gaming contract events processed',
    ['network', 'contract', 'event_type'],
    registry=registry
)

# =============================================================================
# Market Data Metrics
# =============================================================================

# Token price metrics
gaming_token_prices = Gauge(
    'gaming_token_prices_usd',
    'Gaming token prices in USD',
    ['symbol', 'network'],
    registry=registry
)

gaming_token_price_changes_24h = Gauge(
    'gaming_token_price_changes_24h_percent',
    'Gaming token 24h price changes in percent',
    ['symbol', 'network'],
    registry=registry
)

market_data_requests_total = Counter(
    'market_data_requests_total',
    'Total market data API requests',
    ['provider', 'status'],
    registry=registry
)

# =============================================================================
# System Health Metrics
# =============================================================================

# Database metrics
database_connections_active = Gauge(
    'database_connections_active',
    'Active database connections',
    registry=registry
)

database_query_duration_seconds = Histogram(
    'database_query_duration_seconds',
    'Database query duration',
    ['operation'],
    registry=registry
)

database_queries_total = Counter(
    'database_queries_total',
    'Total database queries',
    ['operation', 'status'],
    registry=registry
)

# Redis metrics
redis_connections_active = Gauge(
    'redis_connections_active',
    'Active Redis connections',
    registry=registry
)

redis_operations_total = Counter(
    'redis_operations_total',
    'Total Redis operations',
    ['operation', 'status'],
    registry=registry
)

# Memory and CPU metrics
memory_usage_bytes = Gauge(
    'memory_usage_bytes',
    'Memory usage in bytes',
    registry=registry
)

cpu_usage_percent = Gauge(
    'cpu_usage_percent',
    'CPU usage percentage',
    registry=registry
)

# =============================================================================
# Real-time Monitoring Metrics
# =============================================================================

# Real-time monitor metrics
real_time_monitor_checks_total = Counter(
    'real_time_monitor_checks_total',
    'Total real-time monitor checks',
    ['source'],
    registry=registry
)

real_time_monitor_alerts_total = Counter(
    'real_time_monitor_alerts_total',
    'Total real-time monitor alerts generated',
    ['alert_type', 'priority'],
    registry=registry
)

real_time_monitor_processing_duration_seconds = Histogram(
    'real_time_monitor_processing_duration_seconds',
    'Real-time monitor processing duration',
    registry=registry
)

# WebSocket metrics
websocket_connections_active = Gauge(
    'websocket_connections_active',
    'Active WebSocket connections',
    registry=registry
)

websocket_messages_sent_total = Counter(
    'websocket_messages_sent_total',
    'Total WebSocket messages sent',
    ['message_type'],
    registry=registry
)

# =============================================================================
# Cross-chain Analytics Metrics
# =============================================================================

# Cross-chain activity metrics
cross_chain_articles_distribution = Gauge(
    'cross_chain_articles_distribution',
    'Distribution of articles across blockchain networks',
    ['network'],
    registry=registry
)

cross_chain_gaming_activity_score = Gauge(
    'cross_chain_gaming_activity_score',
    'Gaming activity score by blockchain network',
    ['network'],
    registry=registry
)

# Solana-specific metrics (as requested by user)
solana_gaming_articles_total = Counter(
    'solana_gaming_articles_total',
    'Total Solana gaming articles',
    ['source'],
    registry=registry
)

solana_gaming_projects_active = Gauge(
    'solana_gaming_projects_active',
    'Number of active Solana gaming projects',
    registry=registry
)

# =============================================================================
# Application Info Metrics
# =============================================================================

# Application info
app_info = Info(
    'web3_gaming_tracker_info',
    'Web3 Gaming News Tracker application info',
    registry=registry
)

# Set application info
app_info.info({
    'version': '1.0.0',
    'phase': '5',
    'description': 'Web3 Gaming News Tracker with Visualization',
    'supported_networks': 'ethereum,solana,polygon,bsc,arbitrum,optimism,base,avalanche,immutable,ronin,ton',
    'gaming_sources': '19',
    'solana_sources': '5'
})

# =============================================================================
# Metric Helper Functions
# =============================================================================

def record_http_request(method: str, endpoint: str, status_code: int, duration: float):
    """Record HTTP request metrics"""
    http_requests_total.labels(method=method, endpoint=endpoint, status_code=status_code).inc()
    http_request_duration_seconds.labels(method=method, endpoint=endpoint).observe(duration)

def record_scraping_metrics(source: str, duration: float, articles_count: int, 
                          blockchain_network: str = "", gaming_category: str = ""):
    """Record news scraping metrics"""
    gaming_news_scraping_duration_seconds.labels(source=source).observe(duration)
    if articles_count > 0:
        gaming_news_articles_total.labels(
            source=source, 
            blockchain_network=blockchain_network or "unknown",
            gaming_category=gaming_category or "unknown"
        ).inc(articles_count)

def record_scraping_error(source: str, error_type: str):
    """Record scraping error"""
    gaming_news_scraping_errors_total.labels(source=source, error_type=error_type).inc()

def record_blockchain_rpc(network: str, method: str, duration: float, success: bool):
    """Record blockchain RPC metrics"""
    status = "success" if success else "error"
    blockchain_rpc_requests_total.labels(network=network, method=method, status=status).inc()
    blockchain_rpc_duration_seconds.labels(network=network, method=method).observe(duration)

def record_gaming_project_mention(project: str, blockchain_network: str):
    """Record gaming project mention"""
    gaming_projects_mentioned_total.labels(project=project, blockchain_network=blockchain_network).inc()

def record_market_data_request(provider: str, success: bool):
    """Record market data request"""
    status = "success" if success else "error"
    market_data_requests_total.labels(provider=provider, status=status).inc()

def update_token_price(symbol: str, network: str, price_usd: float, change_24h: float):
    """Update token price metrics"""
    gaming_token_prices.labels(symbol=symbol, network=network).set(price_usd)
    gaming_token_price_changes_24h.labels(symbol=symbol, network=network).set(change_24h)

def record_database_query(operation: str, duration: float, success: bool):
    """Record database query metrics"""
    status = "success" if success else "error"
    database_queries_total.labels(operation=operation, status=status).inc()
    database_query_duration_seconds.labels(operation=operation).observe(duration)

def record_real_time_alert(alert_type: str, priority: str):
    """Record real-time alert"""
    real_time_monitor_alerts_total.labels(alert_type=alert_type, priority=priority).inc()

def update_system_metrics(memory_bytes: int, cpu_percent: float):
    """Update system resource metrics"""
    memory_usage_bytes.set(memory_bytes)
    cpu_usage_percent.set(cpu_percent)

def record_websocket_connection():
    """Record WebSocket connection"""
    websocket_connections_active.inc()

def record_websocket_disconnection():
    """Record WebSocket disconnection"""
    websocket_connections_active.dec()

def record_websocket_message(message_type: str):
    """Record WebSocket message sent"""
    websocket_messages_sent_total.labels(message_type=message_type).inc()

# =============================================================================
# Metrics Endpoint
# =============================================================================

async def metrics_endpoint() -> Response:
    """Prometheus metrics endpoint"""
    try:
        metrics_data = generate_latest(registry)
        return PlainTextResponse(
            content=metrics_data.decode('utf-8'),
            media_type=CONTENT_TYPE_LATEST
        )
    except Exception as e:
        logger.error(f"Error generating metrics: {e}")
        return PlainTextResponse(
            content="# Error generating metrics\n",
            status_code=500
        )

# =============================================================================
# Decorators for Automatic Metrics Collection
# =============================================================================

def track_time(metric_name: str, labels: Dict[str, str] = None):
    """Decorator to track execution time"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record success metric
                if metric_name == "scraping":
                    record_scraping_metrics(
                        labels.get('source', 'unknown'), 
                        duration, 
                        getattr(result, 'count', 0),
                        labels.get('blockchain_network', ''),
                        labels.get('gaming_category', '')
                    )
                elif metric_name == "blockchain_rpc":
                    record_blockchain_rpc(
                        labels.get('network', 'unknown'),
                        labels.get('method', 'unknown'),
                        duration,
                        True
                    )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                # Record error metric
                if metric_name == "scraping":
                    record_scraping_error(
                        labels.get('source', 'unknown'),
                        type(e).__name__
                    )
                elif metric_name == "blockchain_rpc":
                    record_blockchain_rpc(
                        labels.get('network', 'unknown'),
                        labels.get('method', 'unknown'),
                        duration,
                        False
                    )
                
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record success metric based on metric type
                if metric_name == "database":
                    record_database_query(
                        labels.get('operation', 'unknown'),
                        duration,
                        True
                    )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                # Record error metric
                if metric_name == "database":
                    record_database_query(
                        labels.get('operation', 'unknown'),
                        duration,
                        False
                    )
                
                raise
        
        # Return appropriate wrapper based on function type
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
