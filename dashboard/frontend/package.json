{"name": "web3-gaming-dashboard", "version": "1.0.0", "description": "Web3 Gaming News Tracker Dashboard", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.3.0", "axios": "^1.4.0", "recharts": "^2.7.2", "chart.js": "^4.3.0", "react-chartjs-2": "^5.2.0", "@mui/material": "^5.14.1", "@mui/icons-material": "^5.14.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.9.2", "@mui/x-charts": "^6.0.0-alpha.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-query": "^3.39.3", "socket.io-client": "^4.7.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8001"}