import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { QueryClient, QueryClientProvider } from 'react-query';

import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import GamingAnalytics from './pages/GamingAnalytics';
import SocialMediaPage from './pages/SocialMediaPage';
import GamingConfig from './components/GamingConfig';
import AddGamePage from './pages/AddGamePage';
// Other pages will be created as needed
const NewsAnalytics = () => <div>News Analytics Page</div>;
const CrossChain = () => <div>Cross Chain Page</div>;
const RealTime = () => <div>Real Time Page</div>;
const Settings = () => <div>Settings Page</div>;

// Create a dark theme with ColorHunt gaming palette for Web3 gaming aesthetic
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#F14A00', // Bright orange for highlights and hover
      light: '#FF6B33',
      dark: '#CC3D00',
    },
    secondary: {
      main: '#C62300', // Bright red for important elements
      light: '#E63D1C',
      dark: '#A01D00',
    },
    tertiary: {
      main: '#500073', // Medium purple for sections
      light: '#6B1A96',
      dark: '#3D0056',
    },
    background: {
      default: '#2A004E', // Deep purple background
      paper: '#500073',   // Medium purple for cards
    },
    text: {
      primary: '#ffffff',   // Pure white
      secondary: '#e2e8f0', // Light gray
    },
    divider: '#500073',
  },
  typography: {
    fontFamily: '"DejaVu Sans Mono", "Courier New", Courier, monospace',
    fontFamilyMonospace: '"DejaVu Sans Mono", "Fira Code", "Monaco", "Consolas", "Courier New", monospace',
    h1: { fontWeight: 600, fontSize: '2.5rem' },
    h2: { fontWeight: 600, fontSize: '2rem' },
    h3: { fontWeight: 600, fontSize: '1.75rem' },
    h4: { fontWeight: 600, fontSize: '1.5rem' },
    h5: { fontWeight: 500, fontSize: '1.25rem' },
    h6: { fontWeight: 500, fontSize: '1rem' },
    body1: { fontSize: '1rem', lineHeight: 1.6 },
    body2: { fontSize: '0.875rem', lineHeight: 1.5 },
    code: {
      fontFamily: '"DejaVu Sans Mono", "Fira Code", "Monaco", "Consolas", "Courier New", monospace',
      fontSize: '0.875rem',
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundImage: 'linear-gradient(135deg, #500073 0%, #6B1A96 100%)',
          border: '1px solid #500073',
          borderRadius: '0.75rem',
          transition: 'all 0.25s ease-in-out',
          '&:hover': {
            borderColor: '#F14A00',
            boxShadow: '0 4px 12px rgba(241, 74, 0, 0.2)',
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundImage: 'linear-gradient(135deg, #2A004E 0%, #500073 100%)',
          borderBottom: '1px solid #500073',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem',
          textTransform: 'none',
          fontWeight: 500,
          transition: 'all 0.25s ease-in-out',
        },
        containedPrimary: {
          background: 'linear-gradient(135deg, #F14A00 0%, #FF6B33 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #CC3D00 0%, #F14A00 100%)',
            boxShadow: '0 4px 12px rgba(241, 74, 0, 0.3)',
          },
        },
        containedSecondary: {
          background: 'linear-gradient(135deg, #C62300 0%, #E63D1C 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #A01D00 0%, #C62300 100%)',
            boxShadow: '0 4px 12px rgba(198, 35, 0, 0.3)',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: '0.5rem',
            fontFamily: '"DejaVu Sans Mono", monospace',
            '& fieldset': {
              borderColor: '#500073',
            },
            '&:hover fieldset': {
              borderColor: '#F14A00',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#F14A00',
            },
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem',
          fontFamily: '"DejaVu Sans Mono", monospace',
          fontSize: '0.75rem',
        },
        colorPrimary: {
          backgroundColor: 'rgba(241, 74, 0, 0.2)',
          color: '#FF6B33',
          border: '1px solid rgba(241, 74, 0, 0.3)',
        },
        colorSecondary: {
          backgroundColor: 'rgba(198, 35, 0, 0.2)',
          color: '#E63D1C',
          border: '1px solid rgba(198, 35, 0, 0.3)',
        },
      },
    },
  },
});

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={darkTheme}>
        <CssBaseline />
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/gaming-analytics" element={<GamingAnalytics />} />
              <Route path="/social-media" element={<SocialMediaPage />} />
              <Route path="/gaming-config" element={<GamingConfig />} />
              <Route path="/add-game" element={<AddGamePage />} />
              <Route path="/news-analytics" element={<NewsAnalytics />} />
              <Route path="/cross-chain" element={<CrossChain />} />
              <Route path="/real-time" element={<RealTime />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Layout>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
