import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { QueryClient, QueryClientProvider } from 'react-query';

import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import GamingAnalytics from './pages/GamingAnalytics';
import SocialMediaPage from './pages/SocialMediaPage';
import GamingConfig from './components/GamingConfig';
import AddGamePage from './pages/AddGamePage';
// Other pages will be created as needed
const NewsAnalytics = () => <div>News Analytics Page</div>;
const CrossChain = () => <div>Cross Chain Page</div>;
const RealTime = () => <div>Real Time Page</div>;
const Settings = () => <div>Settings Page</div>;

// Create a dark theme with muted jewel tones for Web3 gaming aesthetic
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#c4704a', // Muted reddish-orange
      light: '#d4825a',
      dark: '#a85d3a',
    },
    secondary: {
      main: '#4a8b5c', // Muted emerald green
      light: '#5a9b6c',
      dark: '#3a7b4c',
    },
    tertiary: {
      main: '#8b4a8b', // Muted violet-magenta
      light: '#9b5a9b',
      dark: '#7b3a7b',
    },
    background: {
      default: '#0a0e1a', // Deep dark blue-black
      paper: '#1a1f2e',   // Slightly lighter dark blue
    },
    text: {
      primary: '#f7fafc',   // Almost white
      secondary: '#e2e8f0', // Light gray
    },
    divider: '#2d3748',
  },
  typography: {
    fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", sans-serif',
    fontFamilyMonospace: '"DejaVu Sans Mono", "Fira Code", "Monaco", "Consolas", "Courier New", monospace',
    h1: { fontWeight: 600, fontSize: '2.5rem' },
    h2: { fontWeight: 600, fontSize: '2rem' },
    h3: { fontWeight: 600, fontSize: '1.75rem' },
    h4: { fontWeight: 600, fontSize: '1.5rem' },
    h5: { fontWeight: 500, fontSize: '1.25rem' },
    h6: { fontWeight: 500, fontSize: '1rem' },
    body1: { fontSize: '1rem', lineHeight: 1.6 },
    body2: { fontSize: '0.875rem', lineHeight: 1.5 },
    code: {
      fontFamily: '"DejaVu Sans Mono", "Fira Code", "Monaco", "Consolas", "Courier New", monospace',
      fontSize: '0.875rem',
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundImage: 'linear-gradient(135deg, #1a1f2e 0%, #2d3748 100%)',
          border: '1px solid #2d3748',
          borderRadius: '0.75rem',
          transition: 'all 0.25s ease-in-out',
          '&:hover': {
            borderColor: '#c4704a',
            boxShadow: '0 4px 12px rgba(196, 112, 74, 0.1)',
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundImage: 'linear-gradient(135deg, #1a1f2e 0%, #2d3748 100%)',
          borderBottom: '1px solid #2d3748',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem',
          textTransform: 'none',
          fontWeight: 500,
          transition: 'all 0.25s ease-in-out',
        },
        containedPrimary: {
          background: 'linear-gradient(135deg, #c4704a 0%, #d4825a 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #a85d3a 0%, #c4704a 100%)',
            boxShadow: '0 4px 12px rgba(196, 112, 74, 0.3)',
          },
        },
        containedSecondary: {
          background: 'linear-gradient(135deg, #4a8b5c 0%, #5a9b6c 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #3a7b4c 0%, #4a8b5c 100%)',
            boxShadow: '0 4px 12px rgba(74, 139, 92, 0.3)',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: '0.5rem',
            fontFamily: '"DejaVu Sans Mono", monospace',
            '& fieldset': {
              borderColor: '#2d3748',
            },
            '&:hover fieldset': {
              borderColor: '#c4704a',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#c4704a',
            },
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem',
          fontFamily: '"DejaVu Sans Mono", monospace',
          fontSize: '0.75rem',
        },
        colorPrimary: {
          backgroundColor: 'rgba(196, 112, 74, 0.2)',
          color: '#d4825a',
          border: '1px solid rgba(196, 112, 74, 0.3)',
        },
        colorSecondary: {
          backgroundColor: 'rgba(74, 139, 92, 0.2)',
          color: '#5a9b6c',
          border: '1px solid rgba(74, 139, 92, 0.3)',
        },
      },
    },
  },
});

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={darkTheme}>
        <CssBaseline />
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/gaming-analytics" element={<GamingAnalytics />} />
              <Route path="/social-media" element={<SocialMediaPage />} />
              <Route path="/gaming-config" element={<GamingConfig />} />
              <Route path="/add-game" element={<AddGamePage />} />
              <Route path="/news-analytics" element={<NewsAnalytics />} />
              <Route path="/cross-chain" element={<CrossChain />} />
              <Route path="/real-time" element={<RealTime />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Layout>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
