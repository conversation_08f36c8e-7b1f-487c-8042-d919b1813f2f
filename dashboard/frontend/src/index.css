/* Import DejaVu Sans Mono font, theme, and component styles */
@import './fonts/dejavu-sans-mono.css';
@import './styles/theme.css';
@import './styles/components.css';

body {
  margin: 0;
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--gradient-primary);
  color: var(--text-primary);
  min-height: 100vh;
}

/* Ensure DejaVu Sans Mono is applied to all elements */
* {
  font-family: 'DejaVu Sans Mono', 'Courier New', Courier, monospace !important;
}

/* Material-UI component font override */
.MuiTypography-root,
.MuiButton-root,
.MuiTextField-root,
.MuiInputBase-root,
.MuiFormLabel-root,
.MuiChip-root,
.MuiTab-root,
.MuiMenuItem-root,
.MuiListItemText-root {
  font-family: 'DejaVu Sans Mono', 'Courier New', Courier, monospace !important;
}

code {
  font-family: var(--font-mono);
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--bg-quaternary);
}

/* Loading animations */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom chart styles with muted jewel tones */
.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
  stroke: var(--border-primary);
}

.recharts-text {
  fill: var(--text-tertiary);
  font-family: var(--font-mono);
  font-size: 0.75rem;
}

/* Chart color scheme using muted jewel tones */
.recharts-default-tooltip {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-secondary) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: 0 4px 6px var(--shadow-md) !important;
}

.recharts-tooltip-label {
  color: var(--text-primary) !important;
  font-family: var(--font-mono) !important;
}

.recharts-tooltip-item {
  color: var(--text-secondary) !important;
  font-family: var(--font-mono) !important;
}

/* Gaming-specific chart colors */
.gaming-chart-primary { stroke: var(--highlight-orange); fill: var(--highlight-orange); }
.gaming-chart-secondary { stroke: var(--accent-red); fill: var(--accent-red); }
.gaming-chart-tertiary { stroke: var(--secondary-purple); fill: var(--secondary-purple); }
.gaming-chart-accent { stroke: var(--success-color); fill: var(--success-color); }

/* Responsive design helpers */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}
