import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Chip,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Tabs,
  Tab,
  Paper,
  Badge,
  Tooltip as MuiTooltip,
} from '@mui/material';
import {
  SportsEsports as GamingIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AccountBalanceWallet as WalletIcon,
  Token as TokenIcon,
  Collections as NFTIcon,
  Refresh as RefreshIcon,
  HealthAndSafety as HealthIcon,
  Wifi as ConnectedIcon,
  WifiOff as DisconnectedIcon,
  Update as UpdateIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

import { gamingAnalyticsAPI } from '../services/api';
import GamingMetricsSummary from '../components/GamingMetricsSummary';
import GamingProtocolChart from '../components/GamingProtocolChart';
import WebSocketStatus from '../components/WebSocketStatus';
import useGamingWebSocket from '../hooks/useGamingWebSocket';

const COLORS = ['#00d4ff', '#ff6b35', '#4caf50', '#ff9800', '#9c27b0', '#f44336', '#2196f3', '#795548'];

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`gaming-analytics-tabpanel-${index}`}
      aria-labelledby={`gaming-analytics-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function MetricCard({ title, value, icon, color = 'primary', subtitle, loading = false }) {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              p: 1,
              borderRadius: 1,
              backgroundColor: `${color}.main`,
              color: 'white',
              mr: 2,
            }}
          >
            {icon}
          </Box>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>
        </Box>
        {loading ? (
          <CircularProgress size={24} />
        ) : (
          <>
            <Typography variant="h4" component="div" sx={{ mb: 1 }}>
              {typeof value === 'number' ? value.toLocaleString() : value || 'N/A'}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}

function ProtocolCard({ protocol, metrics, onRefresh, loading = false }) {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="div">
            {protocol.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Typography>
          <Button
            size="small"
            startIcon={<RefreshIcon />}
            onClick={() => onRefresh(protocol)}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <CircularProgress />
          </Box>
        ) : metrics ? (
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                Daily Users
              </Typography>
              <Typography variant="h6">
                {metrics.daily_active_users?.toLocaleString() || 'N/A'}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                Token Price
              </Typography>
              <Typography variant="h6">
                ${metrics.token_price?.toFixed(4) || 'N/A'}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                Market Cap
              </Typography>
              <Typography variant="h6">
                ${(metrics.market_cap / 1000000)?.toFixed(1) || 'N/A'}M
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                NFT Floor
              </Typography>
              <Typography variant="h6">
                {metrics.nft_floor_price?.toFixed(3) || 'N/A'} ETH
              </Typography>
            </Grid>
          </Grid>
        ) : (
          <Alert severity="warning">No data available</Alert>
        )}
      </CardContent>
    </Card>
  );
}

function GamingAnalytics() {
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedProtocol, setSelectedProtocol] = useState('');
  const [realTimeData, setRealTimeData] = useState(null);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const queryClient = useQueryClient();

  // WebSocket connection for real-time updates
  const {
    isConnected,
    connectionId,
    subscribedTopics,
    availableTopics,
    lastUpdate,
    error: wsError,
    reconnectAttempts,
    connect: connectWs,
    disconnect: disconnectWs,
    clearError: clearWsError
  } = useGamingWebSocket({
    autoConnect: true,
    topics: ['gaming_analytics', 'market_summary'],
    onDataUpdate: (data, topic) => {
      console.log('🎮 Real-time data received:', topic, data);
      setRealTimeData(data);
      setLastUpdateTime(new Date());

      // Invalidate queries to refresh data when real-time updates arrive
      if (topic === 'gaming_analytics') {
        queryClient.invalidateQueries('gaming-summary');
        queryClient.invalidateQueries('gaming-dashboard-data');
      }
    },
    onConnectionChange: (status) => {
      console.log('🔌 WebSocket connection status changed:', status);
    }
  });

  // Fetch supported protocols
  const { data: protocols, isLoading: protocolsLoading, error: protocolsError } = useQuery(
    'gaming-protocols',
    gamingAnalyticsAPI.getProtocols,
    {
      refetchInterval: 60000, // Refetch every minute
      onError: (error) => {
        console.error('🚨 Gaming protocols API error:', error);
      },
      onSuccess: (data) => {
        console.log('✅ Gaming protocols loaded:', data);
      }
    }
  );

  // Fetch gaming analytics summary
  const { data: summary, isLoading: summaryLoading, error: summaryError } = useQuery(
    'gaming-summary',
    gamingAnalyticsAPI.getSummary,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
      onError: (error) => {
        console.error('🚨 Gaming summary API error:', error);
      },
      onSuccess: (data) => {
        console.log('✅ Gaming summary loaded:', data);
      }
    }
  );

  // Fetch dashboard data
  const { data: dashboardData, isLoading: dashboardLoading, error: dashboardError } = useQuery(
    'gaming-dashboard-data',
    gamingAnalyticsAPI.getDashboardData,
    {
      refetchInterval: 30000,
      onError: (error) => {
        console.error('🚨 Gaming dashboard API error:', error);
      },
      onSuccess: (data) => {
        console.log('✅ Gaming dashboard data loaded:', data);
      }
    }
  );

  // Fetch health status
  const { data: health, isLoading: healthLoading } = useQuery(
    'gaming-health',
    gamingAnalyticsAPI.getHealth,
    {
      refetchInterval: 10000, // Refetch every 10 seconds
    }
  );

  // Refresh protocol mutation
  const refreshProtocolMutation = useMutation(
    gamingAnalyticsAPI.refreshProtocol,
    {
      onSuccess: () => {
        queryClient.invalidateQueries('gaming-summary');
        queryClient.invalidateQueries('gaming-dashboard-data');
      },
    }
  );

  // Refresh all protocols mutation
  const refreshAllMutation = useMutation(
    gamingAnalyticsAPI.refreshAll,
    {
      onSuccess: () => {
        queryClient.invalidateQueries('gaming-summary');
        queryClient.invalidateQueries('gaming-dashboard-data');
      },
    }
  );

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const handleProtocolRefresh = (protocol) => {
    refreshProtocolMutation.mutate(protocol);
  };

  const handleRefreshAll = () => {
    refreshAllMutation.mutate();
  };

  if (protocolsLoading || summaryLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show errors if any
  if (protocolsError || summaryError || dashboardError) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Gaming Protocol Analytics
        </Typography>
        {protocolsError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Protocols API Error: {protocolsError.message}
          </Alert>
        )}
        {summaryError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Summary API Error: {summaryError.message}
          </Alert>
        )}
        {dashboardError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Dashboard API Error: {dashboardError.message}
          </Alert>
        )}
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Gaming Protocol Analytics
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          {/* WebSocket Connection Status */}
          <MuiTooltip title={`WebSocket ${isConnected ? 'Connected' : 'Disconnected'} - ${subscribedTopics.length} topics subscribed`}>
            <Chip
              icon={isConnected ? <ConnectedIcon /> : <DisconnectedIcon />}
              label={`Live Updates ${isConnected ? 'ON' : 'OFF'}`}
              color={isConnected ? 'success' : 'error'}
              variant={isConnected ? 'filled' : 'outlined'}
            />
          </MuiTooltip>

          {/* Last Update Indicator */}
          {lastUpdateTime && (
            <MuiTooltip title={`Last update: ${lastUpdateTime.toLocaleTimeString()}`}>
              <Badge color="primary" variant="dot">
                <Chip
                  icon={<UpdateIcon />}
                  label="Updated"
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </Badge>
            </MuiTooltip>
          )}

          {/* Health Status */}
          {health && (
            <Chip
              icon={<HealthIcon />}
              label={`${health.supported_protocols} Protocols Active`}
              color={health.status === 'healthy' ? 'success' : 'error'}
            />
          )}

          {/* Refresh Button */}
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={handleRefreshAll}
            disabled={refreshAllMutation.isLoading}
          >
            Refresh All
          </Button>
        </Box>
      </Box>

      {/* WebSocket Error Alert */}
      {wsError && (
        <Alert
          severity="warning"
          sx={{ mb: 2 }}
          action={
            <Button color="inherit" size="small" onClick={clearWsError}>
              Dismiss
            </Button>
          }
        >
          WebSocket Error: {wsError.message || 'Connection issue detected'}
        </Alert>
      )}

      {/* Real-time Data Indicator */}
      {realTimeData && (
        <Alert severity="info" sx={{ mb: 2 }}>
          🔴 Live: Real-time data updates are active. Data refreshes automatically every 5 minutes.
        </Alert>
      )}

      {/* WebSocket Status Component */}
      <WebSocketStatus
        isConnected={isConnected}
        connectionId={connectionId}
        subscribedTopics={subscribedTopics}
        availableTopics={availableTopics}
        lastUpdate={lastUpdate}
        error={wsError}
        reconnectAttempts={reconnectAttempts}
        onConnect={connectWs}
        onDisconnect={disconnectWs}
        onClearError={clearWsError}
      />

      {/* Gaming Metrics Summary */}
      <GamingMetricsSummary
        summary={summary}
        protocols={protocols}
        health={health}
        loading={summaryLoading || protocolsLoading}
        error={null}
      />

      {/* Tabs for different views */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label="gaming analytics tabs">
          <Tab label="Protocol Overview" />
          <Tab label="User Activity" />
          <Tab label="Token Metrics" />
          <Tab label="NFT Analytics" />
        </Tabs>
      </Paper>

      {/* Tab Panels */}
      <TabPanel value={selectedTab} index={0}>
        <Grid container spacing={3}>
          {protocols?.map((protocol) => (
            <Grid item xs={12} sm={6} md={4} key={protocol}>
              <ProtocolCard
                protocol={protocol}
                metrics={dashboardData?.protocols?.[protocol]}
                onRefresh={handleProtocolRefresh}
                loading={refreshProtocolMutation.isLoading && refreshProtocolMutation.variables === protocol}
              />
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={selectedTab} index={1}>
        <Typography variant="h6" gutterBottom>
          User Activity Analytics
        </Typography>
        <Alert severity="info" sx={{ mb: 2 }}>
          User activity metrics will be displayed here once data collection is optimized.
        </Alert>
      </TabPanel>

      <TabPanel value={selectedTab} index={2}>
        <Typography variant="h6" gutterBottom>
          Token Metrics
        </Typography>
        <Alert severity="info" sx={{ mb: 2 }}>
          Token price and market data will be displayed here once data collection is optimized.
        </Alert>
      </TabPanel>

      <TabPanel value={selectedTab} index={3}>
        <Typography variant="h6" gutterBottom>
          NFT Analytics
        </Typography>
        <Alert severity="info" sx={{ mb: 2 }}>
          NFT floor prices and trading data will be displayed here once data collection is optimized.
        </Alert>
      </TabPanel>
    </Box>
  );
}

export default GamingAnalytics;
