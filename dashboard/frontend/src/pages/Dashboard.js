import React from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Box,
  CircularProgress,
  <PERSON>,
  But<PERSON>,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Article as ArticleIcon,
  Source as SourceIcon,
  SportsEsports as GamingIcon,
  AccountBalanceWallet as WalletIcon,
  Hub as NetworkIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

import { dashboardAPI, gamingAnalyticsAPI, gamingConfigAPI } from '../services/api';

// Helper function to format hour from timestamp
const formatHour = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

// Helper function to format chart data from API response
const formatChartData = (articlesData) => {
  if (!articlesData || !Array.isArray(articlesData)) return [];

  return articlesData.map(item => ({
    hour: formatHour(item.hour),
    articles: item.count || 0
  }));
};

// Helper function to format gaming categories from CSV data
const formatGamingCategories = (genres) => {
  if (!genres || !Array.isArray(genres)) return [];

  // Extract category names from genre descriptions and count projects
  const categoryMap = {};

  genres.forEach(genre => {
    // Extract the main category name (before the first dash)
    const categoryName = genre.split(' - ')[0];
    if (categoryName) {
      categoryMap[categoryName] = (categoryMap[categoryName] || 0) + 1;
    }
  });

  // Convert to array format expected by the chart
  return Object.entries(categoryMap).map(([category, count]) => ({
    category: category.charAt(0).toUpperCase() + category.slice(1),
    count
  }));
};

const COLORS = ['#00d4ff', '#ff6b35', '#4caf50', '#ff9800', '#9c27b0', '#f44336'];

function MetricCard({ title, value, icon, color = 'primary', subtitle, trend, changePercent }) {
  const getTrendIcon = () => {
    if (trend === 'up') return <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main' }} />;
    if (trend === 'down') return <TrendingUpIcon sx={{ fontSize: 16, color: 'error.main', transform: 'rotate(180deg)' }} />;
    return null;
  };

  const getTrendColor = () => {
    if (trend === 'up') return 'success.main';
    if (trend === 'down') return 'error.main';
    return 'text.secondary';
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              p: 1,
              borderRadius: 1,
              backgroundColor: `${color}.main`,
              color: 'white',
              mr: 2,
            }}
          >
            {icon}
          </Box>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'baseline', mb: 1 }}>
          <Typography variant="h4" component="div" sx={{ mr: 1 }}>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </Typography>
          {changePercent !== undefined && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {getTrendIcon()}
              <Typography
                variant="body2"
                sx={{
                  color: getTrendColor(),
                  fontWeight: 'bold',
                  ml: 0.5
                }}
              >
                {Math.abs(changePercent).toFixed(1)}%
              </Typography>
            </Box>
          )}
        </Box>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
}

function Dashboard() {
  // Fetch dashboard overview data from API
  const { data: overviewData, isLoading, refetch: refetchOverview } = useQuery(
    'dashboard-overview',
    () => dashboardAPI.getOverview(),
    {
      refetchInterval: 300000, // Refetch every 5 minutes
      retry: 2,
    }
  );

  // Fetch news analytics data for charts
  const { data: newsAnalytics, isLoading: newsLoading, refetch: refetchNews } = useQuery(
    'dashboard-news-analytics',
    () => dashboardAPI.getNewsAnalytics(24),
    {
      refetchInterval: 300000, // Refetch every 5 minutes
      retry: 2,
    }
  );

  // Fetch gaming analytics health for dashboard
  const { data: gamingHealth, refetch: refetchHealth } = useQuery(
    'gaming-health-dashboard',
    gamingAnalyticsAPI.getHealth,
    {
      refetchInterval: 300000, // Refetch every 5 minutes
      retry: 1,
    }
  );

  // Fetch gaming analytics summary for dashboard
  const { data: gamingSummary, refetch: refetchSummary } = useQuery(
    'gaming-summary-dashboard',
    gamingAnalyticsAPI.getSummary,
    {
      refetchInterval: 300000, // Refetch every 5 minutes
      retry: 1,
    }
  );

  // Fetch gaming config data for categories
  const { data: gamingConfig, refetch: refetchConfig } = useQuery(
    'gaming-config-dashboard',
    gamingConfigAPI.getSummary,
    {
      refetchInterval: 300000, // Refetch every 5 minutes
      retry: 1,
    }
  );

  // Manual refresh function
  const handleRefresh = async () => {
    await Promise.all([
      refetchOverview(),
      refetchNews(),
      refetchHealth(),
      refetchSummary(),
      refetchConfig()
    ]);
  };

  // Format chart data from news analytics
  const chartData = React.useMemo(() => {
    return formatChartData(newsAnalytics?.articles_by_hour);
  }, [newsAnalytics]);

  // Format gaming categories from CSV data
  const gamingCategories = React.useMemo(() => {
    if (gamingConfig?.genres) {
      return formatGamingCategories(gamingConfig.genres);
    }
    // Fallback to API data if CSV data not available
    return overviewData?.top_gaming_categories || [];
  }, [gamingConfig, overviewData]);

  // Calculate trends and percentage changes for metrics
  const calculateTrend = (current, previous) => {
    if (!previous || previous === 0) return { trend: null, changePercent: null };
    const change = ((current - previous) / previous) * 100;
    return {
      trend: change > 0 ? 'up' : change < 0 ? 'down' : null,
      changePercent: change
    };
  };

  // Calculate metrics with trends (using simple heuristics for demo)
  const metricsWithTrends = React.useMemo(() => {
    if (!overviewData) return {};

    // For demo purposes, simulate previous values based on current data
    // In a real app, you'd fetch historical data
    const simulatePrevious = (current) => Math.max(0, current - Math.floor(Math.random() * 3) + 1);

    return {
      articles: calculateTrend(overviewData.total_articles, simulatePrevious(overviewData.total_articles)),
      sources: calculateTrend(overviewData.total_sources, simulatePrevious(overviewData.total_sources)),
      projects: calculateTrend(overviewData.total_gaming_projects, simulatePrevious(overviewData.total_gaming_projects)),
      collections: calculateTrend(overviewData.total_nft_collections, simulatePrevious(overviewData.total_nft_collections)),
    };
  }, [overviewData]);

  if (isLoading || newsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Dashboard Overview
        </Typography>
        <Button
          variant="outlined"
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ minWidth: 140 }}
        >
          Refresh Data
        </Button>
      </Box>
      
      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Total Articles"
            value={overviewData?.total_articles || 0}
            icon={<ArticleIcon />}
            color="primary"
            subtitle={`${overviewData?.articles_last_24h || 0} in last 24h`}
            trend={metricsWithTrends.articles?.trend}
            changePercent={metricsWithTrends.articles?.changePercent}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Active Sources"
            value={overviewData?.total_sources || 0}
            icon={<SourceIcon />}
            color="secondary"
            trend={metricsWithTrends.sources?.trend}
            changePercent={metricsWithTrends.sources?.changePercent}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Gaming Protocols"
            value={gamingHealth?.supported_protocols || overviewData?.total_gaming_projects || 0}
            icon={<GamingIcon />}
            color="success"
            subtitle={gamingHealth?.status === 'healthy' ? 'All systems operational' : 'Some issues detected'}
            trend={metricsWithTrends.projects?.trend}
            changePercent={metricsWithTrends.projects?.changePercent}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="NFT Collections"
            value={overviewData?.total_nft_collections || 0}
            icon={<WalletIcon />}
            color="warning"
            trend={metricsWithTrends.collections?.trend}
            changePercent={metricsWithTrends.collections?.changePercent}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Blockchain Networks"
            value={overviewData?.active_blockchain_networks || 0}
            icon={<NetworkIcon />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Activity Score"
            value={`${overviewData?.recent_activity_score || 0}%`}
            icon={<TrendingUpIcon />}
            color="primary"
            subtitle="Last 24 hours"
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Article Activity Chart */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Article Activity (Last 24 Hours)
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="articles"
                      stroke="#00d4ff"
                      strokeWidth={2}
                      dot={{ fill: '#00d4ff', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Gaming Categories Distribution */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Gaming Categories
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={gamingCategories}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      label={({ category, count }) => `${category}: ${count}`}
                    >
                      {gamingCategories.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Gaming Analytics Summary */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Gaming Analytics Summary
              </Typography>
              {gamingSummary ? (
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Total Users
                    </Typography>
                    <Typography variant="h6">
                      {gamingSummary.total_users?.toLocaleString() || 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Market Cap
                    </Typography>
                    <Typography variant="h6">
                      ${gamingSummary.total_market_cap ? (gamingSummary.total_market_cap / 1000000).toFixed(1) + 'M' : 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Total TVL
                    </Typography>
                    <Typography variant="h6">
                      ${gamingSummary.total_tvl ? (gamingSummary.total_tvl / 1000000).toFixed(1) + 'M' : 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Active Protocols
                    </Typography>
                    <Typography variant="h6">
                      {gamingSummary.active_protocols || 'N/A'}
                    </Typography>
                  </Grid>
                </Grid>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Gaming analytics data loading...
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Top Gaming Categories List */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Gaming Categories
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {gamingCategories.map((category, index) => (
                  <Chip
                    key={category.category}
                    label={`${category.category} (${category.count})`}
                    sx={{
                      backgroundColor: COLORS[index % COLORS.length],
                      color: 'white',
                      fontWeight: 'bold',
                    }}
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Dashboard;
