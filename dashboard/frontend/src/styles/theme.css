/* ColorHunt Gaming Palette for Web3 Gaming Dashboard */

:root {
  /* ColorHunt Gaming Palette */
  --primary-dark: #2A004E;        /* Deep purple - main background */
  --primary-dark-light: #3D1A61;  /* Lighter variant */
  --primary-dark-alpha: rgba(42, 0, 78, 0.8);

  --secondary-purple: #500073;     /* Medium purple - cards, sections */
  --secondary-purple-light: #6B1A96;
  --secondary-purple-dark: #3D0056;
  --secondary-purple-alpha: rgba(80, 0, 115, 0.2);

  --accent-red: #C62300;          /* Bright red - important elements */
  --accent-red-light: #E63D1C;
  --accent-red-dark: #A01D00;
  --accent-red-alpha: rgba(198, 35, 0, 0.2);

  --highlight-orange: #F14A00;    /* Bright orange - highlights, hover */
  --highlight-orange-light: #FF6B33;
  --highlight-orange-dark: #CC3D00;
  --highlight-orange-alpha: rgba(241, 74, 0, 0.2);

  /* Dark Theme Base Colors */
  --bg-primary: #2A004E;          /* Deep purple background */
  --bg-secondary: #500073;        /* Medium purple for cards */
  --bg-tertiary: #6B1A96;         /* Lighter purple for elevated elements */
  --bg-quaternary: #8B3DB8;       /* Lightest purple for interactive elements */

  /* Text Colors */
  --text-primary: #ffffff;        /* Pure white for primary text */
  --text-secondary: #e2e8f0;      /* Light gray for secondary text */
  --text-tertiary: #cbd5e0;       /* Medium gray for tertiary text */
  --text-muted: #a0aec0;          /* Muted gray for less important text */
  --text-disabled: #718096;       /* Disabled gray */

  /* Status Colors */
  --success-color: #10B981;       /* Green for success states */
  --warning-color: #F59E0B;       /* Yellow for warnings */
  --error-color: var(--accent-red); /* Red for errors */
  --info-color: #3B82F6;          /* Blue for info */

  /* Border Colors */
  --border-primary: var(--secondary-purple);
  --border-secondary: var(--bg-tertiary);
  --border-accent: var(--highlight-orange);

  /* Shadow Colors */
  --shadow-sm: rgba(0, 0, 0, 0.1);
  --shadow-md: rgba(0, 0, 0, 0.25);
  --shadow-lg: rgba(0, 0, 0, 0.5);

  /* Gradient Backgrounds */
  --gradient-primary: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-purple) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-purple) 0%, var(--bg-tertiary) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-red) 0%, var(--highlight-orange) 100%);
  --gradient-highlight: linear-gradient(135deg, var(--highlight-orange) 0%, var(--highlight-orange-light) 100%);

  /* Font Families */
  --font-mono: "DejaVu Sans Mono", "Fira Code", "Monaco", "Consolas", "Courier New", monospace;
  --font-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", sans-serif;

  /* Spacing Scale */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Global Dark Theme Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-sans);
  background: var(--gradient-primary);
  color: var(--text-primary);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--space-md) 0;
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin: 0 0 var(--space-md) 0;
  color: var(--text-secondary);
}

/* Code and Monospace */
code, pre, .mono {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.5;
}

code {
  background: var(--bg-tertiary);
  color: var(--highlight-orange-light);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-primary);
}

pre {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  overflow-x: auto;
  margin: var(--space-md) 0;
}

pre code {
  background: none;
  border: none;
  padding: 0;
  color: inherit;
}

/* Links */
a {
  color: var(--highlight-orange);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--highlight-orange-light);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--highlight-orange);
  outline-offset: 2px;
}

/* Selection */
::selection {
  background: var(--highlight-orange-alpha);
  color: var(--text-primary);
}

::-moz-selection {
  background: var(--highlight-orange-alpha);
  color: var(--text-primary);
}
