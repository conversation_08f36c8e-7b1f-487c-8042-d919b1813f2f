/* Muted Jewel Tone Color Palette for Web3 Gaming Dashboard */

:root {
  /* Primary Muted Jewel Tones */
  --reddish-orange: #c4704a;      /* Muted reddish-orange */
  --reddish-orange-light: #d4825a;
  --reddish-orange-dark: #a85d3a;
  --reddish-orange-alpha: rgba(196, 112, 74, 0.1);

  --emerald-green: #4a8b5c;       /* Muted emerald green */
  --emerald-green-light: #5a9b6c;
  --emerald-green-dark: #3a7b4c;
  --emerald-green-alpha: rgba(74, 139, 92, 0.1);

  --violet-magenta: #8b4a8b;      /* Muted violet-magenta */
  --violet-magenta-light: #9b5a9b;
  --violet-magenta-dark: #7b3a7b;
  --violet-magenta-alpha: rgba(139, 74, 139, 0.1);

  /* Dark Theme Base Colors */
  --bg-primary: #0a0e1a;          /* Deep dark blue-black */
  --bg-secondary: #1a1f2e;        /* Slightly lighter dark blue */
  --bg-tertiary: #2d3748;         /* Medium dark blue-gray */
  --bg-quaternary: #4a5568;       /* Lighter blue-gray */

  /* Text Colors */
  --text-primary: #f7fafc;        /* Almost white */
  --text-secondary: #e2e8f0;      /* Light gray */
  --text-tertiary: #cbd5e0;       /* Medium gray */
  --text-muted: #a0aec0;          /* Muted gray */
  --text-disabled: #718096;       /* Disabled gray */

  /* Accent Colors */
  --accent-cyan: #4fd1c7;         /* Bright cyan for highlights */
  --accent-yellow: #f6e05e;       /* Warm yellow for warnings */
  --accent-red: #fc8181;          /* Soft red for errors */
  --accent-blue: #63b3ed;         /* Soft blue for info */

  /* Border Colors */
  --border-primary: #2d3748;
  --border-secondary: #4a5568;
  --border-accent: var(--reddish-orange);

  /* Shadow Colors */
  --shadow-sm: rgba(0, 0, 0, 0.1);
  --shadow-md: rgba(0, 0, 0, 0.25);
  --shadow-lg: rgba(0, 0, 0, 0.5);

  /* Gradient Backgrounds */
  --gradient-primary: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  --gradient-accent: linear-gradient(135deg, var(--reddish-orange-alpha) 0%, var(--emerald-green-alpha) 50%, var(--violet-magenta-alpha) 100%);

  /* Font Families */
  --font-mono: "DejaVu Sans Mono", "Fira Code", "Monaco", "Consolas", "Courier New", monospace;
  --font-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", sans-serif;

  /* Spacing Scale */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Global Dark Theme Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-sans);
  background: var(--gradient-primary);
  color: var(--text-primary);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--space-md) 0;
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin: 0 0 var(--space-md) 0;
  color: var(--text-secondary);
}

/* Code and Monospace */
code, pre, .mono {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.5;
}

code {
  background: var(--bg-tertiary);
  color: var(--emerald-green-light);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-primary);
}

pre {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  overflow-x: auto;
  margin: var(--space-md) 0;
}

pre code {
  background: none;
  border: none;
  padding: 0;
  color: inherit;
}

/* Links */
a {
  color: var(--reddish-orange-light);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--reddish-orange);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--reddish-orange);
  outline-offset: 2px;
}

/* Selection */
::selection {
  background: var(--reddish-orange-alpha);
  color: var(--text-primary);
}

::-moz-selection {
  background: var(--reddish-orange-alpha);
  color: var(--text-primary);
}
