/* Component-specific styles using muted jewel tone palette */

/* Gaming Dashboard Cards */
.gaming-card {
  background: var(--gradient-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: all var(--transition-normal);
  box-shadow: 0 2px 4px var(--shadow-sm);
}

.gaming-card:hover {
  border-color: var(--reddish-orange);
  box-shadow: 0 4px 12px var(--reddish-orange-alpha);
  transform: translateY(-2px);
}

.gaming-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-sm);
  border-bottom: 1px solid var(--border-primary);
}

.gaming-card-title {
  font-family: var(--font-mono);
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.gaming-card-subtitle {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  color: var(--text-tertiary);
  margin: var(--space-xs) 0 0 0;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-family: var(--font-mono);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-live {
  background: var(--emerald-green-alpha);
  color: var(--emerald-green-light);
  border: 1px solid var(--emerald-green);
}

.status-beta {
  background: var(--violet-magenta-alpha);
  color: var(--violet-magenta-light);
  border: 1px solid var(--violet-magenta);
}

.status-development {
  background: var(--reddish-orange-alpha);
  color: var(--reddish-orange-light);
  border: 1px solid var(--reddish-orange);
}

.status-inactive {
  background: rgba(113, 128, 150, 0.1);
  color: var(--text-disabled);
  border: 1px solid var(--text-disabled);
}

/* Blockchain Network Tags */
.blockchain-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-family: var(--font-mono);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.blockchain-solana {
  background: linear-gradient(135deg, #9945ff 0%, #14f195 100%);
  color: #ffffff;
}

.blockchain-ethereum {
  background: linear-gradient(135deg, #627eea 0%, #8a92b2 100%);
  color: #ffffff;
}

.blockchain-polygon {
  background: linear-gradient(135deg, #8247e5 0%, #c44cff 100%);
  color: #ffffff;
}

.blockchain-avalanche {
  background: linear-gradient(135deg, #e84142 0%, #ff6b6b 100%);
  color: #ffffff;
}

.blockchain-bsc {
  background: linear-gradient(135deg, #f3ba2f 0%, #ffd700 100%);
  color: #000000;
}

/* Metric Display */
.metric-display {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.metric-label {
  font-family: var(--font-mono);
  font-size: 0.75rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-family: var(--font-mono);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.metric-change {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  font-weight: 500;
}

.metric-change.positive {
  color: var(--emerald-green-light);
}

.metric-change.negative {
  color: var(--accent-red);
}

.metric-change.neutral {
  color: var(--text-tertiary);
}

/* Data Tables */
.gaming-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

.gaming-table th {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: var(--space-sm) var(--space-md);
  text-align: left;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid var(--border-secondary);
}

.gaming-table td {
  padding: var(--space-sm) var(--space-md);
  border-bottom: 1px solid var(--border-primary);
  color: var(--text-secondary);
}

.gaming-table tr:hover {
  background: var(--reddish-orange-alpha);
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 2s infinite;
  border-radius: var(--radius-sm);
}

@keyframes loading-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Form Elements */
.gaming-input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-family: var(--font-mono);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.gaming-input:focus {
  outline: none;
  border-color: var(--reddish-orange);
  box-shadow: 0 0 0 3px var(--reddish-orange-alpha);
}

.gaming-input::placeholder {
  color: var(--text-muted);
  font-style: italic;
}

/* Buttons */
.gaming-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  background: var(--gradient-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-family: var(--font-mono);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.gaming-button:hover {
  border-color: var(--reddish-orange);
  box-shadow: 0 2px 8px var(--reddish-orange-alpha);
  transform: translateY(-1px);
}

.gaming-button.primary {
  background: linear-gradient(135deg, var(--reddish-orange) 0%, var(--reddish-orange-light) 100%);
  border-color: var(--reddish-orange);
}

.gaming-button.secondary {
  background: linear-gradient(135deg, var(--emerald-green) 0%, var(--emerald-green-light) 100%);
  border-color: var(--emerald-green);
}

.gaming-button.tertiary {
  background: linear-gradient(135deg, var(--violet-magenta) 0%, var(--violet-magenta-light) 100%);
  border-color: var(--violet-magenta);
}

/* Notifications */
.notification {
  display: flex;
  align-items: flex-start;
  gap: var(--space-sm);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  border-left: 4px solid;
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

.notification.info {
  background: rgba(99, 179, 237, 0.1);
  border-left-color: var(--accent-blue);
  color: var(--accent-blue);
}

.notification.success {
  background: var(--emerald-green-alpha);
  border-left-color: var(--emerald-green);
  color: var(--emerald-green-light);
}

.notification.warning {
  background: rgba(246, 224, 94, 0.1);
  border-left-color: var(--accent-yellow);
  color: var(--accent-yellow);
}

.notification.error {
  background: rgba(252, 129, 129, 0.1);
  border-left-color: var(--accent-red);
  color: var(--accent-red);
}
