/* Component-specific styles using muted jewel tone palette */

/* Enhanced Analytics Dashboard Styles */
.enhanced-analytics-dashboard {
  padding: 20px;
  background: linear-gradient(180deg, #2A004E 0%, #000000 100%);
  min-height: 100vh;
  color: #ffffff;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #500073;
}

.dashboard-header h2 {
  color: #F14A00;
  font-size: 2rem;
  margin: 0;
}

.protocol-selector {
  display: flex;
  gap: 15px;
  align-items: center;
}

.protocol-select {
  background: #500073;
  color: #ffffff;
  border: 2px solid #C62300;
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 1rem;
  min-width: 200px;
}

.protocol-select:focus {
  outline: none;
  border-color: #F14A00;
  box-shadow: 0 0 10px rgba(241, 74, 0, 0.3);
}

.refresh-button {
  background: linear-gradient(135deg, #C62300, #F14A00);
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-button:hover {
  background: linear-gradient(135deg, #F14A00, #C62300);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(241, 74, 0, 0.4);
}

.analytics-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 30px;
  background: rgba(80, 0, 115, 0.3);
  border-radius: 12px;
  padding: 5px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
}

.tab-button:hover {
  background: rgba(198, 35, 0, 0.3);
}

.tab-button.active {
  background: linear-gradient(135deg, #C62300, #F14A00);
  color: #ffffff;
  box-shadow: 0 2px 10px rgba(241, 74, 0, 0.3);
}

.tab-icon {
  font-size: 1.2rem;
}

.analytics-content {
  background: rgba(80, 0, 115, 0.2);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: linear-gradient(135deg, rgba(80, 0, 115, 0.6), rgba(42, 0, 78, 0.8));
  border: 2px solid #500073;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.metric-card:hover {
  border-color: #C62300;
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(198, 35, 0, 0.3);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.metric-icon {
  font-size: 1.5rem;
}

.metric-header h3 {
  color: #F14A00;
  font-size: 1.1rem;
  margin: 0;
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 5px;
}

.metric-label {
  color: #cccccc;
  font-size: 0.9rem;
}

.metric-change {
  font-size: 1rem;
  font-weight: bold;
  margin-top: 5px;
}

.token-breakdown, .user-segmentation, .earning-mechanics, .token-economics, .nft-grid {
  margin-top: 30px;
}

.token-breakdown h4, .user-segmentation h4, .earning-mechanics h4, .token-economics h4 {
  color: #F14A00;
  font-size: 1.3rem;
  margin-bottom: 20px;
  border-bottom: 2px solid #500073;
  padding-bottom: 10px;
}

.token-list, .segment-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.token-item, .segment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(80, 0, 115, 0.4);
  border: 1px solid #500073;
  border-radius: 8px;
  padding: 15px;
}

.token-symbol, .segment-name {
  color: #F14A00;
  font-weight: bold;
}

.token-amount, .segment-count {
  color: #ffffff;
  font-weight: bold;
}

.token-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.token-card, .nft-card {
  background: linear-gradient(135deg, rgba(80, 0, 115, 0.6), rgba(42, 0, 78, 0.8));
  border: 2px solid #500073;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.token-card:hover, .nft-card:hover {
  border-color: #C62300;
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(198, 35, 0, 0.3);
}

.token-card h5, .nft-card h5 {
  color: #F14A00;
  font-size: 1.2rem;
  margin: 0 0 15px 0;
  text-align: center;
}

.token-metrics, .nft-metrics {
  display: grid;
  gap: 10px;
}

.token-metric, .nft-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(80, 0, 115, 0.5);
}

.token-metric:last-child, .nft-metric:last-child {
  border-bottom: none;
}

.token-metric .label, .nft-metric .label {
  color: #cccccc;
  font-size: 0.9rem;
}

.token-metric .value, .nft-metric .value {
  color: #ffffff;
  font-weight: bold;
}

.nft-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.analytics-footer {
  text-align: center;
  padding: 20px;
  color: #cccccc;
  font-size: 0.9rem;
  border-top: 1px solid #500073;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 20px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(241, 74, 0, 0.3);
  border-top: 4px solid #F14A00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 20px;
  color: #ff0044;
}

.retry-button {
  background: linear-gradient(135deg, #C62300, #F14A00);
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: linear-gradient(135deg, #F14A00, #C62300);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(241, 74, 0, 0.4);
}

/* Gaming Dashboard Cards */
.gaming-card {
  background: var(--gradient-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: all var(--transition-normal);
  box-shadow: 0 2px 4px var(--shadow-sm);
}

.gaming-card:hover {
  border-color: var(--highlight-orange);
  box-shadow: 0 4px 12px var(--highlight-orange-alpha);
  transform: translateY(-2px);
}

.gaming-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-sm);
  border-bottom: 1px solid var(--border-primary);
}

.gaming-card-title {
  font-family: var(--font-mono);
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.gaming-card-subtitle {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  color: var(--text-tertiary);
  margin: var(--space-xs) 0 0 0;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-family: var(--font-mono);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-live {
  background: var(--success-color);
  color: #ffffff;
  border: 1px solid var(--success-color);
}

.status-beta {
  background: var(--secondary-purple-alpha);
  color: var(--bg-tertiary);
  border: 1px solid var(--secondary-purple);
}

.status-development {
  background: var(--highlight-orange-alpha);
  color: var(--highlight-orange-light);
  border: 1px solid var(--highlight-orange);
}

.status-inactive {
  background: rgba(113, 128, 150, 0.1);
  color: var(--text-disabled);
  border: 1px solid var(--text-disabled);
}

/* Blockchain Network Tags */
.blockchain-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-family: var(--font-mono);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.blockchain-solana {
  background: linear-gradient(135deg, #9945ff 0%, #14f195 100%);
  color: #ffffff;
}

.blockchain-ethereum {
  background: linear-gradient(135deg, #627eea 0%, #8a92b2 100%);
  color: #ffffff;
}

.blockchain-polygon {
  background: linear-gradient(135deg, #8247e5 0%, #c44cff 100%);
  color: #ffffff;
}

.blockchain-avalanche {
  background: linear-gradient(135deg, #e84142 0%, #ff6b6b 100%);
  color: #ffffff;
}

.blockchain-bsc {
  background: linear-gradient(135deg, #f3ba2f 0%, #ffd700 100%);
  color: #000000;
}

/* Metric Display */
.metric-display {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.metric-label {
  font-family: var(--font-mono);
  font-size: 0.75rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-family: var(--font-mono);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.metric-change {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  font-weight: 500;
}

.metric-change.positive {
  color: var(--success-color);
}

.metric-change.negative {
  color: var(--accent-red);
}

.metric-change.neutral {
  color: var(--text-tertiary);
}

/* Data Tables */
.gaming-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

.gaming-table th {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: var(--space-sm) var(--space-md);
  text-align: left;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid var(--border-secondary);
}

.gaming-table td {
  padding: var(--space-sm) var(--space-md);
  border-bottom: 1px solid var(--border-primary);
  color: var(--text-secondary);
}

.gaming-table tr:hover {
  background: var(--highlight-orange-alpha);
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 2s infinite;
  border-radius: var(--radius-sm);
}

@keyframes loading-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Form Elements */
.gaming-input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-family: var(--font-mono);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.gaming-input:focus {
  outline: none;
  border-color: var(--highlight-orange);
  box-shadow: 0 0 0 3px var(--highlight-orange-alpha);
}

.gaming-input::placeholder {
  color: var(--text-muted);
  font-style: italic;
}

/* Buttons */
.gaming-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  background: var(--gradient-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-family: var(--font-mono);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.gaming-button:hover {
  border-color: var(--highlight-orange);
  box-shadow: 0 2px 8px var(--highlight-orange-alpha);
  transform: translateY(-1px);
}

.gaming-button.primary {
  background: var(--gradient-highlight);
  border-color: var(--highlight-orange);
}

.gaming-button.secondary {
  background: linear-gradient(135deg, var(--accent-red) 0%, var(--accent-red-light) 100%);
  border-color: var(--accent-red);
}

.gaming-button.tertiary {
  background: linear-gradient(135deg, var(--secondary-purple) 0%, var(--bg-tertiary) 100%);
  border-color: var(--secondary-purple);
}

/* Notifications */
.notification {
  display: flex;
  align-items: flex-start;
  gap: var(--space-sm);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  border-left: 4px solid;
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

.notification.info {
  background: rgba(59, 130, 246, 0.1);
  border-left-color: var(--info-color);
  color: var(--info-color);
}

.notification.success {
  background: rgba(16, 185, 129, 0.1);
  border-left-color: var(--success-color);
  color: var(--success-color);
}

.notification.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: var(--warning-color);
  color: var(--warning-color);
}

.notification.error {
  background: var(--accent-red-alpha);
  border-left-color: var(--accent-red);
  color: var(--accent-red);
}
