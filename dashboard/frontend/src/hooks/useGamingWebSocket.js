import { useState, useEffect, useCallback, useRef } from 'react';
import { gamingWsService } from '../services/api';

/**
 * Custom hook for managing gaming WebSocket connections and real-time data updates
 * @param {Object} options - Configuration options
 * @param {boolean} options.autoConnect - Whether to auto-connect on mount (default: true)
 * @param {Array} options.topics - Topics to subscribe to (default: ['gaming_analytics', 'market_summary'])
 * @param {Function} options.onDataUpdate - Callback for data updates
 * @param {Function} options.onConnectionChange - Callback for connection status changes
 * @returns {Object} WebSocket state and control functions
 */
export function useGamingWebSocket(options = {}) {
  const {
    autoConnect = true,
    topics = ['gaming_analytics', 'market_summary'],
    onDataUpdate,
    onConnectionChange
  } = options;

  const [connectionStatus, setConnectionStatus] = useState({
    connected: false,
    connectionId: null,
    subscribedTopics: [],
    reconnectAttempts: 0
  });

  const [lastUpdate, setLastUpdate] = useState(null);
  const [error, setError] = useState(null);
  const [availableTopics, setAvailableTopics] = useState([]);

  // Use refs to store callbacks to avoid re-creating listeners
  const onDataUpdateRef = useRef(onDataUpdate);
  const onConnectionChangeRef = useRef(onConnectionChange);

  useEffect(() => {
    onDataUpdateRef.current = onDataUpdate;
    onConnectionChangeRef.current = onConnectionChange;
  }, [onDataUpdate, onConnectionChange]);

  // Connection status listener
  const handleConnectionStatus = useCallback((status) => {
    setConnectionStatus(prev => ({ ...prev, ...status }));
    if (onConnectionChangeRef.current) {
      onConnectionChangeRef.current(status);
    }
  }, []);

  // Connection established listener
  const handleConnectionEstablished = useCallback((data) => {
    console.log('🎮 Gaming WebSocket connection established:', data);
    setConnectionStatus(prev => ({
      ...prev,
      connected: true,
      connectionId: data.connection_id
    }));
    setAvailableTopics(data.available_topics || []);
    setError(null);
  }, []);

  // Data update listener
  const handleDataUpdate = useCallback((data) => {
    console.log('📊 Gaming data update received:', data);
    setLastUpdate({
      timestamp: new Date(),
      data: data
    });
    if (onDataUpdateRef.current) {
      onDataUpdateRef.current(data);
    }
  }, []);

  // Gaming analytics specific listener
  const handleGamingAnalytics = useCallback((data) => {
    console.log('🎮 Gaming analytics update:', data);
    setLastUpdate({
      timestamp: new Date(),
      topic: 'gaming_analytics',
      data: data
    });
    if (onDataUpdateRef.current) {
      onDataUpdateRef.current(data, 'gaming_analytics');
    }
  }, []);

  // Market summary specific listener
  const handleMarketSummary = useCallback((data) => {
    console.log('💰 Market summary update:', data);
    setLastUpdate({
      timestamp: new Date(),
      topic: 'market_summary',
      data: data
    });
    if (onDataUpdateRef.current) {
      onDataUpdateRef.current(data, 'market_summary');
    }
  }, []);

  // Error listener
  const handleError = useCallback((error) => {
    console.error('❌ Gaming WebSocket error:', error);
    setError(error);
  }, []);

  // Subscription confirmed listener
  const handleSubscriptionConfirmed = useCallback((data) => {
    console.log('✅ Subscription confirmed:', data.topic);
    setConnectionStatus(prev => ({
      ...prev,
      subscribedTopics: [...new Set([...prev.subscribedTopics, data.topic])]
    }));
  }, []);

  // Connect function
  const connect = useCallback(() => {
    console.log('🔌 Connecting to gaming WebSocket...');
    gamingWsService.connect();
  }, []);

  // Disconnect function
  const disconnect = useCallback(() => {
    console.log('🔌 Disconnecting from gaming WebSocket...');
    gamingWsService.disconnect();
    setConnectionStatus({
      connected: false,
      connectionId: null,
      subscribedTopics: [],
      reconnectAttempts: 0
    });
  }, []);

  // Subscribe to topic function
  const subscribeToTopic = useCallback((topic) => {
    console.log('📡 Subscribing to topic:', topic);
    gamingWsService.subscribeToTopic(topic);
  }, []);

  // Unsubscribe from topic function
  const unsubscribeFromTopic = useCallback((topic) => {
    console.log('📡 Unsubscribing from topic:', topic);
    gamingWsService.unsubscribeFromTopic(topic);
  }, []);

  // Setup listeners and auto-connect
  useEffect(() => {
    // Subscribe to WebSocket events
    gamingWsService.subscribe('connection_status', handleConnectionStatus);
    gamingWsService.subscribe('connection_established', handleConnectionEstablished);
    gamingWsService.subscribe('data_update', handleDataUpdate);
    gamingWsService.subscribe('gaming_analytics', handleGamingAnalytics);
    gamingWsService.subscribe('market_summary', handleMarketSummary);
    gamingWsService.subscribe('error', handleError);
    gamingWsService.subscribe('subscription_confirmed', handleSubscriptionConfirmed);

    // Auto-connect if enabled
    if (autoConnect) {
      connect();
    }

    // Cleanup function
    return () => {
      gamingWsService.unsubscribe('connection_status', handleConnectionStatus);
      gamingWsService.unsubscribe('connection_established', handleConnectionEstablished);
      gamingWsService.unsubscribe('data_update', handleDataUpdate);
      gamingWsService.unsubscribe('gaming_analytics', handleGamingAnalytics);
      gamingWsService.unsubscribe('market_summary', handleMarketSummary);
      gamingWsService.unsubscribe('error', handleError);
      gamingWsService.unsubscribe('subscription_confirmed', handleSubscriptionConfirmed);
    };
  }, [
    autoConnect,
    connect,
    handleConnectionStatus,
    handleConnectionEstablished,
    handleDataUpdate,
    handleGamingAnalytics,
    handleMarketSummary,
    handleError,
    handleSubscriptionConfirmed
  ]);

  // Subscribe to initial topics when connected
  useEffect(() => {
    if (connectionStatus.connected && topics.length > 0) {
      topics.forEach(topic => {
        if (!connectionStatus.subscribedTopics.includes(topic)) {
          subscribeToTopic(topic);
        }
      });
    }
  }, [connectionStatus.connected, topics, connectionStatus.subscribedTopics, subscribeToTopic]);

  return {
    // Connection state
    isConnected: connectionStatus.connected,
    connectionId: connectionStatus.connectionId,
    subscribedTopics: connectionStatus.subscribedTopics,
    reconnectAttempts: connectionStatus.reconnectAttempts,
    availableTopics,
    
    // Data state
    lastUpdate,
    error,
    
    // Control functions
    connect,
    disconnect,
    subscribeToTopic,
    unsubscribeFromTopic,
    
    // Utility functions
    getConnectionStatus: () => gamingWsService.getConnectionStatus(),
    clearError: () => setError(null)
  };
}

export default useGamingWebSocket;
