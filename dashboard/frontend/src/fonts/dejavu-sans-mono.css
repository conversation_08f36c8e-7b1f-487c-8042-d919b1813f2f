/* DejaVu Sans Mono Web Font - CDN Version */
@import url('https://cdn.jsdelivr.net/gh/maxwell-k/dejavu-sans-mono-web-font/index.css');

/* Fallback font definitions */
@font-face {
  font-family: "DejaVu Sans Mono";
  font-style: normal;
  font-weight: normal;
  src: local("DejaVu Sans Mono"), local("DejaVuSansMono"),
       url("https://cdn.jsdelivr.net/gh/maxwell-k/dejavu-sans-mono-web-font/DejaVuSansMono.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "DejaVu Sans Mono";
  font-style: normal;
  font-weight: bold;
  src: local("DejaVu Sans Mono Bold"), local("DejaVuSansMono-Bold"),
       url("https://cdn.jsdelivr.net/gh/maxwell-k/dejavu-sans-mono-web-font/DejaVuSansMono-Bold.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "DejaVu Sans Mono";
  font-style: oblique;
  font-weight: bold;
  src: local("DejaVu Sans Mono Bold Oblique"), local("DejaVuSansMono-BoldOblique"),
       url("https://cdn.jsdelivr.net/gh/maxwell-k/dejavu-sans-mono-web-font/DejaVuSansMono-BoldOblique.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "DejaVu Sans Mono";
  font-style: oblique;
  font-weight: normal;
  src: local("DejaVu Sans Mono Oblique"), local("DejaVuSansMono-Oblique"),
       url("https://cdn.jsdelivr.net/gh/maxwell-k/dejavu-sans-mono-web-font/DejaVuSansMono-Oblique.woff") format("woff");
  font-display: swap;
}
