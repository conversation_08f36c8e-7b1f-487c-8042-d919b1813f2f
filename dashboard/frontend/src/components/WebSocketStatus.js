import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  IconButton,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Button,
  Alert,
} from '@mui/material';
import {
  Wifi as ConnectedIcon,
  WifiOff as DisconnectedIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Topic as TopicIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
} from '@mui/icons-material';

function WebSocketStatus({ 
  isConnected, 
  connectionId, 
  subscribedTopics = [], 
  availableTopics = [],
  lastUpdate,
  error,
  onConnect,
  onDisconnect,
  onClearError,
  reconnectAttempts = 0
}) {
  const [expanded, setExpanded] = useState(false);

  const handleToggleExpanded = () => {
    setExpanded(!expanded);
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Never';
    return new Date(timestamp).toLocaleTimeString();
  };

  const getStatusColor = () => {
    if (error) return 'error';
    if (isConnected) return 'success';
    return 'default';
  };

  const getStatusIcon = () => {
    if (error) return <ErrorIcon />;
    if (isConnected) return <ConnectedIcon />;
    return <DisconnectedIcon />;
  };

  const getStatusText = () => {
    if (error) return 'Error';
    if (isConnected) return 'Connected';
    return 'Disconnected';
  };

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Chip
              icon={getStatusIcon()}
              label={`WebSocket ${getStatusText()}`}
              color={getStatusColor()}
              variant={isConnected ? 'filled' : 'outlined'}
            />
            
            {subscribedTopics.length > 0 && (
              <Chip
                icon={<TopicIcon />}
                label={`${subscribedTopics.length} Topics`}
                size="small"
                variant="outlined"
              />
            )}

            {lastUpdate && (
              <Chip
                icon={<SuccessIcon />}
                label={`Updated ${formatTimestamp(lastUpdate.timestamp)}`}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}

            {reconnectAttempts > 0 && (
              <Chip
                label={`${reconnectAttempts} Reconnect Attempts`}
                size="small"
                color="warning"
                variant="outlined"
              />
            )}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {!isConnected && (
              <Button
                size="small"
                startIcon={<RefreshIcon />}
                onClick={onConnect}
                variant="outlined"
              >
                Connect
              </Button>
            )}
            
            {isConnected && (
              <Button
                size="small"
                onClick={onDisconnect}
                variant="outlined"
                color="error"
              >
                Disconnect
              </Button>
            )}

            <IconButton
              onClick={handleToggleExpanded}
              size="small"
              aria-label="expand websocket details"
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert 
            severity="error" 
            sx={{ mt: 2 }}
            action={
              onClearError && (
                <Button color="inherit" size="small" onClick={onClearError}>
                  Clear
                </Button>
              )
            }
          >
            {error.message || 'WebSocket connection error'}
          </Alert>
        )}

        {/* Expanded Details */}
        <Collapse in={expanded}>
          <Box sx={{ mt: 2 }}>
            <Divider sx={{ mb: 2 }} />
            
            {/* Connection Details */}
            <Typography variant="subtitle2" gutterBottom>
              Connection Details
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <InfoIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Connection ID"
                  secondary={connectionId || 'Not connected'}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <InfoIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Status"
                  secondary={isConnected ? 'Connected and active' : 'Disconnected'}
                />
              </ListItem>
              {lastUpdate && (
                <ListItem>
                  <ListItemIcon>
                    <SuccessIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Last Update"
                    secondary={`${formatTimestamp(lastUpdate.timestamp)} - Topic: ${lastUpdate.topic || 'Unknown'}`}
                  />
                </ListItem>
              )}
            </List>

            {/* Subscribed Topics */}
            {subscribedTopics.length > 0 && (
              <>
                <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                  Subscribed Topics ({subscribedTopics.length})
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {subscribedTopics.map((topic) => (
                    <Chip
                      key={topic}
                      label={topic}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </>
            )}

            {/* Available Topics */}
            {availableTopics.length > 0 && (
              <>
                <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                  Available Topics ({availableTopics.length})
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {availableTopics.map((topic) => (
                    <Chip
                      key={topic}
                      label={topic}
                      size="small"
                      variant="outlined"
                      color={subscribedTopics.includes(topic) ? 'success' : 'default'}
                    />
                  ))}
                </Box>
              </>
            )}

            {/* Real-time Data Preview */}
            {lastUpdate && lastUpdate.data && (
              <>
                <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                  Latest Data Preview
                </Typography>
                <Box
                  sx={{
                    backgroundColor: 'background.paper',
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    p: 1,
                    maxHeight: 200,
                    overflow: 'auto',
                    fontFamily: 'monospace',
                    fontSize: '0.75rem',
                  }}
                >
                  <pre>{JSON.stringify(lastUpdate.data, null, 2)}</pre>
                </Box>
              </>
            )}
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
}

export default WebSocketStatus;
