import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  CardHeader,
  Ty<PERSON>graphy,
  TextField,
  Button,
  Stepper,
  Step,
  StepLabel,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  RadioGroup,
  Radio,
  FormLabel,
  Alert,
  CircularProgress,
  Chip,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Save as SaveIcon,
  Check as CheckIcon
} from '@mui/icons-material';

const AddGameForm = ({ onSuccess, onCancel }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    project_name: '',
    project_website_link: '',
    whitepaper_link: '',
    blockchain: '',
    validated_game_status: '',
    game_status_notes: '',
    token_schedule_link: '',
    token_1_symbol: '',
    token_1_contract_address: '',
    token_1_type: '',
    token_1_coingecko_link: '',
    token_2_symbol: '',
    token_2_contract_address: '',
    token_2_type: '',
    token_2_coingecko_link: '',
    genre: '',
    involves_nfts: '',
    nft_marketplace_link: '',
    game_style: '',
    twitter_link: '',
    discord_link: '',
    telegram_link: '',
    medium_link: '',
    youtube_link: '',
    linkedin_link: '',
    facebook_page: '',
    reddit_link: '',
    token_1_scanner_link: '',
    token_2_scanner_link: '',
    nft_scanner_link: '',
    nft_function: '',
    nft_marketplace_links: '',
    nft_contract_address: '',
    includes_nfts: '',
    nft_name: '',
    nft_holders_count: '',
    nft_holders_source: '',
    daily_active_users: '',
    dau_source: '',
    daily_unique_wallets: '',
    uaw_source: '',
    autoclicker_telegram_invite: '',
    autoclicker_telegram_channel: '',
    autoclicker_telegram_bot: '',
    autoclicker_membership_population: '',
    autoclicker_twitter: '',
    autoclicker_discord: '',
    autoclicker_medium: '',
    autoclicker_youtube: '',
    autoclicker_has_token: '',
    notes_and_comments: '',
    additional_notes: '',
    additional_tokens: '',
    token_1_total_supply: '',
    token_2_total_supply: '',
    developer: '',
    platform: '',
    notes_additional_tokens: ''
  });

  const [loading, setLoading] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [validationErrors, setValidationErrors] = useState({});
  const [projectNameExists, setProjectNameExists] = useState(false);

  const steps = [
    'Basic Information',
    'Blockchain & Tokens',
    'Game Details',
    'Social Media',
    'NFTs & Metrics',
    'Additional Info'
  ];

  const blockchainOptions = [
    'Ethereum', 'Solana', 'Polygon', 'BSC', 'Avalanche', 'Base', 'TON', 'Ronin', 'Arbitrum', 'Optimism'
  ];

  const gameStatusOptions = [
    'Live', 'Beta - Closer to its final form', 'Alpha', 'Development', 'Announced', 'Discontinued'
  ];

  const genreOptions = [
    'Action', 'Action Adventure', 'Action RPG', 'Adventure', 'Battle Royale', 'Casual',
    'Fighting', 'FPS', 'MMORPG', 'Party', 'Platformer', 'Puzzle', 'Racing', 'RTS',
    'RPG', 'Shooter', 'Simulation', 'Sports', 'Stealth', 'Strategy', 'Survival', 'Tactical RPG'
  ];

  const gameStyleOptions = [
    'Free to Play', 'Play to Earn', 'Pay to Play', 'Freemium', 'Subscription'
  ];

  const tokenTypeOptions = [
    'Utility Token', 'Governance Token', 'In-Game Currency', 'NFT', 'Stablecoin'
  ];

  // Validation function for project name
  const validateProjectName = async (projectName) => {
    if (!projectName.trim()) return;
    
    try {
      const response = await fetch(`/api/v1/gaming-form/validate-project/${encodeURIComponent(projectName)}`);
      const data = await response.json();
      
      if (data.exists) {
        setProjectNameExists(true);
        setValidationErrors(prev => ({
          ...prev,
          project_name: `Project "${projectName}" already exists`
        }));
      } else {
        setProjectNameExists(false);
        setValidationErrors(prev => {
          const { project_name, ...rest } = prev;
          return rest;
        });
      }
    } catch (error) {
      console.error('Error validating project name:', error);
    }
  };

  // Handle form field changes
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const { [field]: removed, ...rest } = prev;
        return rest;
      });
    }

    // Special handling for project name validation
    if (field === 'project_name') {
      validateProjectName(value);
    }
  };

  // Validate current step
  const validateStep = (step) => {
    const errors = {};
    
    switch (step) {
      case 0: // Basic Information
        if (!formData.project_name.trim()) {
          errors.project_name = 'Project name is required';
        }
        if (projectNameExists) {
          errors.project_name = 'Project name already exists';
        }
        break;
      case 1: // Blockchain & Tokens
        if (!formData.blockchain) {
          errors.blockchain = 'Blockchain is required';
        }
        break;
      default:
        // No validation needed for other steps
        break;
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle step navigation
  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateStep(activeStep)) return;
    
    setLoading(true);
    setSubmitError('');
    
    try {
      const response = await fetch('/api/v1/gaming-form/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      setSubmitSuccess(true);
      
      // Call success callback if provided
      if (onSuccess) {
        onSuccess(data);
      }
      
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Reset form
  const handleReset = () => {
    setFormData({
      project_name: '',
      project_website_link: '',
      // ... reset all fields
    });
    setActiveStep(0);
    setSubmitSuccess(false);
    setSubmitError('');
    setValidationErrors({});
    setProjectNameExists(false);
  };

  if (submitSuccess) {
    return (
      <Card>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <CheckIcon sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
          <Typography variant="h4" gutterBottom>
            Game Added Successfully!
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            {formData.project_name} has been added to the gaming database.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
            <Button
              variant="contained"
              onClick={handleReset}
              startIcon={<AddIcon />}
            >
              Add Another Game
            </Button>
            <Button
              variant="outlined"
              onClick={onCancel}
            >
              Back to Dashboard
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Add New Gaming Project"
        subheader="Add a new Web3 gaming project to the tracking database"
        action={
          <Button
            variant="outlined"
            onClick={onCancel}
            size="small"
          >
            Cancel
          </Button>
        }
      />
      <CardContent>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {submitError && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {submitError}
          </Alert>
        )}

        {/* Step Content */}
        <Box sx={{ minHeight: 400 }}>
          {activeStep === 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Basic Project Information
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Project Name *"
                  value={formData.project_name}
                  onChange={(e) => handleChange('project_name', e.target.value)}
                  error={!!validationErrors.project_name}
                  helperText={validationErrors.project_name}
                  onBlur={() => validateProjectName(formData.project_name)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Project Website"
                  value={formData.project_website_link}
                  onChange={(e) => handleChange('project_website_link', e.target.value)}
                  placeholder="https://example.com"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Whitepaper Link"
                  value={formData.whitepaper_link}
                  onChange={(e) => handleChange('whitepaper_link', e.target.value)}
                  placeholder="https://example.com/whitepaper.pdf"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Developer/Studio"
                  value={formData.developer}
                  onChange={(e) => handleChange('developer', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Game Status</InputLabel>
                  <Select
                    value={formData.validated_game_status}
                    onChange={(e) => handleChange('validated_game_status', e.target.value)}
                    label="Game Status"
                  >
                    {gameStatusOptions.map((status) => (
                      <MenuItem key={status} value={status}>
                        {status}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Platform"
                  value={formData.platform}
                  onChange={(e) => handleChange('platform', e.target.value)}
                  placeholder="PC, Mobile, Web, Console"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Game Status Notes"
                  value={formData.game_status_notes}
                  onChange={(e) => handleChange('game_status_notes', e.target.value)}
                />
              </Grid>
            </Grid>
          )}

          {activeStep === 1 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Blockchain & Token Information
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={!!validationErrors.blockchain}>
                  <InputLabel>Blockchain *</InputLabel>
                  <Select
                    value={formData.blockchain}
                    onChange={(e) => handleChange('blockchain', e.target.value)}
                    label="Blockchain *"
                  >
                    {blockchainOptions.map((blockchain) => (
                      <MenuItem key={blockchain} value={blockchain}>
                        {blockchain}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Token Schedule Link"
                  value={formData.token_schedule_link}
                  onChange={(e) => handleChange('token_schedule_link', e.target.value)}
                />
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }}>
                  <Chip label="Primary Token" />
                </Divider>
              </Grid>

              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Token 1 Symbol"
                  value={formData.token_1_symbol}
                  onChange={(e) => handleChange('token_1_symbol', e.target.value)}
                  placeholder="e.g., AXS"
                />
              </Grid>

              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Token 1 Type</InputLabel>
                  <Select
                    value={formData.token_1_type}
                    onChange={(e) => handleChange('token_1_type', e.target.value)}
                    label="Token 1 Type"
                  >
                    {tokenTypeOptions.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Token 1 Contract Address"
                  value={formData.token_1_contract_address}
                  onChange={(e) => handleChange('token_1_contract_address', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Token 1 CoinGecko Link"
                  value={formData.token_1_coingecko_link}
                  onChange={(e) => handleChange('token_1_coingecko_link', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Token 1 Total Supply"
                  value={formData.token_1_total_supply}
                  onChange={(e) => handleChange('token_1_total_supply', e.target.value)}
                />
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }}>
                  <Chip label="Secondary Token (Optional)" />
                </Divider>
              </Grid>

              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Token 2 Symbol"
                  value={formData.token_2_symbol}
                  onChange={(e) => handleChange('token_2_symbol', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Token 2 Type</InputLabel>
                  <Select
                    value={formData.token_2_type}
                    onChange={(e) => handleChange('token_2_type', e.target.value)}
                    label="Token 2 Type"
                  >
                    {tokenTypeOptions.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Token 2 Contract Address"
                  value={formData.token_2_contract_address}
                  onChange={(e) => handleChange('token_2_contract_address', e.target.value)}
                />
              </Grid>
            </Grid>
          )}

          {activeStep === 2 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Game Details & Classification
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Primary Genre</InputLabel>
                  <Select
                    value={formData.genre}
                    onChange={(e) => handleChange('genre', e.target.value)}
                    label="Primary Genre"
                  >
                    {genreOptions.map((genre) => (
                      <MenuItem key={genre} value={genre}>
                        {genre}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Game Style</InputLabel>
                  <Select
                    value={formData.game_style}
                    onChange={(e) => handleChange('game_style', e.target.value)}
                    label="Game Style"
                  >
                    {gameStyleOptions.map((style) => (
                      <MenuItem key={style} value={style}>
                        {style}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl component="fieldset">
                  <FormLabel component="legend">Does this project involve NFTs?</FormLabel>
                  <RadioGroup
                    value={formData.involves_nfts}
                    onChange={(e) => handleChange('involves_nfts', e.target.value)}
                    row
                  >
                    <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
                    <FormControlLabel value="No" control={<Radio />} label="No" />
                  </RadioGroup>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Daily Active Users (DAU)"
                  value={formData.daily_active_users}
                  onChange={(e) => handleChange('daily_active_users', e.target.value)}
                  placeholder="e.g., 10000"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Daily Unique Active Wallets (UAW)"
                  value={formData.daily_unique_wallets}
                  onChange={(e) => handleChange('daily_unique_wallets', e.target.value)}
                  placeholder="e.g., 5000"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="DAU Source Link"
                  value={formData.dau_source}
                  onChange={(e) => handleChange('dau_source', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="UAW Source Link"
                  value={formData.uaw_source}
                  onChange={(e) => handleChange('uaw_source', e.target.value)}
                />
              </Grid>
            </Grid>
          )}

          {activeStep === 3 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Social Media & Community Links
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Twitter (X) Link"
                  value={formData.twitter_link}
                  onChange={(e) => handleChange('twitter_link', e.target.value)}
                  placeholder="https://twitter.com/project"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Discord Server Link"
                  value={formData.discord_link}
                  onChange={(e) => handleChange('discord_link', e.target.value)}
                  placeholder="https://discord.gg/project"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Telegram Channel Link"
                  value={formData.telegram_link}
                  onChange={(e) => handleChange('telegram_link', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Medium Link"
                  value={formData.medium_link}
                  onChange={(e) => handleChange('medium_link', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="YouTube Channel"
                  value={formData.youtube_link}
                  onChange={(e) => handleChange('youtube_link', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="LinkedIn"
                  value={formData.linkedin_link}
                  onChange={(e) => handleChange('linkedin_link', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Facebook Page"
                  value={formData.facebook_page}
                  onChange={(e) => handleChange('facebook_page', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Reddit - Subreddit or User"
                  value={formData.reddit_link}
                  onChange={(e) => handleChange('reddit_link', e.target.value)}
                />
              </Grid>
            </Grid>
          )}

          {activeStep === 4 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  NFTs & Blockchain Details
                </Typography>
              </Grid>

              {formData.involves_nfts === 'Yes' && (
                <>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="NFT Name"
                      value={formData.nft_name}
                      onChange={(e) => handleChange('nft_name', e.target.value)}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="NFT Contract Address"
                      value={formData.nft_contract_address}
                      onChange={(e) => handleChange('nft_contract_address', e.target.value)}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="NFT Marketplace Link"
                      value={formData.nft_marketplace_link}
                      onChange={(e) => handleChange('nft_marketplace_link', e.target.value)}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="NFT Holders Count"
                      value={formData.nft_holders_count}
                      onChange={(e) => handleChange('nft_holders_count', e.target.value)}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={2}
                      label="What are the NFTs used for?"
                      value={formData.nft_function}
                      onChange={(e) => handleChange('nft_function', e.target.value)}
                    />
                  </Grid>
                </>
              )}

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }}>
                  <Chip label="Blockchain Scanner Links" />
                </Divider>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Token 1 Blockchain Scanner Link"
                  value={formData.token_1_scanner_link}
                  onChange={(e) => handleChange('token_1_scanner_link', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Token 2 Blockchain Scanner Link"
                  value={formData.token_2_scanner_link}
                  onChange={(e) => handleChange('token_2_scanner_link', e.target.value)}
                />
              </Grid>

              {formData.involves_nfts === 'Yes' && (
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="NFT Blockchain Scanner Link"
                    value={formData.nft_scanner_link}
                    onChange={(e) => handleChange('nft_scanner_link', e.target.value)}
                  />
                </Grid>
              )}
            </Grid>
          )}

          {activeStep === 5 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Additional Information & Notes
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Additional Tokens"
                  value={formData.additional_tokens}
                  onChange={(e) => handleChange('additional_tokens', e.target.value)}
                  placeholder="List any additional tokens not covered above"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Notes and Comments"
                  value={formData.notes_and_comments}
                  onChange={(e) => handleChange('notes_and_comments', e.target.value)}
                  placeholder="Any additional information about this project"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Additional Notes"
                  value={formData.additional_notes}
                  onChange={(e) => handleChange('additional_notes', e.target.value)}
                />
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }}>
                  <Chip label="Auto-Clicker & Telegram Bot (Optional)" />
                </Divider>
              </Grid>

              <Grid item xs={12}>
                <FormControl component="fieldset">
                  <FormLabel component="legend">Does this Auto-Clicker have an official Token?</FormLabel>
                  <RadioGroup
                    value={formData.autoclicker_has_token}
                    onChange={(e) => handleChange('autoclicker_has_token', e.target.value)}
                    row
                  >
                    <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
                    <FormControlLabel value="No" control={<Radio />} label="No" />
                    <FormControlLabel value="N/A" control={<Radio />} label="N/A" />
                  </RadioGroup>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Auto-Clicker Telegram Invite Link"
                  value={formData.autoclicker_telegram_invite}
                  onChange={(e) => handleChange('autoclicker_telegram_invite', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Auto-Clicker Telegram Bot Address"
                  value={formData.autoclicker_telegram_bot}
                  onChange={(e) => handleChange('autoclicker_telegram_bot', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Auto-Clicker TG Membership Population"
                  value={formData.autoclicker_membership_population}
                  onChange={(e) => handleChange('autoclicker_membership_population', e.target.value)}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Auto-Clicker Twitter (X) Page"
                  value={formData.autoclicker_twitter}
                  onChange={(e) => handleChange('autoclicker_twitter', e.target.value)}
                />
              </Grid>
            </Grid>
          )}
        </Box>

        {/* Navigation Buttons */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            disabled={activeStep === 0}
            onClick={handleBack}
          >
            Back
          </Button>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={loading || Object.keys(validationErrors).length > 0}
                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
              >
                {loading ? 'Submitting...' : 'Submit Game'}
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
                disabled={Object.keys(validationErrors).length > 0}
              >
                Next
              </Button>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default AddGameForm;
