import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  Button,
  Chip,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
  Box,
  Typography,
  Grid,
  CircularProgress,
  Link
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Storage as DatabaseIcon,
  TrendingUp as TrendingUpIcon,
  People as UsersIcon,
  MonetizationOn as CoinsIcon
} from '@mui/icons-material';

const GamingConfig = () => {
  const [projects, setProjects] = useState([]);
  const [summary, setSummary] = useState({});
  const [loading, setLoading] = useState(true);
  const [reloading, setReloading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [projectsRes, summaryRes] = await Promise.all([
        fetch('/api/gaming-config/projects'),
        fetch('/api/gaming-config/summary')
      ]);
      
      const projectsData = await projectsRes.json();
      const summaryData = await summaryRes.json();
      
      setProjects(projectsData.projects || []);
      setSummary(summaryData);
    } catch (error) {
      console.error('Failed to fetch gaming config data:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleProject = async (projectName, enabled) => {
    try {
      const response = await fetch('/api/gaming-config/projects/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_name: projectName,
          enabled: enabled
        })
      });

      if (response.ok) {
        // Update local state
        setProjects(projects.map(project => 
          project.project_name === projectName 
            ? { ...project, enabled: enabled }
            : project
        ));
        
        // Update summary
        setSummary(prev => ({
          ...prev,
          enabled_projects: enabled 
            ? prev.enabled_projects + 1 
            : prev.enabled_projects - 1
        }));
      }
    } catch (error) {
      console.error('Failed to toggle project:', error);
    }
  };

  const reloadConfiguration = async () => {
    try {
      setReloading(true);
      const response = await fetch('/api/gaming-config/reload', {
        method: 'POST'
      });
      
      if (response.ok) {
        await fetchData();
      }
    } catch (error) {
      console.error('Failed to reload configuration:', error);
    } finally {
      setReloading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'live':
        return 'bg-green-100 text-green-800';
      case 'beta - closer to its final form':
        return 'bg-blue-100 text-blue-800';
      case 'alpha':
        return 'bg-yellow-100 text-yellow-800';
      case 'development':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getBlockchainColor = (blockchain) => {
    switch (blockchain?.toLowerCase()) {
      case 'ethereum':
        return 'bg-purple-100 text-purple-800';
      case 'solana':
        return 'bg-green-100 text-green-800';
      case 'polygon':
        return 'bg-indigo-100 text-indigo-800';
      case 'bsc':
        return 'bg-yellow-100 text-yellow-800';
      case 'ronin':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Loading gaming configuration...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom>
            Gaming Configuration
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage gaming projects and tracking settings
          </Typography>
        </Box>
        <Button
          onClick={reloadConfiguration}
          disabled={reloading}
          variant="contained"
          startIcon={<RefreshIcon />}
          sx={{ minWidth: 150 }}
        >
          {reloading ? 'Reloading...' : 'Reload CSV Data'}
        </Button>
      </Box>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Overview" value="overview" />
          <Tab label="Projects" value="projects" />
          <Tab label="Tokens" value="tokens" />
          <Tab label="Analytics" value="analytics" />
        </Tabs>
      </Box>

      {activeTab === 'overview' && (
        <Box>
          {/* Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center">
                    <DatabaseIcon sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Total Projects
                      </Typography>
                      <Typography variant="h4" component="div">
                        {summary.total_projects || 0}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center">
                    <TrendingUpIcon sx={{ fontSize: 32, color: 'success.main', mr: 2 }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Enabled Projects
                      </Typography>
                      <Typography variant="h4" component="div">
                        {summary.enabled_projects || 0}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center">
                    <UsersIcon sx={{ fontSize: 32, color: 'secondary.main', mr: 2 }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Live Projects
                      </Typography>
                      <Typography variant="h4" component="div">
                        {summary.live_projects || 0}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center">
                    <CoinsIcon sx={{ fontSize: 32, color: 'warning.main', mr: 2 }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Total Tokens
                      </Typography>
                      <Typography variant="h4" component="div">
                        {summary.total_tokens || 0}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Blockchain Distribution */}
          <Card sx={{ mb: 3 }}>
            <CardHeader>
              <Typography variant="h6">Blockchain Distribution</Typography>
            </CardHeader>
            <CardContent>
              <Box display="flex" flexWrap="wrap" gap={1}>
                {summary.blockchains?.map((blockchain, index) => (
                  <Chip
                    key={index}
                    label={blockchain}
                    size="small"
                    sx={{ backgroundColor: getBlockchainColor(blockchain) }}
                  />
                ))}
              </Box>
            </CardContent>
          </Card>

          {/* Genre Distribution */}
          <Card>
            <CardHeader>
              <Typography variant="h6">Game Genres</Typography>
            </CardHeader>
            <CardContent>
              <Box display="flex" flexWrap="wrap" gap={1}>
                {summary.genres?.map((genre, index) => (
                  <Chip
                    key={index}
                    label={genre}
                    variant="outlined"
                    size="small"
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}

      {activeTab === 'projects' && (
        <Box>
          <Grid container spacing={2}>
            {projects.map((project, index) => (
              <Grid item xs={12} key={index}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Box flex={1}>
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <Typography variant="h6">{project.project_name}</Typography>
                          <Chip
                            label={project.status}
                            size="small"
                            sx={{ backgroundColor: getStatusColor(project.status) }}
                          />
                          <Chip
                            label={project.blockchain}
                            size="small"
                            sx={{ backgroundColor: getBlockchainColor(project.blockchain) }}
                          />
                          {project.genre && (
                            <Chip label={project.genre} variant="outlined" size="small" />
                          )}
                        </Box>

                        <Box sx={{ mt: 1 }}>
                          {project.website && (
                            <Typography variant="body2" color="text.secondary">
                              Website: <Link href={project.website} target="_blank" rel="noopener noreferrer">{project.website}</Link>
                            </Typography>
                          )}
                          {project.tokens?.length > 0 && (
                            <Typography variant="body2" color="text.secondary">
                              Tokens: {project.tokens.map(t => t.symbol).join(', ')}
                            </Typography>
                          )}
                          {project.daily_active_users && (
                            <Typography variant="body2" color="text.secondary">
                              DAU: {project.daily_active_users.toLocaleString()}
                            </Typography>
                          )}
                        </Box>
                      </Box>

                      <Box display="flex" alignItems="center" gap={1}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={project.enabled}
                              onChange={(e) => toggleProject(project.project_name, e.target.checked)}
                            />
                          }
                          label={project.enabled ? 'Enabled' : 'Disabled'}
                        />
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {activeTab === 'tokens' && (
        <Box>
          <Card>
            <CardHeader>
              <Typography variant="h6">Token Management</Typography>
            </CardHeader>
            <CardContent>
              <Typography variant="body1" color="text.secondary">
                Token configuration and contract management will be available here.
              </Typography>
            </CardContent>
          </Card>
        </Box>
      )}

      {activeTab === 'analytics' && (
        <Box>
          <Card>
            <CardHeader>
              <Typography variant="h6">Analytics Configuration</Typography>
            </CardHeader>
            <CardContent>
              <Typography variant="body1" color="text.secondary">
                Analytics settings and data source configuration will be available here.
              </Typography>
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default GamingConfig;
