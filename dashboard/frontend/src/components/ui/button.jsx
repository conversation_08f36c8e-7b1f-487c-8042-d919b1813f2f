import React from 'react';
import { Button as <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@mui/material';

export const Button = ({ children, className, variant = 'contained', ...props }) => {
  return (
    <MuiButton 
      variant={variant}
      className={className}
      sx={{ 
        textTransform: 'none',
        borderRadius: 2,
        ...props.sx 
      }}
      {...props}
    >
      {children}
    </MuiButton>
  );
};
