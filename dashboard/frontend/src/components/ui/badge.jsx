import React from 'react';
import { Chip } from '@mui/material';

export const Badge = ({ children, className, variant = 'filled', ...props }) => {
  const getVariantProps = () => {
    switch (variant) {
      case 'outline':
        return { variant: 'outlined' };
      case 'secondary':
        return { color: 'secondary' };
      default:
        return { variant: 'filled' };
    }
  };

  return (
    <Chip 
      label={children}
      size="small"
      className={className}
      sx={{ 
        borderRadius: 1,
        fontSize: '0.75rem',
        height: 'auto',
        '& .MuiChip-label': {
          padding: '2px 6px'
        },
        ...props.sx 
      }}
      {...getVariantProps()}
      {...props}
    />
  );
};
