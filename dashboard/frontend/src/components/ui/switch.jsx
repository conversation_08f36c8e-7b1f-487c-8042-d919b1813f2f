import React from 'react';
import { Switch as MuiSwitch, FormControlLabel } from '@mui/material';

export const Switch = ({ checked, onCheckedChange, label, className, ...props }) => {
  const switchComponent = (
    <MuiSwitch 
      checked={checked}
      onChange={(event) => onCheckedChange && onCheckedChange(event.target.checked)}
      className={className}
      sx={{ 
        ...props.sx 
      }}
      {...props}
    />
  );

  if (label) {
    return (
      <FormControlLabel
        control={switchComponent}
        label={label}
      />
    );
  }

  return switchComponent;
};
