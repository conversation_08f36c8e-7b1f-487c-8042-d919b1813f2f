import React from 'react';
import { Tabs as MuiTabs, Tab, Box } from '@mui/material';

export const Tabs = ({ value, onValueChange, children, className, ...props }) => {
  return (
    <Box className={className} {...props}>
      {children}
    </Box>
  );
};

export const TabsList = ({ children, className, ...props }) => {
  const [value, setValue] = React.useState(0);
  
  const handleChange = (event, newValue) => {
    setValue(newValue);
    // Find the parent Tabs component and call its onValueChange
    const tabsParent = event.target.closest('[data-tabs-parent]');
    if (tabsParent && tabsParent.onValueChange) {
      tabsParent.onValueChange(children[newValue].props.value);
    }
  };

  return (
    <MuiTabs 
      value={value}
      onChange={handleChange}
      className={className}
      sx={{ 
        borderBottom: 1, 
        borderColor: 'divider',
        '& .MuiTab-root': {
          textTransform: 'none',
          minWidth: 0,
          fontWeight: 500,
        },
        ...props.sx 
      }}
      {...props}
    >
      {React.Children.map(children, (child, index) => (
        <Tab label={child.props.children} />
      ))}
    </MuiTabs>
  );
};

export const TabsTrigger = ({ value, children, className, ...props }) => {
  // This is handled by TabsList
  return null;
};

export const TabsContent = ({ value, children, className, ...props }) => {
  return (
    <Box 
      className={className}
      sx={{ 
        paddingTop: 3,
        ...props.sx 
      }}
      {...props}
    >
      {children}
    </Box>
  );
};
