import React from 'react';
import { <PERSON> as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Typography } from '@mui/material';

export const Card = ({ children, className, ...props }) => {
  return (
    <MuiCard 
      className={className}
      sx={{ 
        backgroundColor: 'background.paper',
        borderRadius: 2,
        boxShadow: 3,
        ...props.sx 
      }}
      {...props}
    >
      {children}
    </MuiCard>
  );
};

export const CardHeader = ({ children, className, ...props }) => {
  return (
    <MuiCardHeader 
      className={className}
      sx={{ 
        paddingBottom: 1,
        ...props.sx 
      }}
      {...props}
    >
      {children}
    </MuiCardHeader>
  );
};

export const CardTitle = ({ children, className, ...props }) => {
  return (
    <Typography 
      variant="h6" 
      component="h3"
      className={className}
      sx={{ 
        fontWeight: 600,
        ...props.sx 
      }}
      {...props}
    >
      {children}
    </Typography>
  );
};

export const CardContent = ({ children, className, ...props }) => {
  return (
    <MuiCardContent 
      className={className}
      sx={{ 
        paddingTop: 0,
        '&:last-child': {
          paddingBottom: 2
        },
        ...props.sx 
      }}
      {...props}
    >
      {children}
    </MuiCardContent>
  );
};
