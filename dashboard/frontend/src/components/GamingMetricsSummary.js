import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Chip,
  LinearProgress,
  Alert,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  AccountBalanceWallet as WalletIcon,
  Token as TokenIcon,
  Collections as NFTIcon,
  SportsEsports as GamingIcon,
  HealthAndSafety as HealthIcon,
} from '@mui/icons-material';

function MetricCard({ title, value, icon, color = 'primary', subtitle, trend, loading = false }) {
  const getTrendIcon = () => {
    if (!trend) return null;
    return trend > 0 ? (
      <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16 }} />
    ) : (
      <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16 }} />
    );
  };

  const getTrendColor = () => {
    if (!trend) return 'text.secondary';
    return trend > 0 ? 'success.main' : 'error.main';
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              p: 1,
              borderRadius: 1,
              backgroundColor: `${color}.main`,
              color: 'white',
              mr: 2,
            }}
          >
            {icon}
          </Box>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>
        </Box>
        
        {loading ? (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CircularProgress size={20} />
            <Typography variant="body2" color="text.secondary">
              Loading...
            </Typography>
          </Box>
        ) : (
          <>
            <Typography variant="h4" component="div" sx={{ mb: 1 }}>
              {typeof value === 'number' ? value.toLocaleString() : value || 'N/A'}
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {subtitle && (
                <Typography variant="body2" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
              {trend && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {getTrendIcon()}
                  <Typography variant="body2" sx={{ color: getTrendColor() }}>
                    {Math.abs(trend).toFixed(1)}%
                  </Typography>
                </Box>
              )}
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );
}

function ProtocolHealthCard({ protocols, loading = false }) {
  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Protocol Health
          </Typography>
          <CircularProgress />
        </CardContent>
      </Card>
    );
  }

  if (!protocols || protocols.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Protocol Health
          </Typography>
          <Alert severity="info">No protocol data available</Alert>
        </CardContent>
      </Card>
    );
  }

  const healthyCount = protocols.filter(p => p.status === 'healthy').length;
  const healthPercentage = (healthyCount / protocols.length) * 100;

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Protocol Health
        </Typography>
        
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Healthy Protocols
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {healthyCount}/{protocols.length}
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={healthPercentage}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: 'grey.300',
              '& .MuiLinearProgress-bar': {
                backgroundColor: healthPercentage > 80 ? 'success.main' : 
                                healthPercentage > 60 ? 'warning.main' : 'error.main',
              },
            }}
          />
        </Box>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {protocols.map((protocol, index) => (
            <Chip
              key={protocol.name || index}
              label={protocol.name || `Protocol ${index + 1}`}
              size="small"
              color={protocol.status === 'healthy' ? 'success' : 'error'}
              variant="outlined"
            />
          ))}
        </Box>
      </CardContent>
    </Card>
  );
}

function GamingMetricsSummary({ 
  summary, 
  protocols, 
  health, 
  loading = false, 
  error = null 
}) {
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        Error loading gaming metrics: {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Main Metrics Grid */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Users"
            value={summary?.total_users}
            icon={<PeopleIcon />}
            color="primary"
            subtitle="Across all protocols"
            trend={summary?.user_growth_24h}
            loading={loading}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Market Cap"
            value={summary?.total_market_cap ? `$${(summary.total_market_cap / 1000000).toFixed(1)}M` : 'N/A'}
            icon={<TokenIcon />}
            color="success"
            subtitle="Gaming tokens"
            trend={summary?.market_cap_change_24h}
            loading={loading}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total TVL"
            value={summary?.total_tvl ? `$${(summary.total_tvl / 1000000).toFixed(1)}M` : 'N/A'}
            icon={<WalletIcon />}
            color="warning"
            subtitle="Value locked"
            trend={summary?.tvl_change_24h}
            loading={loading}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Active Protocols"
            value={health?.supported_protocols || protocols?.length}
            icon={<GamingIcon />}
            color="info"
            subtitle={health?.status === 'healthy' ? 'All operational' : 'Some issues'}
            loading={loading}
          />
        </Grid>
      </Grid>

      {/* Protocol Health */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <ProtocolHealthCard 
            protocols={protocols?.map(name => ({ name, status: 'healthy' })) || []}
            loading={loading}
          />
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Status
              </Typography>
              
              {health ? (
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <HealthIcon color={health.status === 'healthy' ? 'success' : 'error'} />
                    <Typography variant="h6" color={health.status === 'healthy' ? 'success.main' : 'error.main'}>
                      {health.status === 'healthy' ? 'Healthy' : 'Issues Detected'}
                    </Typography>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Cache Size: {health.cache_size || 0} protocols
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary">
                    Last Updated: {health.timestamp ? new Date(health.timestamp).toLocaleTimeString() : 'Unknown'}
                  </Typography>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  System status unavailable
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default GamingMetricsSummary;
