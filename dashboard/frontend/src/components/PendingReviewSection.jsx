import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Grid,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Snackbar,
  Badge,
  Tooltip,
  IconButton,
  Collapse
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Warning as WarningIcon,
  Security as SecurityIcon
} from '@mui/icons-material';

const PendingReviewSection = () => {
  const [pendingProjects, setPendingProjects] = useState([]);
  const [reviewStats, setReviewStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionType, setActionType] = useState('');
  const [adminNotes, setAdminNotes] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [expanded, setExpanded] = useState(true);

  useEffect(() => {
    fetchPendingProjects();
    fetchReviewStats();
    
    // Set up polling for real-time updates
    const interval = setInterval(() => {
      fetchPendingProjects();
      fetchReviewStats();
    }, 30000); // Poll every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchPendingProjects = async () => {
    try {
      const response = await fetch('/api/v1/gaming-review/pending');
      if (response.ok) {
        const data = await response.json();
        setPendingProjects(data);
      }
    } catch (error) {
      console.error('Error fetching pending projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchReviewStats = async () => {
    try {
      const response = await fetch('/api/v1/gaming-review/stats');
      if (response.ok) {
        const data = await response.json();
        setReviewStats(data);
      }
    } catch (error) {
      console.error('Error fetching review stats:', error);
    }
  };

  const handleAction = (project, action) => {
    setSelectedProject(project);
    setActionType(action);
    setAdminNotes('');
    setDialogOpen(true);
  };

  const confirmAction = async () => {
    try {
      const endpoint = actionType === 'approve' 
        ? `/api/v1/gaming-review/approve/${selectedProject.project_id}`
        : `/api/v1/gaming-review/reject/${selectedProject.project_id}`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ admin_notes: adminNotes })
      });

      if (response.ok) {
        const result = await response.json();
        setSnackbar({
          open: true,
          message: result.message,
          severity: 'success'
        });
        
        // Refresh the lists
        fetchPendingProjects();
        fetchReviewStats();
      } else {
        throw new Error('Action failed');
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: `Error ${actionType}ing project: ${error.message}`,
        severity: 'error'
      });
    }

    setDialogOpen(false);
    setSelectedProject(null);
  };

  const getConfidenceColor = (confidence) => {
    switch (confidence) {
      case 'HIGH': return 'success';
      case 'MEDIUM': return 'warning';
      default: return 'default';
    }
  };

  const needsAttention = (project) => {
    return project.admin_notes && project.admin_notes.includes('ATTN: Admin');
  };

  if (loading) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography>Loading pending projects...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ mb: 4 }}>
      {/* Header with Stats */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={() => setExpanded(!expanded)}>
          {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </IconButton>
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          Pending Review
        </Typography>
        <Badge badgeContent={reviewStats.total_pending || 0} color="primary">
          <SecurityIcon />
        </Badge>
      </Box>

      <Collapse in={expanded}>
        {/* Stats Summary */}
        {reviewStats.total_pending > 0 && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Typography variant="body2">
                <strong>{reviewStats.total_pending}</strong> projects pending review
              </Typography>
              <Chip 
                label={`${reviewStats.high_confidence} High Confidence`} 
                color="success" 
                size="small" 
              />
              <Chip 
                label={`${reviewStats.medium_confidence} Medium Confidence`} 
                color="warning" 
                size="small" 
              />
              {reviewStats.needs_attention > 0 && (
                <Chip 
                  label={`${reviewStats.needs_attention} Need Attention`} 
                  color="error" 
                  size="small"
                  icon={<WarningIcon />}
                />
              )}
            </Box>
          </Alert>
        )}

        {/* Pending Projects Grid */}
        {pendingProjects.length === 0 ? (
          <Alert severity="success">
            <Typography>No projects pending review! 🎉</Typography>
          </Alert>
        ) : (
          <Grid container spacing={2}>
            {pendingProjects.map((project) => (
              <Grid item xs={12} md={6} lg={4} key={project.project_id}>
                <Card 
                  sx={{ 
                    height: '100%',
                    border: needsAttention(project) ? '2px solid #f44336' : '1px solid #e0e0e0'
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="h6" sx={{ flexGrow: 1 }}>
                        {project.project_name}
                      </Typography>
                      {needsAttention(project) && (
                        <Tooltip title="Needs Admin Attention">
                          <WarningIcon color="error" />
                        </Tooltip>
                      )}
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      {project.blockchain} • {project.contract_address.slice(0, 8)}...
                    </Typography>
                    
                    <Box sx={{ mb: 2 }}>
                      <Chip 
                        label={`${project.confidence} Confidence`}
                        color={getConfidenceColor(project.confidence)}
                        size="small"
                        sx={{ mr: 1 }}
                      />
                      <Chip 
                        label={`${project.detected_patterns.length} Patterns`}
                        variant="outlined"
                        size="small"
                      />
                    </Box>

                    {project.admin_notes && (
                      <Alert severity="warning" sx={{ mb: 2 }}>
                        <Typography variant="caption">
                          {project.admin_notes}
                        </Typography>
                      </Alert>
                    )}

                    <Typography variant="caption" color="text.secondary">
                      Detected: {new Date(project.created_at).toLocaleDateString()}
                    </Typography>
                  </CardContent>
                  
                  <CardActions>
                    <Button
                      size="small"
                      startIcon={<ApproveIcon />}
                      color="success"
                      onClick={() => handleAction(project, 'approve')}
                    >
                      Approve
                    </Button>
                    <Button
                      size="small"
                      startIcon={<RejectIcon />}
                      color="error"
                      onClick={() => handleAction(project, 'reject')}
                    >
                      Reject
                    </Button>
                    <Button
                      size="small"
                      startIcon={<ViewIcon />}
                      onClick={() => {
                        // TODO: Open project details modal
                        console.log('View project details:', project);
                      }}
                    >
                      Details
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Collapse>

      {/* Action Confirmation Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {actionType === 'approve' ? 'Approve' : 'Reject'} Gaming Project
        </DialogTitle>
        <DialogContent>
          {selectedProject && (
            <Box>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Are you sure you want to {actionType} "{selectedProject.project_name}"?
              </Typography>
              
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Admin Notes (Optional)"
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                placeholder={`Add notes about this ${actionType} decision...`}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={confirmAction}
            color={actionType === 'approve' ? 'success' : 'error'}
            variant="contained"
          >
            {actionType === 'approve' ? 'Approve' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })} 
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PendingReviewSection;
