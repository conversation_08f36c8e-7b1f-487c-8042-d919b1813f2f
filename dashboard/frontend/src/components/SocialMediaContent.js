import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Link,
  Tooltip,
  Badge,
  Divider,
  FormControl,
  Select,
  MenuItem,
  InputLabel
} from '@mui/material';
import {
  Twitter as TwitterIcon,
  Reddit as RedditIcon,
  ThumbUp as ThumbUpIcon,
  Repeat as RetweetIcon,
  ChatBubbleOutline as ReplyIcon,
  Launch as LaunchIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material';
import { socialMediaAPI } from '../services/api';

const SocialMediaContent = ({ filters = {} }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [twitterPosts, setTwitterPosts] = useState([]);
  const [redditPosts, setRedditPosts] = useState([]);
  const [summary, setSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [sortBy, setSortBy] = useState('newest'); // 'newest' or 'hottest'

  useEffect(() => {
    loadSocialMediaData();
  }, [filters, sortBy]);

  const sortPosts = (posts, platform) => {
    if (sortBy === 'newest') {
      return [...posts].sort((a, b) => new Date(b.created_at || b.created_utc) - new Date(a.created_at || a.created_utc));
    } else if (sortBy === 'hottest') {
      if (platform === 'twitter') {
        return [...posts].sort((a, b) => {
          const aEngagement = (a.engagement?.likes || 0) + (a.engagement?.retweets || 0) + (a.engagement?.replies || 0);
          const bEngagement = (b.engagement?.likes || 0) + (b.engagement?.retweets || 0) + (b.engagement?.replies || 0);
          return bEngagement - aEngagement;
        });
      } else if (platform === 'reddit') {
        return [...posts].sort((a, b) => {
          const aEngagement = (a.engagement?.score || 0) + (a.engagement?.num_comments || 0);
          const bEngagement = (b.engagement?.score || 0) + (b.engagement?.num_comments || 0);
          return bEngagement - aEngagement;
        });
      }
    }
    return posts;
  };

  const loadSocialMediaData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load summary data
      const summaryResponse = await socialMediaAPI.getSummary(24);
      setSummary(summaryResponse.data);

      // Load Twitter posts
      const twitterResponse = await socialMediaAPI.getTwitterPosts(50, 24, 0, true);
      const sortedTwitterPosts = sortPosts(twitterResponse.data.posts, 'twitter');
      setTwitterPosts(sortedTwitterPosts);

      // Load Reddit posts
      const redditResponse = await socialMediaAPI.getRedditPosts(50, 24, 5, true);
      const sortedRedditPosts = sortPosts(redditResponse.data.posts, 'reddit');
      setRedditPosts(sortedRedditPosts);

    } catch (err) {
      console.error('Error loading social media data:', err);
      setError('Failed to load social media content');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadSocialMediaData();
    setRefreshing(false);
  };

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const getSentimentColor = (score) => {
    if (score > 0.3) return 'success';
    if (score < -0.3) return 'error';
    return 'warning';
  };

  const getSentimentIcon = (score) => {
    if (score > 0.3) return <TrendingUpIcon fontSize="small" />;
    if (score < -0.3) return <TrendingDownIcon fontSize="small" />;
    return null;
  };

  const TwitterPostCard = ({ post }) => (
    <Card sx={{ mb: 2, '&:hover': { boxShadow: 3 } }}>
      <CardContent>
        <Box display="flex" alignItems="flex-start" gap={2}>
          <Avatar sx={{ bgcolor: '#1DA1F2' }}>
            <TwitterIcon />
          </Avatar>
          <Box flex={1}>
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <Typography variant="subtitle2" fontWeight="bold">
                {post.author_name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                @{post.author_username}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {formatTimeAgo(post.created_at)}
              </Typography>
              {post.sentiment_score && (
                <Chip
                  size="small"
                  icon={getSentimentIcon(post.sentiment_score)}
                  label={`${(post.sentiment_score * 100).toFixed(0)}%`}
                  color={getSentimentColor(post.sentiment_score)}
                  variant="outlined"
                />
              )}
            </Box>
            
            <Typography variant="body2" paragraph>
              {post.text}
            </Typography>

            {/* Gaming Projects */}
            {post.gaming_projects && post.gaming_projects.length > 0 && (
              <Box mb={1}>
                {post.gaming_projects.map((project, index) => (
                  <Chip
                    key={index}
                    label={project}
                    size="small"
                    color="primary"
                    variant="outlined"
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                ))}
              </Box>
            )}

            {/* Hashtags */}
            {post.hashtags && post.hashtags.length > 0 && (
              <Box mb={1}>
                {post.hashtags.map((hashtag, index) => (
                  <Chip
                    key={index}
                    label={`#${hashtag}`}
                    size="small"
                    color="secondary"
                    variant="outlined"
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                ))}
              </Box>
            )}

            {/* Engagement metrics */}
            <Box display="flex" alignItems="center" gap={2} mt={1}>
              <Box display="flex" alignItems="center" gap={0.5}>
                <ThumbUpIcon fontSize="small" color="action" />
                <Typography variant="caption">{post.engagement.likes}</Typography>
              </Box>
              <Box display="flex" alignItems="center" gap={0.5}>
                <RetweetIcon fontSize="small" color="action" />
                <Typography variant="caption">{post.engagement.retweets}</Typography>
              </Box>
              <Box display="flex" alignItems="center" gap={0.5}>
                <ReplyIcon fontSize="small" color="action" />
                <Typography variant="caption">{post.engagement.replies}</Typography>
              </Box>
              {post.url && (
                <IconButton
                  size="small"
                  component={Link}
                  href={post.url}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <LaunchIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  const RedditPostCard = ({ post }) => (
    <Card sx={{ mb: 2, '&:hover': { boxShadow: 3 } }}>
      <CardContent>
        <Box display="flex" alignItems="flex-start" gap={2}>
          <Avatar sx={{ bgcolor: '#FF4500' }}>
            <RedditIcon />
          </Avatar>
          <Box flex={1}>
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <Typography variant="subtitle2" fontWeight="bold">
                r/{post.subreddit}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                u/{post.author}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {formatTimeAgo(post.created_utc)}
              </Typography>
              {post.sentiment_score && (
                <Chip
                  size="small"
                  icon={getSentimentIcon(post.sentiment_score)}
                  label={`${(post.sentiment_score * 100).toFixed(0)}%`}
                  color={getSentimentColor(post.sentiment_score)}
                  variant="outlined"
                />
              )}
            </Box>
            
            <Typography variant="h6" gutterBottom>
              {post.title}
            </Typography>

            {post.selftext && (
              <Typography variant="body2" paragraph>
                {post.selftext.length > 300 
                  ? `${post.selftext.substring(0, 300)}...` 
                  : post.selftext
                }
              </Typography>
            )}

            {/* Gaming Projects */}
            {post.gaming_projects && post.gaming_projects.length > 0 && (
              <Box mb={1}>
                {post.gaming_projects.map((project, index) => (
                  <Chip
                    key={index}
                    label={project}
                    size="small"
                    color="primary"
                    variant="outlined"
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                ))}
              </Box>
            )}

            {/* Gaming Tokens */}
            {post.gaming_tokens && post.gaming_tokens.length > 0 && (
              <Box mb={1}>
                {post.gaming_tokens.map((token, index) => (
                  <Chip
                    key={index}
                    label={token}
                    size="small"
                    color="secondary"
                    variant="outlined"
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                ))}
              </Box>
            )}

            {/* Engagement metrics */}
            <Box display="flex" alignItems="center" gap={2} mt={1}>
              <Box display="flex" alignItems="center" gap={0.5}>
                <ThumbUpIcon fontSize="small" color="action" />
                <Typography variant="caption">{post.engagement.score}</Typography>
              </Box>
              <Box display="flex" alignItems="center" gap={0.5}>
                <ReplyIcon fontSize="small" color="action" />
                <Typography variant="caption">{post.engagement.num_comments}</Typography>
              </Box>
              {post.engagement.upvote_ratio && (
                <Typography variant="caption" color="text.secondary">
                  {Math.round(post.engagement.upvote_ratio * 100)}% upvoted
                </Typography>
              )}
              {post.permalink && (
                <IconButton
                  size="small"
                  component={Link}
                  href={post.permalink}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <LaunchIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Summary Cards */}
      {summary && (
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <TwitterIcon color="primary" />
                  <Typography variant="h6">{summary.recent_twitter_posts}</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Recent Twitter Posts
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <RedditIcon color="primary" />
                  <Typography variant="h6">{summary.recent_reddit_posts}</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Recent Reddit Posts
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6">
                  {summary.top_gaming_projects.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Trending Projects
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6">
                  {summary.top_gaming_influencers.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active Influencers
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Content Tabs */}
      <Card>
        <Box display="flex" justifyContent="space-between" alignItems="center" p={2}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab 
              label={
                <Badge badgeContent={twitterPosts.length} color="primary">
                  <Box display="flex" alignItems="center" gap={1}>
                    <TwitterIcon />
                    Twitter
                  </Box>
                </Badge>
              } 
            />
            <Tab 
              label={
                <Badge badgeContent={redditPosts.length} color="primary">
                  <Box display="flex" alignItems="center" gap={1}>
                    <RedditIcon />
                    Reddit
                  </Box>
                </Badge>
              } 
            />
          </Tabs>
          <Box display="flex" alignItems="center" gap={2}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Sort by</InputLabel>
              <Select
                value={sortBy}
                label="Sort by"
                onChange={(e) => setSortBy(e.target.value)}
              >
                <MenuItem value="newest">Newest</MenuItem>
                <MenuItem value="hottest">Hottest</MenuItem>
              </Select>
            </FormControl>
            <Tooltip title="Refresh content">
              <IconButton onClick={handleRefresh} disabled={refreshing}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        <Divider />
        <CardContent>
          {activeTab === 0 && (
            <Box>
              {twitterPosts.length === 0 ? (
                <Typography variant="body2" color="text.secondary" textAlign="center" py={4}>
                  No Twitter posts found
                </Typography>
              ) : (
                twitterPosts.map((post) => (
                  <TwitterPostCard key={post.id} post={post} />
                ))
              )}
            </Box>
          )}
          {activeTab === 1 && (
            <Box>
              {redditPosts.length === 0 ? (
                <Typography variant="body2" color="text.secondary" textAlign="center" py={4}>
                  No Reddit posts found
                </Typography>
              ) : (
                redditPosts.map((post) => (
                  <RedditPostCard key={post.id} post={post} />
                ))
              )}
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default SocialMediaContent;
